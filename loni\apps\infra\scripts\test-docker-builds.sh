#!/bin/bash
# Docker Build Testing Script for LONI Platform
# Tests frontend and backend builds with proper error reporting

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1" >&2
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

warn() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# Get script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
INFRA_DIR="$(cd "$SCRIPT_DIR/.." && pwd)"
PROJECT_ROOT="$(cd "$INFRA_DIR/../.." && pwd)"

log "Starting Docker build tests for LONI Platform..."
log "Infrastructure directory: $INFRA_DIR"
log "Project root: $PROJECT_ROOT"

# Change to infra directory
cd "$INFRA_DIR"

# Test 1: Validate Docker Compose configuration
test_docker_compose_config() {
    log "Testing Docker Compose configuration..."
    
    if docker-compose config --quiet; then
        success "Docker Compose configuration is valid"
        return 0
    else
        error "Docker Compose configuration has errors"
        docker-compose config
        return 1
    fi
}

# Test 2: Check Dockerfile stage names
test_dockerfile_stages() {
    log "Checking Dockerfile stage names..."
    
    # Check frontend Dockerfile
    local frontend_stages=$(grep "^FROM .* AS " ../frontend/Dockerfile | awk '{print $4}')
    log "Frontend stages: $frontend_stages"
    
    if echo "$frontend_stages" | grep -q "production"; then
        success "Frontend Dockerfile has 'production' stage"
    else
        error "Frontend Dockerfile missing 'production' stage"
        error "Available stages: $frontend_stages"
        return 1
    fi
    
    # Check backend Dockerfile
    local backend_stages=$(grep "^FROM .* AS " ../backend/Dockerfile | awk '{print $4}')
    log "Backend stages: $backend_stages"
    
    if echo "$backend_stages" | grep -q "production"; then
        success "Backend Dockerfile has 'production' stage"
    else
        error "Backend Dockerfile missing 'production' stage"
        error "Available stages: $backend_stages"
        return 1
    fi
}

# Test 3: Check build contexts
test_build_contexts() {
    log "Checking build contexts..."
    
    if [[ -f "../frontend/Dockerfile" ]]; then
        success "Frontend Dockerfile exists"
    else
        error "Frontend Dockerfile not found at ../frontend/Dockerfile"
        return 1
    fi
    
    if [[ -f "../frontend/package.json" ]]; then
        success "Frontend package.json exists"
    else
        error "Frontend package.json not found"
        return 1
    fi
    
    if [[ -f "../backend/Dockerfile" ]]; then
        success "Backend Dockerfile exists"
    else
        error "Backend Dockerfile not found at ../backend/Dockerfile"
        return 1
    fi
    
    if [[ -f "../backend/pyproject.toml" ]]; then
        success "Backend pyproject.toml exists"
    else
        error "Backend pyproject.toml not found"
        return 1
    fi
}

# Test 4: Build frontend image
test_frontend_build() {
    log "Testing frontend build..."
    
    log "Building frontend image with target 'production'..."
    if docker-compose build --no-cache frontend 2>&1 | tee /tmp/frontend-build.log; then
        success "Frontend build completed successfully"
        
        # Check if image was created
        if docker images | grep -q "loni-frontend"; then
            success "Frontend image 'loni-frontend' created"
        else
            warn "Frontend image may not have been tagged correctly"
        fi
        
        return 0
    else
        error "Frontend build failed"
        error "Build log:"
        cat /tmp/frontend-build.log
        return 1
    fi
}

# Test 5: Build backend image
test_backend_build() {
    log "Testing backend build..."
    
    log "Building backend image with target 'production'..."
    if docker-compose build --no-cache backend 2>&1 | tee /tmp/backend-build.log; then
        success "Backend build completed successfully"
        
        # Check if image was created
        if docker images | grep -q "loni-backend"; then
            success "Backend image 'loni-backend' created"
        else
            warn "Backend image may not have been tagged correctly"
        fi
        
        return 0
    else
        error "Backend build failed"
        error "Build log:"
        cat /tmp/backend-build.log
        return 1
    fi
}

# Test 6: Test both builds together
test_combined_build() {
    log "Testing combined frontend and backend build..."
    
    if docker-compose build --no-cache frontend backend 2>&1 | tee /tmp/combined-build.log; then
        success "Combined build completed successfully"
        return 0
    else
        error "Combined build failed"
        error "Build log:"
        cat /tmp/combined-build.log
        return 1
    fi
}

# Test 7: Verify image functionality
test_image_functionality() {
    log "Testing image functionality..."
    
    # Test frontend image
    log "Testing frontend image startup..."
    if timeout 30 docker run --rm -d --name test-frontend -p 3001:3000 loni-frontend:latest; then
        sleep 5
        if curl -f http://localhost:3001 > /dev/null 2>&1; then
            success "Frontend image responds to HTTP requests"
        else
            warn "Frontend image may not be responding (this is normal if no health endpoint exists)"
        fi
        docker stop test-frontend || true
    else
        warn "Frontend image test skipped (may require additional setup)"
    fi
    
    # Test backend image
    log "Testing backend image startup..."
    if timeout 30 docker run --rm -d --name test-backend -p 8001:8000 loni-backend:latest; then
        sleep 5
        if curl -f http://localhost:8001/health > /dev/null 2>&1; then
            success "Backend image responds to health checks"
        else
            warn "Backend image may not be responding (may require database connection)"
        fi
        docker stop test-backend || true
    else
        warn "Backend image test skipped (may require additional setup)"
    fi
}

# Cleanup function
cleanup() {
    log "Cleaning up test artifacts..."
    
    # Stop any running test containers
    docker stop test-frontend test-backend 2>/dev/null || true
    docker rm test-frontend test-backend 2>/dev/null || true
    
    # Remove build logs
    rm -f /tmp/frontend-build.log /tmp/backend-build.log /tmp/combined-build.log
    
    success "Cleanup completed"
}

# Main execution
main() {
    log "Starting Docker build test suite..."
    
    local tests=(
        "test_docker_compose_config"
        "test_dockerfile_stages"
        "test_build_contexts"
        "test_frontend_build"
        "test_backend_build"
        "test_combined_build"
        "test_image_functionality"
    )
    
    local passed=0
    local failed=0
    
    for test in "${tests[@]}"; do
        log "Running test: $test"
        if $test; then
            ((passed++))
        else
            ((failed++))
            error "Test failed: $test"
        fi
        echo
    done
    
    log "Test Summary:"
    success "Passed: $passed"
    if [[ $failed -gt 0 ]]; then
        error "Failed: $failed"
    else
        log "Failed: $failed"
    fi
    
    if [[ $failed -eq 0 ]]; then
        success "All Docker build tests passed! Images are ready for deployment."
        log "You can now run:"
        log "  docker-compose up -d --build"
    else
        warn "Some tests failed. Please check the errors above and fix the issues."
    fi
}

# Handle signals
trap cleanup SIGTERM SIGINT

# Run main function
main "$@"

# Cleanup on exit
cleanup
