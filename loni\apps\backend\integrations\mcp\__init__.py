"""
MCP (Model Context Protocol) integration package.

This package provides MCP protocol implementation for standardized
AI model communication following the MCP specification.
"""

from .client import MC<PERSON>lient
from .server import MCPServer
from .types import MCPRequest, MCPResponse, MCPTool, MCPResource

__all__ = [
    "MCPClient",
    "MCPServer", 
    "MCPRequest",
    "MCPResponse",
    "MCPTool",
    "MCPResource",
]
