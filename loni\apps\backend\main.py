"""
Main FastAPI application entry point for LONI AI Platform.

This module initializes the FastAPI application with all necessary middleware,
routers, and dependencies following SOLID principles.
"""

import asyncio
from contextlib import asynccontextmanager
from typing import AsyncGenerator

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from loguru import logger

from api.middleware.error_handler import ErrorHandlerMiddleware
from api.middleware.logging import LoggingMiddleware
from api.middleware.metrics import MetricsMiddleware
from api.routes import create_auth_router, create_user_router, create_chat_router, health_router
from core.config import get_settings
from core.database import DatabaseManager
from core.container import Container, set_container


@asynccontextmanager
async def lifespan(app: FastAPI) -> AsyncGenerator[None, None]:
    """
    Application lifespan manager.
    
    Handles startup and shutdown events for the FastAPI application.
    """
    # Startup
    logger.info("Starting LONI AI Platform Backend...")
    
    # Initialize database
    database_manager = DatabaseManager()
    await database_manager.initialize()
    
    # Initialize dependency container
    container = Container()
    await container.initialize()

    # Store in app state and set global container
    app.state.database_manager = database_manager
    app.state.container = container
    set_container(container)
    
    logger.info("LONI AI Platform Backend started successfully")
    
    yield
    
    # Shutdown
    logger.info("Shutting down LONI AI Platform Backend...")
    
    await container.cleanup()
    await database_manager.cleanup()
    
    logger.info("LONI AI Platform Backend shutdown complete")


def create_application() -> FastAPI:
    """
    Create and configure the FastAPI application.
    
    Returns:
        Configured FastAPI application instance
    """
    settings = get_settings()
    
    app = FastAPI(
        title="LONI AI Platform API",
        description="Modern AI application platform with intelligent model management",
        version="1.0.0",
        docs_url="/docs" if settings.debug else None,
        redoc_url="/redoc" if settings.debug else None,
        lifespan=lifespan,
    )
    
    # Configure CORS with security restrictions
    if settings.cors_origins:
        app.add_middleware(
            CORSMiddleware,
            allow_origins=settings.cors_origins,
            allow_credentials=True,
            allow_methods=["GET", "POST", "PUT", "DELETE", "PATCH", "OPTIONS"],
            allow_headers=[
                "Accept",
                "Accept-Language",
                "Content-Language",
                "Content-Type",
                "Authorization",
                "X-Requested-With",
                "X-CSRF-Token",
            ],
            expose_headers=["X-Total-Count", "X-Page-Count"],
        )
    
    # Add security middleware
    app.add_middleware(
        TrustedHostMiddleware,
        allowed_hosts=settings.allowed_hosts,
    )
    
    # Add custom middleware
    app.add_middleware(MetricsMiddleware)
    app.add_middleware(LoggingMiddleware)
    app.add_middleware(ErrorHandlerMiddleware)
    
    # Include routers
    app.include_router(health_router, tags=["Health"])
    app.include_router(create_auth_router(), prefix="/auth", tags=["Authentication"])
    app.include_router(create_user_router(), prefix="/users", tags=["Users"])
    app.include_router(create_chat_router(), prefix="/chat", tags=["Chat"])
    
    return app


# Create the application instance
app = create_application()


@app.get("/")
async def root() -> dict[str, str]:
    """Root endpoint providing API information."""
    return {
        "name": "LONI AI Platform API",
        "version": "1.0.0",
        "description": "Modern AI application platform with intelligent model management",
        "docs": "/docs",
    }


if __name__ == "__main__":
    import uvicorn
    
    settings = get_settings()
    
    uvicorn.run(
        "main:app",
        host=settings.host,
        port=settings.port,
        reload=settings.debug,
        log_level="debug" if settings.debug else "info",
    )