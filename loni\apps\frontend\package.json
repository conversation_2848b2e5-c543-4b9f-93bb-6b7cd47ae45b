{"name": "loni-frontend", "version": "1.0.0", "description": "LONI AI Platform Frontend", "private": true, "scripts": {"dev": "next dev --turbo", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "type-check": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build"}, "dependencies": {"next": "^14.0.3", "react": "^18.2.0", "react-dom": "^18.2.0", "@copilotkit/react-core": "^1.9.1", "@copilotkit/react-ui": "^1.9.1", "@copilotkit/react-textarea": "^1.9.1", "ag-grid-react": "^31.0.0", "ag-grid-community": "^31.0.0", "ag-grid-enterprise": "^31.0.0", "@radix-ui/react-accordion": "^1.1.2", "@radix-ui/react-alert-dialog": "^1.0.5", "@radix-ui/react-avatar": "^1.0.4", "@radix-ui/react-checkbox": "^1.0.4", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-form": "^0.0.3", "@radix-ui/react-hover-card": "^1.0.7", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-popover": "^1.0.7", "@radix-ui/react-progress": "^1.0.3", "@radix-ui/react-scroll-area": "^1.0.5", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-separator": "^1.0.3", "@radix-ui/react-switch": "^1.0.3", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-toast": "^1.1.5", "@radix-ui/react-tooltip": "^1.0.7", "@tanstack/react-query": "^5.8.4", "@tanstack/react-query-devtools": "^5.8.4", "tailwindcss": "^3.3.6", "autoprefixer": "^10.4.16", "postcss": "^8.4.32", "@tailwindcss/forms": "^0.5.7", "@tailwindcss/typography": "^0.5.10", "tailwindcss-animate": "^1.0.7", "tailwind-merge": "^2.0.0", "clsx": "^2.0.0", "class-variance-authority": "^0.7.0", "cmdk": "^0.2.0", "lucide-react": "^0.294.0", "react-hook-form": "^7.48.2", "@hookform/resolvers": "^3.3.2", "zod": "^3.22.4", "framer-motion": "^10.16.5", "react-intersection-observer": "^9.5.3", "date-fns": "^2.30.0", "react-hot-toast": "^2.4.1", "react-markdown": "^9.0.1", "remark-gfm": "^4.0.0", "react-syntax-highlighter": "^15.5.0", "zustand": "^4.4.7", "jotai": "^2.6.0", "socket.io-client": "^4.7.4", "axios": "^1.6.2"}, "devDependencies": {"@types/node": "^20.9.0", "@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "@types/react-syntax-highlighter": "^15.5.11", "typescript": "^5.2.2", "@typescript-eslint/eslint-plugin": "^6.12.0", "@typescript-eslint/parser": "^6.12.0", "eslint": "^8.54.0", "eslint-config-next": "^14.0.3", "eslint-config-prettier": "^9.0.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-tailwindcss": "^3.13.0", "prettier": "^3.1.0", "prettier-plugin-tailwindcss": "^0.5.7", "jest": "^29.7.0", "@testing-library/react": "^14.1.2", "@testing-library/jest-dom": "^6.1.5", "@testing-library/user-event": "^14.5.1", "jest-environment-jsdom": "^29.7.0", "@storybook/addon-essentials": "^7.6.3", "@storybook/addon-interactions": "^7.6.3", "@storybook/addon-links": "^7.6.3", "@storybook/blocks": "^7.6.3", "@storybook/nextjs": "^7.6.3", "@storybook/react": "^7.6.3", "@storybook/testing-library": "^0.2.2", "storybook": "^7.6.3"}, "engines": {"node": ">=18.0.0", "bun": ">=1.0.0"}, "packageManager": "bun@1.0.0"}