{"name": "send_slack_message_through_n8n", "description": "Use this tool to Send a message in Slack.", "color": "linear-gradient(rgb(25,248,134), rgb(46,226,32))", "iconSrc": "", "schema": "[{\"id\":0,\"property\":\"message\",\"description\":\"The message to send in Slack\",\"type\":\"string\",\"required\":true}]", "func": "/*\n* You can use any libraries imported in Flowise\n* You can use properties specified in Input Schema as variables. Ex: Property = userid, Variable = $userid\n* You can get default flow config: $flow.sessionId, $flow.chatId, $flow.chatflowId, $flow.input, $flow.state\n* You can get custom variables: $vars.<variable-name>\n* Must return a string value at the end of function\n*/\n\nconst fetch = require('node-fetch');\nconst url = 'Your n8n Webhook URL';\nconst options = {\n    method: 'POST',\n    headers: {\n        'Content-Type': 'application/json',\n        'Authorization': `Bearer ${$vars.headerauth}`\n    },\n    body: JSON.stringify({\n      'message': $message\n    })\n};\ntry {\n    const response = await fetch(url, options);\n    const text = await response.text();\n    return text;\n} catch (error) {\n    console.error(error);\n    return '';\n}"}