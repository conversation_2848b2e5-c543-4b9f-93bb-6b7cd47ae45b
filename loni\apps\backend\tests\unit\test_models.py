"""
Unit tests for database models.

This module tests the core database models and their functionality.
"""

import pytest
from datetime import datetime
from uuid import uuid4

from core.models.user import User, SubscriptionTier
from core.models.conversation import Conversation, Message, MessageRole, ConversationStatus
from core.models.document import Document, ProcessingStatus, DocumentType
from core.models.ai_model import AIModel, ModelProvider, ModelType, ModelStatus


class TestUserModel:
    """Test User model functionality."""
    
    def test_user_creation(self):
        """Test user creation with required fields."""
        user = User(
            email="<EMAIL>",
            hashed_password="hashed_password",
            name="Test User"
        )
        
        assert user.email == "<EMAIL>"
        assert user.name == "Test User"
        assert user.is_active is True
        assert user.is_superuser is False
        assert user.is_verified is False
        assert user.subscription_tier == SubscriptionTier.FREE
        assert user.api_quota_used == 0
        assert user.api_quota_limit == 1000
        assert user.login_count == 0
        assert user.beta_features_enabled is False
    
    def test_user_update_last_active(self):
        """Test updating last active timestamp."""
        user = User(
            email="<EMAIL>",
            hashed_password="hashed_password",
            name="Test User"
        )
        
        original_time = user.last_active_at
        user.update_last_active()
        
        assert user.last_active_at != original_time
        assert isinstance(user.last_active_at, datetime)
    
    def test_user_increment_login_count(self):
        """Test incrementing login count."""
        user = User(
            email="<EMAIL>",
            hashed_password="hashed_password",
            name="Test User"
        )
        
        original_count = user.login_count
        user.increment_login_count()
        
        assert user.login_count == original_count + 1
    
    def test_user_quota_management(self):
        """Test API quota management."""
        user = User(
            email="<EMAIL>",
            hashed_password="hashed_password",
            name="Test User"
        )
        
        # Test quota usage
        assert user.can_use_api(100) is True
        user.use_api_quota(100)
        assert user.api_quota_used == 100
        
        # Test quota limit
        assert user.can_use_api(1000) is False
        
        # Test quota reset
        user.reset_api_quota()
        assert user.api_quota_used == 0
    
    def test_user_subscription_upgrade(self):
        """Test subscription tier upgrade."""
        user = User(
            email="<EMAIL>",
            hashed_password="hashed_password",
            name="Test User"
        )
        
        user.upgrade_subscription(SubscriptionTier.PRO)
        assert user.subscription_tier == SubscriptionTier.PRO
        assert user.api_quota_limit == 10000
    
    def test_user_display_name(self):
        """Test display name property."""
        user = User(
            email="<EMAIL>",
            hashed_password="hashed_password",
            name="Test User"
        )
        
        assert user.display_name == "Test User"
    
    def test_user_to_dict(self):
        """Test user serialization to dictionary."""
        user = User(
            email="<EMAIL>",
            hashed_password="hashed_password",
            name="Test User"
        )
        
        user_dict = user.to_dict()
        
        expected_fields = [
            "id", "email", "name", "is_active", "is_superuser", "is_verified",
            "avatar_url", "bio", "preferences", "timezone", "language",
            "subscription_tier", "api_quota_used", "api_quota_limit",
            "last_active_at", "login_count", "beta_features_enabled",
            "created_at", "updated_at"
        ]
        
        for field in expected_fields:
            assert field in user_dict


class TestConversationModel:
    """Test Conversation model functionality."""
    
    def test_conversation_creation(self):
        """Test conversation creation."""
        user_id = uuid4()
        conversation = Conversation(
            title="Test Conversation",
            user_id=user_id,
            model_name="gpt-4"
        )
        
        assert conversation.title == "Test Conversation"
        assert conversation.user_id == user_id
        assert conversation.model_name == "gpt-4"
        assert conversation.temperature == 0.7
        assert conversation.rag_enabled is True
        assert conversation.message_count == 0
        assert conversation.total_tokens == 0
        assert conversation.status == ConversationStatus.ACTIVE
    
    def test_conversation_update_stats(self):
        """Test conversation statistics update."""
        user_id = uuid4()
        conversation = Conversation(
            title="Test Conversation",
            user_id=user_id,
            model_name="gpt-4"
        )
        
        conversation.update_stats(message_count_delta=1, tokens_delta=100)
        
        assert conversation.message_count == 1
        assert conversation.total_tokens == 100
        assert conversation.last_message_at is not None
    
    def test_conversation_archive(self):
        """Test conversation archiving."""
        user_id = uuid4()
        conversation = Conversation(
            title="Test Conversation",
            user_id=user_id,
            model_name="gpt-4"
        )
        
        conversation.archive()
        assert conversation.status == ConversationStatus.ARCHIVED
    
    def test_conversation_display_title(self):
        """Test conversation display title."""
        user_id = uuid4()
        conversation = Conversation(
            title="Test Conversation",
            user_id=user_id,
            model_name="gpt-4"
        )
        
        assert conversation.display_title == "Test Conversation"
        
        # Test with None title
        conversation.title = None
        assert conversation.id.hex[:8] in conversation.display_title
    
    def test_conversation_average_tokens(self):
        """Test average tokens per message calculation."""
        user_id = uuid4()
        conversation = Conversation(
            title="Test Conversation",
            user_id=user_id,
            model_name="gpt-4"
        )
        
        # No messages
        assert conversation.average_tokens_per_message == 0.0
        
        # With messages
        conversation.update_stats(message_count_delta=2, tokens_delta=200)
        assert conversation.average_tokens_per_message == 100.0


class TestMessageModel:
    """Test Message model functionality."""
    
    def test_message_creation(self):
        """Test message creation."""
        conversation_id = uuid4()
        message = Message(
            conversation_id=conversation_id,
            role=MessageRole.USER,
            content="Hello, how are you?"
        )
        
        assert message.conversation_id == conversation_id
        assert message.role == MessageRole.USER
        assert message.content == "Hello, how are you?"
        assert message.is_edited is False
        assert message.is_deleted is False
    
    def test_message_role_properties(self):
        """Test message role properties."""
        conversation_id = uuid4()
        
        user_message = Message(
            conversation_id=conversation_id,
            role=MessageRole.USER,
            content="User message"
        )
        
        assistant_message = Message(
            conversation_id=conversation_id,
            role=MessageRole.ASSISTANT,
            content="Assistant message"
        )
        
        system_message = Message(
            conversation_id=conversation_id,
            role=MessageRole.SYSTEM,
            content="System message"
        )
        
        assert user_message.is_user_message is True
        assert user_message.is_assistant_message is False
        assert user_message.is_system_message is False
        
        assert assistant_message.is_user_message is False
        assert assistant_message.is_assistant_message is True
        assert assistant_message.is_system_message is False
        
        assert system_message.is_user_message is False
        assert system_message.is_assistant_message is False
        assert system_message.is_system_message is True
    
    def test_message_edit_and_delete(self):
        """Test message editing and deletion."""
        conversation_id = uuid4()
        message = Message(
            conversation_id=conversation_id,
            role=MessageRole.USER,
            content="Original content"
        )
        
        # Test editing
        message.mark_edited()
        assert message.is_edited is True
        
        # Test soft deletion
        message.soft_delete()
        assert message.is_deleted is True
        
        # Test restoration
        message.restore()
        assert message.is_deleted is False
    
    def test_message_content_length(self):
        """Test message content length property."""
        conversation_id = uuid4()
        message = Message(
            conversation_id=conversation_id,
            role=MessageRole.USER,
            content="Hello, world!"
        )
        
        assert message.content_length == len("Hello, world!")


class TestDocumentModel:
    """Test Document model functionality."""
    
    def test_document_creation(self):
        """Test document creation."""
        user_id = uuid4()
        document = Document(
            title="Test Document",
            content="This is test content.",
            user_id=user_id
        )
        
        assert document.title == "Test Document"
        assert document.content == "This is test content."
        assert document.user_id == user_id
        assert document.processing_status == ProcessingStatus.PENDING
        assert document.chunk_count == 0
    
    def test_document_processing_status(self):
        """Test document processing status management."""
        user_id = uuid4()
        document = Document(
            title="Test Document",
            content="This is test content.",
            user_id=user_id
        )
        
        # Test processing
        document.mark_processing()
        assert document.processing_status == ProcessingStatus.PROCESSING
        assert document.is_processing is True
        
        # Test completion
        document.mark_completed(chunk_count=5)
        assert document.processing_status == ProcessingStatus.COMPLETED
        assert document.chunk_count == 5
        assert document.is_processed is True
        
        # Test failure
        document.mark_failed("Test error")
        assert document.processing_status == ProcessingStatus.FAILED
        assert document.has_failed is True
        assert document.get_processing_error() == "Test error"
    
    def test_document_type_inference(self):
        """Test document type inference from content type."""
        user_id = uuid4()
        
        # PDF document
        pdf_doc = Document(
            title="PDF Document",
            content="PDF content",
            content_type="application/pdf",
            user_id=user_id
        )
        assert pdf_doc.document_type == DocumentType.PDF
        
        # Text document
        text_doc = Document(
            title="Text Document",
            content="Text content",
            content_type="text/plain",
            user_id=user_id
        )
        assert text_doc.document_type == DocumentType.TEXT
    
    def test_document_tags(self):
        """Test document tag management."""
        user_id = uuid4()
        document = Document(
            title="Test Document",
            content="This is test content.",
            user_id=user_id
        )
        
        # Add tags
        document.add_tag("important")
        document.add_tag("research")
        
        tags = document.get_tags()
        assert "important" in tags
        assert "research" in tags
        
        # Remove tag
        document.remove_tag("important")
        tags = document.get_tags()
        assert "important" not in tags
        assert "research" in tags


class TestAIModelModel:
    """Test AIModel model functionality."""
    
    def test_ai_model_creation(self):
        """Test AI model creation."""
        model = AIModel(
            name="gpt-4",
            provider=ModelProvider.OPENAI,
            model_type=ModelType.CHAT
        )
        
        assert model.name == "gpt-4"
        assert model.provider == ModelProvider.OPENAI
        assert model.model_type == ModelType.CHAT
        assert model.status == ModelStatus.AVAILABLE
        assert model.is_enabled is True
        assert model.usage_count == 0
    
    def test_ai_model_capabilities(self):
        """Test AI model capabilities management."""
        model = AIModel(
            name="gpt-4",
            provider=ModelProvider.OPENAI,
            model_type=ModelType.CHAT,
            capabilities={"streaming": True, "function_calling": True}
        )
        
        assert model.supports_streaming is True
        assert model.supports_function_calling is True
        assert model.supports_vision is False
        
        # Add capability
        model.add_capability("vision", True)
        assert model.get_capability("vision") is True
        
        # Remove capability
        model.remove_capability("streaming")
        assert model.get_capability("streaming") is False
    
    def test_ai_model_usage_tracking(self):
        """Test AI model usage tracking."""
        model = AIModel(
            name="gpt-4",
            provider=ModelProvider.OPENAI,
            model_type=ModelType.CHAT
        )
        
        original_count = model.usage_count
        model.increment_usage()
        
        assert model.usage_count == original_count + 1
        assert model.last_used_at is not None
    
    def test_ai_model_status_management(self):
        """Test AI model status management."""
        model = AIModel(
            name="llama3.2",
            provider=ModelProvider.OLLAMA,
            model_type=ModelType.CHAT
        )
        
        # Test downloading
        model.mark_downloading()
        assert model.status == ModelStatus.DOWNLOADING
        
        # Test downloaded
        model.mark_downloaded()
        assert model.status == ModelStatus.DOWNLOADED
        assert model.is_available is True
        
        # Test error
        model.mark_error("Download failed")
        assert model.status == ModelStatus.ERROR
        assert model.is_available is False
    
    def test_ai_model_properties(self):
        """Test AI model properties."""
        model = AIModel(
            name="gpt-4",
            display_name="GPT-4 Turbo",
            provider=ModelProvider.OPENAI,
            model_type=ModelType.CHAT
        )
        
        assert model.full_name == "openai:gpt-4"
        assert model.display_title == "GPT-4 Turbo"
        
        # Test without display name
        model.display_name = None
        assert model.display_title == "gpt-4"
