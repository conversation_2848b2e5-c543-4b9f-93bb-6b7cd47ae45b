/**
 * TypeScript type definitions for the LONI platform
 */

// Base types
export interface User {
  id: string
  email: string
  name: string
  is_active: boolean
  is_verified: boolean
  is_superuser: boolean
  api_quota_limit: number
  api_quota_used: number
}

export interface UserProfile extends User {
  quota_status: QuotaStatus
}

export interface QuotaStatus {
  used: number
  limit: number
  remaining: number
  percentage_used: number
}

// Authentication types
export interface LoginRequest {
  username: string
  password: string
}

export interface RegisterRequest {
  email: string
  password: string
  name: string
}

export interface AuthResponse {
  access_token: string
  token_type: string
}

// Conversation types
export interface Message {
  id: string
  conversation_id: string
  role: 'user' | 'assistant' | 'system'
  content: string
  model_name?: string
  tokens_used?: number
  created_at: string
}

export interface Conversation {
  id: string
  user_id: string
  title: string
  model_name: string
  rag_enabled: boolean
  temperature: number
  created_at: string
  updated_at: string
}

export interface ConversationWithMessages extends Conversation {
  messages: Message[]
}

export interface ConversationStats {
  conversation_id: string
  title: string
  model_name: string
  total_messages: number
  user_messages: number
  assistant_messages: number
  total_tokens: number
  created_at: string
  updated_at: string
  rag_enabled: boolean
  temperature: number
}

// Chat types
export interface ChatRequest {
  message: string
  conversation_id?: string
  model_name?: string
  stream?: boolean
  rag_enabled?: boolean
}

export interface ChatResponse {
  conversation_id: string
  user_message: Message
  assistant_message: Message
}

export interface ModelInfo {
  id: string
  name: string
  provider: string
  type: string
  context_length: number
  supports_streaming: boolean
}

// RAG types
export interface Document {
  id: string
  title: string
  source: string
  metadata: Record<string, any>
}

export interface DocumentDetail extends Document {
  content: string
}

export interface SearchResult {
  id: string
  score: number
  title: string
  content: string
  source: string
  metadata: Record<string, any>
}

export interface SearchRequest {
  query: string
  limit?: number
  score_threshold?: number
  filters?: Record<string, any>
}

export interface SearchResponse {
  query: string
  results: SearchResult[]
  total_results: number
  execution_time_ms: number
}

// UI State types
export interface AppState {
  user: User | null
  isAuthenticated: boolean
  isLoading: boolean
  currentConversation: Conversation | null
  conversations: Conversation[]
  messages: Message[]
  isGenerating: boolean
  sidebarOpen: boolean
  theme: 'light' | 'dark' | 'system'
}

export interface ChatState {
  messages: Message[]
  isGenerating: boolean
  currentModel: string
  ragEnabled: boolean
  temperature: number
  streamingMessage: string
  error: string | null
}

// Form types
export interface ConversationCreateForm {
  title?: string
  model_name?: string
  rag_enabled?: boolean
  temperature?: number
}

export interface ConversationUpdateForm {
  title?: string
  model_name?: string
  rag_enabled?: boolean
  temperature?: number
}

export interface UserUpdateForm {
  name?: string
  email?: string
}

export interface DocumentUploadForm {
  file: File
  title?: string
  metadata?: Record<string, any>
}

// API Response types
export interface ApiError {
  detail: string
  status?: number
}

export interface ApiResponse<T> {
  data?: T
  error?: ApiError
  loading?: boolean
}

// Component Props types
export interface ConversationItemProps {
  conversation: Conversation
  isActive?: boolean
  onClick?: () => void
  onDelete?: () => void
  onRename?: (newTitle: string) => void
}

export interface MessageItemProps {
  message: Message
  isGenerating?: boolean
  onRegenerate?: () => void
  onCopy?: () => void
}

export interface ChatInputProps {
  onSend: (message: string) => void
  isGenerating?: boolean
  placeholder?: string
  maxLength?: number
}

export interface ModelSelectorProps {
  models: ModelInfo[]
  selectedModel: string
  onModelChange: (modelId: string) => void
  disabled?: boolean
}

// Utility types
export type Nullable<T> = T | null
export type Optional<T> = T | undefined
export type ID = string

// Event types
export interface ConversationEvent {
  type: 'create' | 'update' | 'delete' | 'select'
  conversation: Conversation
}

export interface MessageEvent {
  type: 'send' | 'receive' | 'stream' | 'error'
  message?: Message
  content?: string
  error?: string
}

// Streaming types
export interface StreamChunk {
  content?: string
  done?: boolean
  conversation_id?: string
  error?: string
}

// Settings types
export interface UserSettings {
  theme: 'light' | 'dark' | 'system'
  defaultModel: string
  ragEnabled: boolean
  temperature: number
  autoSave: boolean
  notifications: boolean
}

// Export all as a module
export type {
  // Re-export common types for convenience
  User as UserType,
  Conversation as ConversationType,
  Message as MessageType,
  Document as DocumentType,
}