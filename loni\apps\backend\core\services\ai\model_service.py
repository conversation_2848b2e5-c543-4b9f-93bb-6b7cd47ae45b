"""
Model management service.

This service handles AI model information and capabilities
following the Single Responsibility Principle.
"""

from typing import Dict, List, Any

from ...config.application import get_settings


class ModelService:
    """Service for managing AI model information and capabilities."""
    
    def __init__(self):
        """Initialize the model service."""
        self.settings = get_settings()
        self._models_cache = None
    
    async def get_available_models(self) -> List[Dict[str, Any]]:
        """
        Get list of available AI models.
        
        Returns:
            List of model configurations
        """
        if self._models_cache is None:
            self._models_cache = self._build_models_list()
        
        return self._models_cache
    
    def _build_models_list(self) -> List[Dict[str, Any]]:
        """
        Build the list of available models based on configuration.
        
        Returns:
            List of model configurations
        """
        models = []
        
        # OpenAI models
        if self.settings.openai.api_key:
            models.extend([
                {
                    'id': 'gpt-4',
                    'name': 'GPT-4',
                    'provider': 'openai',
                    'type': 'chat',
                    'context_length': 8192,
                    'supports_streaming': True,
                    'cost_per_1k_tokens': {'input': 0.03, 'output': 0.06},
                    'capabilities': ['chat', 'code', 'analysis']
                },
                {
                    'id': 'gpt-4-turbo',
                    'name': 'GPT-4 Turbo',
                    'provider': 'openai',
                    'type': 'chat',
                    'context_length': 128000,
                    'supports_streaming': True,
                    'cost_per_1k_tokens': {'input': 0.01, 'output': 0.03},
                    'capabilities': ['chat', 'code', 'analysis', 'function_calling']
                },
                {
                    'id': 'gpt-3.5-turbo',
                    'name': 'GPT-3.5 Turbo',
                    'provider': 'openai',
                    'type': 'chat',
                    'context_length': 16385,
                    'supports_streaming': True,
                    'cost_per_1k_tokens': {'input': 0.001, 'output': 0.002},
                    'capabilities': ['chat', 'code']
                }
            ])
        
        # Anthropic models
        if self.settings.anthropic.api_key:
            models.extend([
                {
                    'id': 'claude-3-opus',
                    'name': 'Claude 3 Opus',
                    'provider': 'anthropic',
                    'type': 'chat',
                    'context_length': 200000,
                    'supports_streaming': True,
                    'cost_per_1k_tokens': {'input': 0.015, 'output': 0.075},
                    'capabilities': ['chat', 'code', 'analysis', 'reasoning']
                },
                {
                    'id': 'claude-3-sonnet',
                    'name': 'Claude 3 Sonnet',
                    'provider': 'anthropic',
                    'type': 'chat',
                    'context_length': 200000,
                    'supports_streaming': True,
                    'cost_per_1k_tokens': {'input': 0.003, 'output': 0.015},
                    'capabilities': ['chat', 'code', 'analysis']
                },
                {
                    'id': 'claude-3-haiku',
                    'name': 'Claude 3 Haiku',
                    'provider': 'anthropic',
                    'type': 'chat',
                    'context_length': 200000,
                    'supports_streaming': True,
                    'cost_per_1k_tokens': {'input': 0.00025, 'output': 0.00125},
                    'capabilities': ['chat', 'code']
                }
            ])
        
        # Ollama models (local)
        models.extend([
            {
                'id': 'llama3.2:latest',
                'name': 'Llama 3.2',
                'provider': 'ollama',
                'type': 'chat',
                'context_length': 8192,
                'supports_streaming': True,
                'cost_per_1k_tokens': {'input': 0, 'output': 0},
                'capabilities': ['chat', 'code'],
                'local': True
            },
            {
                'id': 'codellama:latest',
                'name': 'Code Llama',
                'provider': 'ollama',
                'type': 'chat',
                'context_length': 16384,
                'supports_streaming': True,
                'cost_per_1k_tokens': {'input': 0, 'output': 0},
                'capabilities': ['code'],
                'local': True
            }
        ])
        
        return models
    
    async def get_model_by_id(self, model_id: str) -> Dict[str, Any] | None:
        """
        Get model configuration by ID.
        
        Args:
            model_id: The model identifier
            
        Returns:
            Model configuration or None if not found
        """
        models = await self.get_available_models()
        return next((model for model in models if model['id'] == model_id), None)
    
    async def get_models_by_provider(self, provider: str) -> List[Dict[str, Any]]:
        """
        Get models filtered by provider.
        
        Args:
            provider: The provider name (openai, anthropic, ollama)
            
        Returns:
            List of models for the provider
        """
        models = await self.get_available_models()
        return [model for model in models if model['provider'] == provider]
    
    async def get_models_by_capability(self, capability: str) -> List[Dict[str, Any]]:
        """
        Get models filtered by capability.
        
        Args:
            capability: The capability (chat, code, analysis, etc.)
            
        Returns:
            List of models with the capability
        """
        models = await self.get_available_models()
        return [model for model in models if capability in model.get('capabilities', [])]
    
    def estimate_cost(self, model_id: str, input_tokens: int, output_tokens: int) -> float:
        """
        Estimate the cost for using a model.
        
        Args:
            model_id: The model identifier
            input_tokens: Number of input tokens
            output_tokens: Number of output tokens
            
        Returns:
            Estimated cost in USD
        """
        # This would need to be async in real implementation
        models = self._models_cache or self._build_models_list()
        model = next((model for model in models if model['id'] == model_id), None)
        
        if not model or not model.get('cost_per_1k_tokens'):
            return 0.0
        
        cost_config = model['cost_per_1k_tokens']
        input_cost = (input_tokens / 1000) * cost_config['input']
        output_cost = (output_tokens / 1000) * cost_config['output']
        
        return input_cost + output_cost
    
    def get_default_model(self, capability: str = 'chat') -> str:
        """
        Get the default model for a capability.
        
        Args:
            capability: The desired capability
            
        Returns:
            Default model ID
        """
        if capability == 'code':
            return 'gpt-4'
        elif capability == 'analysis':
            return 'claude-3-sonnet'
        else:
            return 'gpt-4'  # Default chat model