{"name": "AI Agent Mastery Prototype API Endpoint", "nodes": [{"parameters": {"options": {}}, "id": "eaee6a87-f260-44bc-998b-1f76bde7d8a1", "name": "OpenAI Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1, "position": [-820, 540], "credentials": {"openAiApi": {"id": "05Q6PbnSdyEcu9Ze", "name": "OpenAi account"}}}, {"parameters": {"jsonMode": "expressionData", "jsonData": "={{ $json.data || $json.text || $json.concatenated_data }}", "options": {"metadata": {"metadataValues": [{"name": "=file_id", "value": "={{ $('Set File ID').first().json.file_id }}"}, {"name": "file_title", "value": "={{ $('Set File ID').first().json.file_title }}"}, {"name": "file_url", "value": "={{ $('Set File ID').first().json.file_url }}"}]}}}, "id": "c1fd41f0-b88c-4240-95c3-1947cfd1e886", "name": "Default Data Loader", "type": "@n8n/n8n-nodes-langchain.documentDefaultDataLoader", "typeVersion": 1, "position": [720, 1600]}, {"parameters": {"model": "text-embedding-3-small", "options": {}}, "id": "6cf61c7f-a1d1-453f-b6ad-a321cdeb22d0", "name": "Embeddings OpenAI1", "type": "@n8n/n8n-nodes-langchain.embeddingsOpenAi", "typeVersion": 1, "position": [480, 1600], "credentials": {"openAiApi": {"id": "05Q6PbnSdyEcu9Ze", "name": "OpenAi account"}}}, {"parameters": {"content": "## Agent Tools for Agentic RAG", "height": 389, "width": 523, "color": 4}, "id": "923664c8-07c6-4306-b2ca-8a2f81803f31", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [460, 600]}, {"parameters": {"content": "## Tool to Add Google Drive Files to Vector DB", "height": 867, "width": 3493, "color": 5}, "id": "fc8f619a-dcf5-4d8a-bf9e-262e9666bd79", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-2340, 1000]}, {"parameters": {"operation": "download", "fileId": {"__rl": true, "value": "={{ $('Set File ID').item.json.file_id }}", "mode": "id"}, "options": {"googleFileConversion": {"conversion": {"docsToFormat": "text/plain"}}}}, "id": "0d0b3638-800b-47e1-bdbd-757e1d7a90b1", "name": "Download File", "type": "n8n-nodes-base.googleDrive", "typeVersion": 3, "position": [-860, 1280], "executeOnce": true, "credentials": {"googleDriveOAuth2Api": {"id": "p411dEzjKc4VbQGi", "name": "Google Drive account"}}}, {"parameters": {"pollTimes": {"item": [{"mode": "everyMinute"}]}, "triggerOn": "specificFolder", "folderToWatch": {"__rl": true, "value": "1HtaIIK3kWwjbwhsmEbtJ-upalxVn3Ek9", "mode": "list", "cachedResultName": "n8n Documents", "cachedResultUrl": "https://drive.google.com/drive/folders/1HtaIIK3kWwjbwhsmEbtJ-upalxVn3Ek9"}, "event": "fileCreated", "options": {}}, "id": "1623e8b2-edcc-47fb-923e-a0e95de6ca7b", "name": "File Created", "type": "n8n-nodes-base.googleDriveTrigger", "typeVersion": 1, "position": [-2260, 1120], "credentials": {"googleDriveOAuth2Api": {"id": "p411dEzjKc4VbQGi", "name": "Google Drive account"}}}, {"parameters": {"pollTimes": {"item": [{"mode": "everyMinute"}]}, "triggerOn": "specificFolder", "folderToWatch": {"__rl": true, "value": "1HtaIIK3kWwjbwhsmEbtJ-upalxVn3Ek9", "mode": "list", "cachedResultName": "n8n Documents", "cachedResultUrl": "https://drive.google.com/drive/folders/1HtaIIK3kWwjbwhsmEbtJ-upalxVn3Ek9"}, "event": "fileUpdated", "options": {}}, "id": "5bc15df1-1325-46b7-bc39-9f331b5a171c", "name": "File Updated", "type": "n8n-nodes-base.googleDriveTrigger", "typeVersion": 1, "position": [-2260, 1280], "credentials": {"googleDriveOAuth2Api": {"id": "p411dEzjKc4VbQGi", "name": "Google Drive account"}}}, {"parameters": {"operation": "text", "options": {}}, "id": "d815834b-d7da-4b0a-85aa-e16a00bd924b", "name": "Extract Document Text", "type": "n8n-nodes-base.extractFromFile", "typeVersion": 1, "position": [-80, 1600], "alwaysOutputData": true}, {"parameters": {"sessionIdType": "customKey", "sessionKey": "={{ $json.sessionId }}", "tableName": "messages"}, "id": "c6f48759-6f35-43d8-a433-3141f79122a7", "name": "Postgres Chat Memory", "type": "@n8n/n8n-nodes-langchain.memoryPostgresChat", "typeVersion": 1, "position": [-680, 580], "notesInFlow": false, "credentials": {"postgres": {"id": "PPVUOCGy8dTN3kdl", "name": "Live Agent Studio Test"}}}, {"parameters": {"operation": "delete", "tableId": "documents", "filterType": "string", "filterString": "=metadata->>file_id=like.*{{ $json.file_id }}*"}, "id": "b1c86377-961b-41f6-bd2a-cb486cbdcc77", "name": "Delete Old Doc Rows", "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [-1540, 1120], "alwaysOutputData": true, "credentials": {"supabaseApi": {"id": "Eu6anmgplJHxqZu2", "name": "Studio Test"}}}, {"parameters": {"assignments": {"assignments": [{"id": "10646eae-ae46-4327-a4dc-9987c2d76173", "name": "file_id", "value": "={{ $json.id }}", "type": "string"}, {"id": "f4536df5-d0b1-4392-bf17-b8137fb31a44", "name": "file_type", "value": "={{ $json.mimeType }}", "type": "string"}, {"id": "77d782de-169d-4a46-8a8e-a3831c04d90f", "name": "file_title", "value": "={{ $json.name }}", "type": "string"}, {"id": "9bde4d7f-e4f3-4ebd-9338-dce1350f9eab", "name": "file_url", "value": "={{ $json.webViewLink }}", "type": "string"}]}, "options": {}}, "id": "800fb81d-a5c6-486e-afcb-a8af514d53c0", "name": "Set File ID", "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [-1760, 1260]}, {"parameters": {"content": "## RAG AI Agent with Chat Interface with API Endpoint", "height": 1165, "width": 2776}, "id": "f2b02f8a-8b1d-4414-9181-03e356081a2d", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-2340, -180]}, {"parameters": {"options": {}}, "id": "6be15467-b0bc-478f-80d1-1a194aab8450", "name": "Respond to Webhook", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1.1, "position": [280, 220]}, {"parameters": {"operation": "pdf", "options": {}}, "id": "9eee15ff-84d6-4e8c-8735-02ec4c8c802f", "name": "Extract PDF Text", "type": "n8n-nodes-base.extractFromFile", "typeVersion": 1, "position": [-80, 1040]}, {"parameters": {"aggregate": "aggregateAllItemData", "options": {}}, "id": "643de6e5-def2-4327-ba1a-ebba11db2369", "name": "Aggregate", "type": "n8n-nodes-base.aggregate", "typeVersion": 1, "position": [-40, 1220]}, {"parameters": {"fieldsToSummarize": {"values": [{"aggregation": "concatenate", "field": "data"}]}, "options": {}}, "id": "80b8f5b4-a5c5-4d94-b734-88ef5157eda3", "name": "Summarize", "type": "n8n-nodes-base.summarize", "typeVersion": 1, "position": [160, 1300]}, {"parameters": {"promptType": "define", "text": "={{ $json.chatInput }}", "options": {"systemMessage": "=You are an intelligent AI assistant with advanced research and analysis capabilities. You excel at retrieving, processing, and synthesizing information from diverse document types to provide accurate, comprehensive answers. You are intuitive, friendly, and proactive, always aiming to deliver the most relevant information while maintaining clarity and precision.\n\nGoal:\n\nYour goal is to provide accurate, relevant, and well-sourced information by utilizing your suite of tools. You aim to streamline the user's research process, offer insightful analysis, and ensure they receive reliable answers to their queries. You help users by delivering thoughtful, well-researched responses that save them time and enhance their understanding of complex topics.\n\nTool Instructions:\n\n- Always begin with Memory: Before doing anything, use the memory tool to fetch relevant memories. You prioritize using this tool first and you always use it if the answer needs to be personalized to the user in ANY way!\n\n- Document Retrieval Strategy:\nFor general information queries: Use RAG first. Then analyze individual documents if RAG is insufficient.\nFor numerical analysis or data queries: Use SQL on tabular data\n\n- Knowledge Boundaries: Explicitly acknowledge when you cannot find an answer in the available resources.\n\nFor the rest of the tools, use them as necessary based on their descriptions.\n\nOutput Format:\n\nStructure your responses to be clear, concise, and well-organized. Begin with a direct answer to the user's query when possible, followed by supporting information and your reasoning process.\n\nMisc Instructions:\n\n- Query Clarification:\nRequest clarification when queries are ambiguous - but check memories first because that might clarify things.\n\nData Analysis Best Practices:\n- Explain your analytical approach when executing code or SQL queries\nPresent numerical findings with appropriate context and units\n\n- Source Prioritization:\nPrioritize the most recent and authoritative documents when information varies\n\n- Transparency About Limitations:\nClearly state when information appears outdated or incomplete\nAcknowledge when web search might provide more current information than your document corpus"}}, "id": "07688fd4-6917-432c-9f82-62a4a2e75546", "name": "RAG AI Agent", "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.6, "position": [-540, 280]}, {"parameters": {"rules": {"values": [{"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 1}, "conditions": [{"leftValue": "={{ $('Set File ID').item.json.file_type }}", "rightValue": "application/pdf", "operator": {"type": "string", "operation": "equals"}}], "combinator": "and"}}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 1}, "conditions": [{"id": "2ae7faa7-a936-4621-a680-60c512163034", "leftValue": "={{ $('Set File ID').item.json.file_type }}", "rightValue": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 1}, "conditions": [{"id": "fc193b06-363b-4699-a97d-e5a850138b0e", "leftValue": "={{ $('Set File ID').item.json.file_type }}", "rightValue": "=application/vnd.google-apps.spreadsheet", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 1}, "conditions": [{"id": "b69f5605-0179-4b02-9a32-e34bb085f82d", "leftValue": "={{ $('Set File ID').item.json.file_type }}", "rightValue": "application/vnd.google-apps.document", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}}]}, "options": {"fallbackOutput": "extra"}}, "id": "cd523a16-eaba-4751-8b46-2af75bd0b212", "name": "Switch", "type": "n8n-nodes-base.switch", "typeVersion": 3, "position": [-620, 1260]}, {"parameters": {"mode": "insert", "tableName": {"__rl": true, "value": "documents", "mode": "list", "cachedResultName": "documents"}, "options": {"queryName": "match_documents"}}, "id": "7ef663db-37e4-4694-9ec9-18ae43fed997", "name": "Insert into Supabase Vectorstore", "type": "@n8n/n8n-nodes-langchain.vectorStoreSupabase", "typeVersion": 1, "position": [660, 1380], "credentials": {"supabaseApi": {"id": "Eu6anmgplJHxqZu2", "name": "Studio Test"}}}, {"parameters": {"operation": "xlsx", "options": {}}, "id": "7e7930c2-1ac0-427f-a2b9-b48f963e0cf1", "name": "Extract from Excel", "type": "n8n-nodes-base.extractFromFile", "typeVersion": 1, "position": [-260, 1220]}, {"parameters": {"assignments": {"assignments": [{"id": "f422e2e0-381c-46ea-8f38-3f58c501d8b9", "name": "schema", "value": "={{ $('Extract from Excel').isExecuted ? $('Extract from Excel').first().json.keys().toJsonString() : $('Extract from CSV').first().json.keys().toJsonString() }}", "type": "string"}, {"id": "bb07c71e-5b60-4795-864c-cc3845b6bc46", "name": "data", "value": "={{ $json.concatenated_data }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [660, 1160], "id": "af6dcf25-e56e-4b7d-bc7d-40f84551d6ca", "name": "<PERSON>"}, {"parameters": {"options": {}}, "type": "n8n-nodes-base.extractFromFile", "typeVersion": 1, "position": [-260, 1400], "id": "f895aced-438e-4ce2-a292-f611c0a5dd99", "name": "Extract from CSV"}, {"parameters": {"content": "## Run Each Node Once to Set Up Database Tables", "height": 540, "width": 640, "color": 3}, "type": "n8n-nodes-base.stickyNote", "position": [-3020, -180], "typeVersion": 1, "id": "1e507bc7-c7f0-4b48-9594-db25a3fe886b", "name": "Sticky Note3"}, {"parameters": {"operation": "execute<PERSON>uery", "query": "CREATE TABLE document_metadata (\n    id TEXT PRIMARY KEY,\n    title TEXT,\n    url TEXT,\n    created_at TIMESTAMP DEFAULT NOW(),\n    schema TEXT\n);", "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.5, "position": [-2920, 140], "id": "443a8418-6abb-4e33-b960-9201ca44bbdd", "name": "Create Document Metadata Table", "credentials": {"postgres": {"id": "PPVUOCGy8dTN3kdl", "name": "Live Agent Studio Test"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "CREATE TABLE document_rows (\n    id SERIAL PRIMARY KEY,\n    dataset_id TEXT REFERENCES document_metadata(id),\n    row_data JSONB  -- Store the actual row data\n);", "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.5, "position": [-2620, 140], "id": "2b966ce9-b329-4c4b-ab42-07897044fdc5", "name": "Create Document Rows Table (for Tabular Data)", "credentials": {"postgres": {"id": "PPVUOCGy8dTN3kdl", "name": "Live Agent Studio Test"}}}, {"parameters": {"descriptionType": "manual", "toolDescription": "Use this tool to fetch all available documents, including the table schema if the file is a CSV or Excel file.", "operation": "select", "schema": {"__rl": true, "mode": "list", "value": "public"}, "table": {"__rl": true, "value": "document_metadata", "mode": "list", "cachedResultName": "document_metadata"}, "returnAll": true, "options": {}}, "type": "n8n-nodes-base.postgresTool", "typeVersion": 2.5, "position": [500, 840], "id": "7fa19178-dcc5-42a3-b5a3-dba5e6ad6442", "name": "List Documents", "credentials": {"postgres": {"id": "PPVUOCGy8dTN3kdl", "name": "Live Agent Studio Test"}}}, {"parameters": {"descriptionType": "manual", "toolDescription": "Given a file ID, fetches the text from the document.", "operation": "execute<PERSON>uery", "query": "SELECT \n    string_agg(content, ' ') as document_text\nFROM documents\n  WHERE metadata->>'file_id' = $1\nGROUP BY metadata->>'file_id';", "options": {"queryReplacement": "={{ $fromAI('file_id') }}"}}, "type": "n8n-nodes-base.postgresTool", "typeVersion": 2.5, "position": [580, 720], "id": "b54bc0eb-4a25-4a73-9b20-f46b1e5a2f70", "name": "Get File Contents", "credentials": {"postgres": {"id": "PPVUOCGy8dTN3kdl", "name": "Live Agent Studio Test"}}}, {"parameters": {"descriptionType": "manual", "toolDescription": "Run a SQL query - use this to query from the document_rows table once you know the file ID you are querying. dataset_id is the file_id and you are always using the row_data for filtering, which is a jsonb field that has all the keys from the file schema given in the document_metadata table.\n\nExample query:\n\nSELECT AVG((row_data->>'revenue')::numeric)\nFROM document_rows\nWHERE dataset_id = '123';\n\nExample query 2:\n\nSELECT \n    row_data->>'category' as category,\n    SUM((row_data->>'sales')::numeric) as total_sales\nFROM dataset_rows\nWHERE dataset_id = '123'\nGROUP BY row_data->>'category';", "operation": "execute<PERSON>uery", "query": "{{ $fromAI('sql_query') }}", "options": {}}, "type": "n8n-nodes-base.postgresTool", "typeVersion": 2.5, "position": [680, 840], "id": "eca88c18-669c-443f-88fd-d830da385b96", "name": "Query Document Rows", "credentials": {"postgres": {"id": "PPVUOCGy8dTN3kdl", "name": "Live Agent Studio Test"}}}, {"parameters": {"options": {}}, "type": "@n8n/n8n-nodes-langchain.embeddingsOpenAi", "typeVersion": 1.2, "position": [840, 840], "id": "0f909b17-b747-45ac-9f7c-e308ba7675a2", "name": "Embeddings OpenAI2", "credentials": {"openAiApi": {"id": "05Q6PbnSdyEcu9Ze", "name": "OpenAi account"}}}, {"parameters": {"options": {"reset": false}}, "type": "n8n-nodes-base.splitInBatches", "typeVersion": 3, "position": [-1940, 1120], "id": "ab81fbea-f779-41f0-b8b7-af39de9f3ba7", "name": "Loop Over Items"}, {"parameters": {"operation": "execute<PERSON>uery", "query": "-- Enable the pgvector extension to work with embedding vectors\ncreate extension vector;\n\n-- Create a table to store your documents\ncreate table documents (\n  id bigserial primary key,\n  content text, -- corresponds to Document.pageContent\n  metadata jsonb, -- corresponds to Document.metadata\n  embedding vector(1536) -- 1536 works for OpenAI embeddings, change if needed\n);\n\n-- Create a function to search for documents\ncreate function match_documents (\n  query_embedding vector(1536),\n  match_count int default null,\n  filter jsonb DEFAULT '{}'\n) returns table (\n  id bigint,\n  content text,\n  metadata jsonb,\n  similarity float\n)\nlanguage plpgsql\nas $$\n#variable_conflict use_column\nbegin\n  return query\n  select\n    id,\n    content,\n    metadata,\n    1 - (documents.embedding <=> query_embedding) as similarity\n  from documents\n  where metadata @> filter\n  order by documents.embedding <=> query_embedding\n  limit match_count;\nend;\n$$;", "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.5, "position": [-2920, -80], "id": "709175ca-b2ac-4167-bf66-835ec0f556ca", "name": "Create Documents Table and Match Function", "credentials": {"postgres": {"id": "PPVUOCGy8dTN3kdl", "name": "Live Agent Studio Test"}}}, {"parameters": {"operation": "delete", "tableId": "document_rows", "filters": {"conditions": [{"keyName": "dataset_id", "condition": "eq", "keyValue": "={{ $('Set File ID').item.json.file_id }}"}]}}, "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [-1320, 1280], "id": "cb9a7018-f9c1-4224-87a9-8b87c29cb94c", "name": "Delete Old Data Rows", "alwaysOutputData": true, "executeOnce": true, "credentials": {"supabaseApi": {"id": "Eu6anmgplJHxqZu2", "name": "Studio Test"}}}, {"parameters": {"operation": "upsert", "schema": {"__rl": true, "mode": "list", "value": "public"}, "table": {"__rl": true, "value": "document_metadata", "mode": "list", "cachedResultName": "document_metadata"}, "columns": {"mappingMode": "defineBelow", "value": {"id": "={{ $('Set File ID').item.json.file_id }}", "title": "={{ $('Set File ID').item.json.file_title }}", "url": "={{ $('Set File ID').item.json.file_url }}"}, "matchingColumns": ["id"], "schema": [{"id": "id", "displayName": "id", "required": true, "defaultMatch": true, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "title", "displayName": "title", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": false}, {"id": "url", "displayName": "url", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": false, "removed": false}, {"id": "created_at", "displayName": "created_at", "required": false, "defaultMatch": false, "display": true, "type": "dateTime", "canBeUsedToMatch": false}, {"id": "schema", "displayName": "schema", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": false, "removed": true}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.5, "position": [-1080, 1140], "id": "6bbcca24-2e70-4b91-a2fc-0e983e83d8da", "name": "Insert Document Metadata", "executeOnce": true, "credentials": {"postgres": {"id": "PPVUOCGy8dTN3kdl", "name": "Live Agent Studio Test"}}}, {"parameters": {"schema": {"__rl": true, "mode": "list", "value": "public"}, "table": {"__rl": true, "value": "document_rows", "mode": "list", "cachedResultName": "document_rows"}, "columns": {"mappingMode": "defineBelow", "value": {"dataset_id": "={{ $('Set File ID').item.json.file_id }}", "row_data": "={{ $json.toJsonString().replaceAll(/'/g, \"''\") }}"}, "matchingColumns": ["id"], "schema": [{"id": "id", "displayName": "id", "required": false, "defaultMatch": true, "display": true, "type": "number", "canBeUsedToMatch": true, "removed": true}, {"id": "dataset_id", "displayName": "dataset_id", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "row_data", "displayName": "row_data", "required": false, "defaultMatch": false, "display": true, "type": "object", "canBeUsedToMatch": true}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.5, "position": [-40, 1400], "id": "224f7b32-fc1b-4977-acb9-cea47c212705", "name": "Insert Table Rows", "credentials": {"postgres": {"id": "PPVUOCGy8dTN3kdl", "name": "Live Agent Studio Test"}}}, {"parameters": {"operation": "upsert", "schema": {"__rl": true, "mode": "list", "value": "public"}, "table": {"__rl": true, "value": "document_metadata", "mode": "list", "cachedResultName": "document_metadata"}, "columns": {"mappingMode": "defineBelow", "value": {"id": "={{ $('Set File ID').item.json.file_id }}", "schema": "={{ $json.schema }}"}, "matchingColumns": ["id"], "schema": [{"id": "id", "displayName": "id", "required": true, "defaultMatch": true, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "title", "displayName": "title", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": false, "removed": true}, {"id": "url", "displayName": "url", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": false, "removed": true}, {"id": "created_at", "displayName": "created_at", "required": false, "defaultMatch": false, "display": true, "type": "dateTime", "canBeUsedToMatch": false}, {"id": "schema", "displayName": "schema", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": false, "removed": false}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.5, "position": [860, 1160], "id": "50028258-77e8-4313-9f20-7066056e5d45", "name": "Update <PERSON><PERSON>a for Document Metadata", "credentials": {"postgres": {"id": "PPVUOCGy8dTN3kdl", "name": "Live Agent Studio Test"}}}, {"parameters": {"chunkSize": 400, "options": {}}, "type": "@n8n/n8n-nodes-langchain.textSplitterRecursiveCharacterTextSplitter", "typeVersion": 1, "position": [620, 1720], "id": "29ed7bce-1457-4cfa-9b83-ff9b80b1afe2", "name": "Recursive Character Text Splitter"}, {"parameters": {"content": "## Save Long Term Memories", "height": 360, "width": 1600, "color": 6}, "type": "n8n-nodes-base.stickyNote", "position": [460, 220], "typeVersion": 1, "id": "002806a4-d595-4c33-a5fe-3ec63538239d", "name": "Sticky Note4"}, {"parameters": {"jsonSchemaExample": "{\n\t\"memories\": [\"Memory 1\", \"Memory 2\", \"Memory 3\"]\n}"}, "type": "@n8n/n8n-nodes-langchain.outputParserStructured", "typeVersion": 1.2, "position": [740, 440], "id": "41b25f02-8193-4ad0-be58-e7966b131f94", "name": "Structured Output Parser"}, {"parameters": {"operation": "execute<PERSON>uery", "query": "-- Create a table to store your documents\ncreate table memories (\n  id bigserial primary key,\n  content text, -- corresponds to Document.pageContent\n  metadata jsonb, -- corresponds to Document.metadata\n  embedding vector(1536) -- 1536 works for OpenAI embeddings, change if needed\n);\n\n-- Create a function to search for documents\ncreate function match_memories (\n  query_embedding vector(1536),\n  match_count int default null,\n  filter jsonb DEFAULT '{}'\n) returns table (\n  id bigint,\n  content text,\n  metadata jsonb,\n  similarity float\n)\nlanguage plpgsql\nas $$\n#variable_conflict use_column\nbegin\n  return query\n  select\n    id,\n    content,\n    metadata,\n    1 - (memories.embedding <=> query_embedding) as similarity\n  from memories\n  where metadata @> filter\n  order by memories.embedding <=> query_embedding\n  limit match_count;\nend;\n$$;", "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.5, "position": [-2620, -80], "id": "ce77ba8b-a198-4175-9a93-33036b2b1632", "name": "Create Memories Table and Match Function", "credentials": {"postgres": {"id": "PPVUOCGy8dTN3kdl", "name": "Live Agent Studio Test"}}}, {"parameters": {"options": {}}, "type": "@n8n/n8n-nodes-langchain.embeddingsOpenAi", "typeVersion": 1.2, "position": [1620, 820], "id": "2eacdc57-f5db-4afa-9f63-ef125e921bad", "name": "Embeddings OpenAI", "credentials": {"openAiApi": {"id": "05Q6PbnSdyEcu9Ze", "name": "OpenAi account"}}}, {"parameters": {"jsonMode": "expressionData", "jsonData": "={{ $('Basic LLM Chain').item.json.output.memories }}", "options": {"metadata": {"metadataValues": [{"name": "timestamp", "value": "={{ $now }}"}]}}}, "type": "@n8n/n8n-nodes-langchain.documentDefaultDataLoader", "typeVersion": 1, "position": [1720, 680], "id": "47b9410b-8464-4746-9dd5-2bad2684<PERSON><PERSON>", "name": "Default Data Loader1"}, {"parameters": {"chunkSize": 400}, "type": "@n8n/n8n-nodes-langchain.textSplitterCharacterTextSplitter", "typeVersion": 1, "position": [1820, 840], "id": "47fbc900-e567-4c28-8923-10fe4932a336", "name": "Character Text Splitter"}, {"parameters": {"assignments": {"assignments": [{"id": "636ddfda-13fd-4f21-ad30-fa21472ed9e7", "name": "output", "value": "={{ $('RAG AI Agent').item.json.output }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [1860, 420], "id": "54a04270-4749-4f6f-9994-7b2e6c3e7c21", "name": "Edit Fields1", "executeOnce": true}, {"parameters": {"options": {}}, "type": "@n8n/n8n-nodes-langchain.embeddingsOpenAi", "typeVersion": 1.2, "position": [-80, 780], "id": "7b496d7f-4867-42d2-8fcf-fd0e0b4c7bce", "name": "Embeddings OpenAI3", "credentials": {"openAiApi": {"id": "05Q6PbnSdyEcu9Ze", "name": "OpenAi account"}}}, {"parameters": {"mode": "retrieve-as-tool", "toolName": "memories", "toolDescription": "Use this tool to fetch memories from previous conversations if needed to answer a question for the user or continue the conversation.", "tableName": {"__rl": true, "value": "memories", "mode": "list", "cachedResultName": "memories"}, "options": {"queryName": "match_memories"}}, "type": "@n8n/n8n-nodes-langchain.vectorStoreSupabase", "typeVersion": 1, "position": [-220, 620], "id": "c8d60b9b-4d37-424b-995d-15a49e06175a", "name": "Retrieve Memories Tool", "credentials": {"supabaseApi": {"id": "Eu6anmgplJHxqZu2", "name": "Studio Test"}}}, {"parameters": {"mode": "retrieve-as-tool", "toolName": "documents", "toolDescription": "Use RAG to look up information in the knowledgebase.", "tableName": {"__rl": true, "value": "documents", "mode": "list", "cachedResultName": "documents"}, "options": {"queryName": "match_documents"}}, "type": "@n8n/n8n-nodes-langchain.vectorStoreSupabase", "typeVersion": 1, "position": [720, 700], "id": "cf6f5455-3dbd-4180-8217-5e0d0a509d9b", "name": "Document RAG Tool", "credentials": {"supabaseApi": {"id": "Eu6anmgplJHxqZu2", "name": "Studio Test"}}}, {"parameters": {"promptType": "define", "text": "=You are an expert at maintaining a memory database for a user, making sure that there aren't duplicate or contradictory memories.\n\nThe current memories being added to the database are:\n\n{{ $json.output.memories }}\n\nYour job is to:\n- Search the memory database to find any duplicate or contradictory memories. The tool will return memories from the search but you have to decide if they are duplicates or contradictory of the memories being added or not.\n- If you find any duplicate/contradictory memories, output a list of memories to delete (the content of the memory). Otherwise, if you find no memories to delete, output an empty list. You determine based on all the memories returned from the search if they are duplicates/contradictory. The memories you output have to match EXACTLY with the content received.", "hasOutputParser": true, "options": {}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.7, "position": [1020, 280], "id": "772d32ba-ea25-4f08-be63-5fab985e8245", "name": "Memory Agent"}, {"parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4o-mini"}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1.2, "position": [1080, 800], "id": "f431163f-dbf1-4d33-90be-6eeb90905619", "name": "OpenAI Chat Model2", "credentials": {"openAiApi": {"id": "05Q6PbnSdyEcu9Ze", "name": "OpenAi account"}}}, {"parameters": {"mode": "insert", "tableName": {"__rl": true, "value": "memories", "mode": "list", "cachedResultName": "memories"}, "options": {"queryName": "match_memories"}}, "type": "@n8n/n8n-nodes-langchain.vectorStoreSupabase", "typeVersion": 1, "position": [1560, 280], "id": "8d43ef48-1ba0-4826-9ed6-f7fd9a09444e", "name": "Supabase Vector Store", "executeOnce": true, "credentials": {"supabaseApi": {"id": "Eu6anmgplJHxqZu2", "name": "Studio Test"}}}, {"parameters": {"options": {}}, "type": "@n8n/n8n-nodes-langchain.embeddingsOpenAi", "typeVersion": 1.2, "position": [1300, 820], "id": "d108ffef-1f81-4bed-a648-fe2f2be93e54", "name": "Embeddings OpenAI4", "credentials": {"openAiApi": {"id": "05Q6PbnSdyEcu9Ze", "name": "OpenAi account"}}}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "cb436122-f430-4ea5-98d8-b99e8c52eeaa", "leftValue": "={{ $json.output.memories }}", "rightValue": "", "operator": {"type": "array", "operation": "notEmpty", "singleValue": true}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [840, 280], "id": "3dad8afd-dbcc-4b30-ae51-7bf81d2e4fad", "name": "If"}, {"parameters": {"content": "## Memory Searcher", "height": 380, "width": 520, "color": 6}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [1000, 600], "id": "7ea7a233-c4d2-4306-98da-0e18b718e553", "name": "Sticky Note5"}, {"parameters": {"promptType": "define", "text": "=You are an expert at extract key information from conversations to store as long term memories for RAG.\n\nThe user said:\n\n{{ $('Edit Fields').item.json.chatInput }}\n\nYour goal is to output a list of key memories to extract from the conversation.\n\nJust output a empty list if there is nothing worth saving, like if the user is just asking a basic question without providing additional context.", "hasOutputParser": true}, "type": "@n8n/n8n-nodes-langchain.chainLlm", "typeVersion": 1.5, "position": [500, 280], "id": "71d1500c-a72c-4d18-8aea-bb1c02f5908d", "name": "Basic LLM Chain"}, {"parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4o-mini"}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1.2, "position": [560, 440], "id": "b02165b2-8a79-4566-afb3-3d34925b242c", "name": "OpenAI Chat Model1", "credentials": {"openAiApi": {"id": "05Q6PbnSdyEcu9Ze", "name": "OpenAi account"}}}, {"parameters": {"content": "## Memory Chunking & Embedding", "height": 380, "width": 520, "color": 6}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [1540, 600], "id": "0e41846e-4c28-463e-b955-80a75556c42d", "name": "Sticky Note6"}, {"parameters": {"mode": "retrieve-as-tool", "toolName": "memories", "toolDescription": "Use this tool to retrieve memories from the database. You specifically want to use this to find similar memories to what you are planning on adding so that you can do what it takes to remove duplicates.", "tableName": {"__rl": true, "value": "memories", "mode": "list", "cachedResultName": "memories"}, "topK": 8, "options": {"queryName": "match_memories"}}, "type": "@n8n/n8n-nodes-langchain.vectorStoreSupabase", "typeVersion": 1, "position": [1180, 680], "id": "45b19fd3-0f18-4285-b151-8174e2080196", "name": "Similar Memory Searcher", "credentials": {"supabaseApi": {"id": "Eu6anmgplJHxqZu2", "name": "Studio Test"}}}, {"parameters": {"jsonSchemaExample": "[\"Memory 1\", \"Memory 2\", \"Memory 3\"]"}, "type": "@n8n/n8n-nodes-langchain.outputParserStructured", "typeVersion": 1.2, "position": [1320, 440], "id": "fdf6c62e-340b-4b74-9bcd-53590eed928b", "name": "Structured Output Parser1"}, {"parameters": {"operation": "execute<PERSON>uery", "query": "DELETE FROM memories\nWHERE content IN ({{ $json.output.concat($('Basic LLM Chain').item.json.output.memories).map(item => `'${item.replace(/'/g, \"''\")}'`).join(', ') || '__IMPOSSIBLE_VALUE__' }})", "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.5, "position": [1360, 280], "id": "232f7356-18c8-4e5d-b42c-da6e41cf85fa", "name": "Postgres", "credentials": {"postgres": {"id": "PPVUOCGy8dTN3kdl", "name": "Live Agent Studio Test"}}}, {"parameters": {"name": "execute_code", "description": "Call this tool to execute JavaScript code that you create with parameters that you specify too. All code must be a single line and must be JavaScript code.", "jsCode": "/**\n * Execute arbitrary JavaScript code and capture its console output\n * @param {string} codeString - JavaScript code to execute\n * @returns {string} The captured console output\n */\nfunction executeCode(codeString) {\n  // Save the original console.log function\n  const originalLog = console.log;\n  \n  // Create an array to store the output\n  const outputLines = [];\n  \n  // Override console.log to capture output\n  console.log = function(...args) {\n    // Convert all arguments to strings and join them\n    const output = args.map(arg => \n      typeof arg === 'object' ? JSON.stringify(arg) : String(arg)\n    ).join(' ');\n    \n    // Add to our captured output\n    outputLines.push(output);\n  };\n  \n  // Variable to store the result\n  let result;\n  \n  try {\n    // Execute the code\n    result = eval(codeString);\n  } catch (error) {\n    // Restore the original console.log\n    console.log = originalLog;\n    return `Error executing code: ${error.message}`;\n  } finally {\n    // Restore the original console.log\n    console.log = originalLog;\n  }\n  \n  // Join all captured output lines\n  const output = outputLines.join('\\n');\n  \n  // If there's a result but no console output, return the result\n  if (output === '' && result !== undefined) {\n    return String(result);\n  }\n  \n  return output;\n}\n\nreturn executeCode(query.code_to_execute);", "specifyInputSchema": true, "jsonSchemaExample": "{\n\t\"code_to_execute\": \"console.log('test')\"\n}"}, "type": "@n8n/n8n-nodes-langchain.toolCode", "typeVersion": 1.1, "position": [-320, 720], "id": "6072ab31-badf-4ec8-887b-b52cfc269a79", "name": "Code Tool"}, {"parameters": {"content": "## Workflow Agent Tools", "height": 380, "width": 1100, "color": 4}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [460, -180], "id": "c5e9894b-29b5-492c-89cc-91898072e823", "name": "Sticky Note7"}, {"parameters": {"name": "web_search", "description": "Call this tool to do an advanced web search based on a query you define.", "workflowId": {"__rl": true, "value": "={{ $workflow.id }}", "mode": "id"}, "workflowInputs": {"mappingMode": "defineBelow", "value": {"query": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('query', ``, 'string') }}", "tool_type": "web_search"}, "matchingColumns": [], "schema": [{"id": "tool_type", "displayName": "tool_type", "required": false, "defaultMatch": false, "display": true, "canBeUsedToMatch": true, "type": "string", "removed": false}, {"id": "query", "displayName": "query", "required": false, "defaultMatch": false, "display": true, "canBeUsedToMatch": true, "type": "string", "removed": false}], "attemptToConvertTypes": false, "convertFieldsToString": false}}, "type": "@n8n/n8n-nodes-langchain.toolWorkflow", "typeVersion": 2, "position": [-560, 700], "id": "f3579fd4-cf59-4f4f-8679-9f4b1b15b25c", "name": "Web Search Tool"}, {"parameters": {"rules": {"values": [{"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"leftValue": "={{ $json.tool_type }}", "rightValue": "image_analysis", "operator": {"type": "string", "operation": "equals"}, "id": "b22be562-3277-48fc-87be-f3a3930f7fd6"}], "combinator": "and"}}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "a50a5a5a-cc87-4654-97ea-05090efdb416", "leftValue": "={{ $json.tool_type }}", "rightValue": "web_search", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}}]}, "options": {"fallbackOutput": "none"}}, "type": "n8n-nodes-base.switch", "typeVersion": 3.2, "position": [760, -60], "id": "0f61c3a5-ddcc-4dc1-86ee-f41cea1dfb55", "name": "Determine Tool Type"}, {"parameters": {"url": "=https://api.search.brave.com/res/v1/web/search?q={{ $('Tool Start').item.json.query }} }}&summary=1", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Accept", "value": "application/json"}, {"name": "Accept-Encoding", "value": "gzip"}]}, "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1040, 40], "id": "7343f9bc-5c03-493f-a211-cf9fa9af5752", "name": "Brave Web Search", "credentials": {"httpHeaderAuth": {"id": "6wzSkRM1jflKXEHm", "name": "<PERSON><PERSON><PERSON>"}}}, {"parameters": {"workflowInputs": {"values": [{"name": "tool_type"}, {"name": "query"}, {"name": "image_url"}]}}, "type": "n8n-nodes-base.executeWorkflowTrigger", "typeVersion": 1.1, "position": [540, -60], "id": "f0f2a762-cdd0-4fb2-8c5d-3fb6109c4d4b", "name": "Tool Start"}, {"parameters": {"url": "=https://api.search.brave.com/res/v1/summarizer/search?key={{ $json.summarizer.key }}&entity_info=1", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Accept", "value": "application/json"}, {"name": "Accept-Encoding", "value": "gzip"}]}, "options": {}}, "id": "6fabdad3-f640-44c3-9a5d-534ecb66293b", "name": "Summarize Web Research", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1300, 40], "credentials": {"httpHeaderAuth": {"id": "6wzSkRM1jflKXEHm", "name": "<PERSON><PERSON><PERSON>"}}}, {"parameters": {"operation": "download", "fileId": {"__rl": true, "value": "={{ $json.image_url }}", "mode": "url"}, "options": {"googleFileConversion": {"conversion": {"docsToFormat": "text/plain"}}}}, "id": "89f7c2bd-3514-4ac4-a346-07dbfb0d70e7", "name": "Download File1", "type": "n8n-nodes-base.googleDrive", "typeVersion": 3, "position": [1040, -140], "executeOnce": true, "credentials": {"googleDriveOAuth2Api": {"id": "p411dEzjKc4VbQGi", "name": "Google Drive account"}}}, {"parameters": {"resource": "image", "operation": "analyze", "modelId": {"__rl": true, "value": "gpt-4o-mini", "mode": "list", "cachedResultName": "GPT-4O-MINI"}, "text": "={{ $('Tool Start').item.json.query }}", "inputType": "base64", "options": {}}, "type": "@n8n/n8n-nodes-langchain.openAi", "typeVersion": 1.8, "position": [1300, -140], "id": "1d0c9153-932f-4c59-bafd-0f93ddd363ee", "name": "OpenAI", "credentials": {"openAiApi": {"id": "05Q6PbnSdyEcu9Ze", "name": "OpenAi account"}}}, {"parameters": {"name": "image_analysis", "description": "Call this tool to analyze an image based on an image URL that you supply as image_url. The image URL needs to be a URL to a Google Drive image. Also supply query, which is the prompt to the LLM to extract information from the image.\n\nUse the list_documents tool to get the URL for the image.", "workflowId": {"__rl": true, "value": "={{ $workflow.id }}", "mode": "id"}, "workflowInputs": {"mappingMode": "defineBelow", "value": {"query": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('query', ``, 'string') }}", "tool_type": "image_analysis", "image_url": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('image_url', ``, 'string') }}"}, "matchingColumns": [], "schema": [{"id": "tool_type", "displayName": "tool_type", "required": false, "defaultMatch": false, "display": true, "canBeUsedToMatch": true, "type": "string", "removed": false}, {"id": "query", "displayName": "query", "required": false, "defaultMatch": false, "display": true, "canBeUsedToMatch": true, "type": "string", "removed": false}, {"id": "image_url", "displayName": "image_url", "required": false, "defaultMatch": false, "display": true, "canBeUsedToMatch": true, "type": "string", "removed": false}], "attemptToConvertTypes": false, "convertFieldsToString": false}}, "type": "@n8n/n8n-nodes-langchain.toolWorkflow", "typeVersion": 2, "position": [-440, 600], "id": "f67be6dd-2605-43b6-83dd-6637191baf6a", "name": "Image Analysis Tool"}, {"parameters": {"content": "# Dynamous AI Agent Mastery - Cloud n8n Agent\nThis workflow implements a powerful AI agent leveraging cloud services through n8n. Use this as a template to build AI agents that utilize state-of-the-art cloud LLMs and have the core set of tools used in most agents!\n\n## Features\n- **Cloud LLM Integration**: Uses OpenAI models for high-quality conversations and embeddings. The LLM provider can be switched easily to something like Anthropic or OpenRouter too!\n- **Agentic RAG**: Google Drive integration and cloud vector storage for knowledge retrieval\n- **Long-term Memory**: Persistent conversation memory through cloud Supabase\n- **Web Search**: Brave API for comprehensive web search capabilities\n- **Code Execution**: Generates and runs JavaScript code\n- **Image Analysis**: Processes images with vision-capable cloud LLMs\n- **Google Drive Integration**: Automatically processes files from your Drive for knowledge retrieval\n\n## Setup Requirements\n1. **Cloud Services**:\n   - Supabase project\n   - OpenAI API key (or the key for another provider if you switch)\n   - Brave API key\n   - Google Drive API credentials\n2. **Environment Configuration**:\n   - Set up credentials in n8n for OpenAI, Supabase, Brave, and Google Drive\n   - Set appropriate model selections in the workflow (gpt-4o-mini, text-embedding-3-small, etc.)\n3. **Database Setup**:\n   - Run the nodes in the top left to set up the database tables (just have to do this once)\n   - Standard dimension of 1536 for OpenAI embeddings\n\n## Workflow Structure\nThe RAG pipeline (bottom part of the workflow in blue) is responsible for syncing a Google Drive folder with the Supabase knowledge base. Make sure the workflow is toggled to active in the top right for this pipeline to work!\n\nThe primary agent is in the yellow box in the middle of the workflow. All of the tools are connected either directly to the agent (in the same box or agentic RAG in the middle green box) or as \"sub-workflows\" which are in the green top box.\n\nThe process of creating long term memories is in the purple boxes.\n\n## Key Differentiators for the Cloud Implementation (Compared to Local)\n- Access to state-of-the-art LLM capabilities (GPT-4o, Claude 3.7, etc.)\n- Reduced computational load on your machine\n- Google Drive integration for seamless document processing\n- Scalable infrastructure that can handle growing data needs\n- Comprehensive web search via Brave API", "height": 1240, "width": 640, "color": 5}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-3020, 380], "id": "4d70ad17-f902-4d56-8489-c8efb9836cf0", "name": "Sticky Note8"}, {"parameters": {"httpMethod": "POST", "path": "agent-api", "responseMode": "responseNode", "options": {}}, "id": "9704c139-2800-4211-aff3-d67b01623800", "name": "Webhook", "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [-2280, 400], "webhookId": "85473238-8da8-4598-a6ed-cc64965e7d49"}, {"parameters": {"public": true, "options": {}}, "id": "eaecf302-6805-4586-b8ef-6d646f90be17", "name": "When chat message received", "type": "@n8n/n8n-nodes-langchain.chatTrigger", "typeVersion": 1.1, "position": [-940, 380], "webhookId": "6582629e-98d3-43c9-9bc9-fae3d0e2c68b"}, {"parameters": {"assignments": {"assignments": [{"id": "9a9a245e-f1a1-4282-bb02-a81ffe629f0f", "name": "chatInput", "value": "={{ $json.chatInput || $('Webhook').item.json.body.query }}", "type": "string"}, {"id": "b80831d8-c653-4203-8706-adedfdb98f77", "name": "sessionId", "value": "={{ $json.sessionId || $json.session_id || $('Webhook').item.json.body.session_id }}", "type": "string"}]}, "options": {}}, "id": "dd7c6371-49aa-431f-b08c-857c06286f69", "name": "<PERSON>", "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [-740, 280]}, {"parameters": {"tableId": "conversations", "fieldsUi": {"fieldValues": [{"fieldId": "user_id", "fieldValue": "={{ $('Webhook').item.json.body.user_id }}"}, {"fieldId": "session_id", "fieldValue": "={{ $json.session_id }}"}]}}, "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [-740, -40], "id": "a1d94e15-d20a-4c0b-8fd0-98ddf6a99416", "name": "Supabase1", "credentials": {"supabaseApi": {"id": "Eu6anmgplJHxqZu2", "name": "Studio Test"}}}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "201ba6c4-9be3-4ae1-b7ca-9321c0542549", "leftValue": "={{ $json.body.session_id }}", "rightValue": "", "operator": {"type": "string", "operation": "empty", "singleValue": true}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [-1180, 260], "id": "2538ed23-f393-4bdc-9950-798f763b4359", "name": "If1"}, {"parameters": {"assignments": {"assignments": [{"id": "57be6196-201b-43fa-8c37-06fb9f53f7c8", "name": "session_id", "value": "={{ $('Webhook').item.json.body.user_id }}~{{ [...Array(10)].map(() => Math.random().toString(36).charAt(2)).join('') }}", "type": "string"}, {"id": "9e8dcec6-c03f-44c7-b7b4-3c38ba9ce3be", "name": "chat_input", "value": "={{ $json.body.query }}", "type": "string"}]}, "includeOtherFields": true, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [-940, -40], "id": "be9b23b0-59c8-499f-b343-2848abe318dc", "name": "Edit Fields2"}, {"parameters": {"operation": "update", "tableId": "conversations", "filters": {"conditions": [{"keyName": "session_id", "condition": "eq", "keyValue": "={{ $('Edit Fields2').item.json.session_id }}"}]}, "fieldsUi": {"fieldValues": [{"fieldId": "title", "fieldValue": "={{ $json.text }}"}]}}, "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [-180, -40], "id": "2fe411a5-c9a1-4db9-a6c8-1a378bb52ec4", "name": "Supabase", "credentials": {"supabaseApi": {"id": "Eu6anmgplJHxqZu2", "name": "Studio Test"}}}, {"parameters": {"model": {"__rl": true, "value": "gpt-4.1-nano", "mode": "list", "cachedResultName": "gpt-4.1-nano"}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1.2, "position": [-460, 140], "id": "15c784cc-26d8-411c-a624-dd1cfc8ee873", "name": "OpenAI Chat Model3", "credentials": {"openAiApi": {"id": "05Q6PbnSdyEcu9Ze", "name": "OpenAi account"}}}, {"parameters": {"promptType": "define", "text": "=Based on the user message below, create a 4-6 word sentence for the conversation description since this is the first message in the description.\n\n{{ $('Webhook').item.json.body.query }}"}, "type": "@n8n/n8n-nodes-langchain.chainLlm", "typeVersion": 1.6, "position": [-560, -40], "id": "6cc894f7-9d3d-4a50-90ff-9ffd68b4b26d", "name": "Basic LLM Chain1"}, {"parameters": {"numberInputs": 3}, "type": "n8n-nodes-base.merge", "typeVersion": 3.1, "position": [-140, 220], "id": "********-3385-4ad0-97f5-c7e926a96961", "name": "<PERSON><PERSON>"}, {"parameters": {"fieldsToAggregate": {"fieldToAggregate": [{"fieldToAggregate": "output"}, {"fieldToAggregate": "title", "renameField": true, "outputFieldName": "conversation_title"}, {"fieldToAggregate": "session_id"}]}, "options": {}}, "type": "n8n-nodes-base.aggregate", "typeVersion": 1, "position": [0, 220], "id": "a7ce18e3-ae94-4b61-8649-fa2822eedf59", "name": "Aggregate1"}, {"parameters": {"assignments": {"assignments": [{"id": "c05ad106-f118-4d0d-a361-724945332d6f", "name": "output", "value": "={{ $json.output[0] }}", "type": "string"}, {"id": "cb080a99-02ae-43c2-a5b5-25ac3617c523", "name": "conversation_title", "value": "={{ $json.conversation_title[0] }}", "type": "string"}, {"id": "8bb827bb-1b97-4e3b-be3b-da37ede6b052", "name": "session_id", "value": "={{ $json.session_id[0] }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [140, 220], "id": "a32d89c6-1fae-41bb-bb08-37e950370ab8", "name": "Edit Fields3"}, {"parameters": {"url": "https://your-supabase-url/auth/v1/user", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Authorization", "value": "={{ $json.headers.authorization }}"}, {"name": "apikey", "value": "your-supabase-anon-key"}]}, "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [-2120, 260], "id": "cc4bc721-caf3-4b47-aed7-4a267cf42e38", "name": "HTTP Request"}, {"parameters": {"errorMessage": "Unauthorized call to the agent API!"}, "type": "n8n-nodes-base.stopAndError", "typeVersion": 1, "position": [-1740, 520], "id": "024f774b-2080-42cc-b6f2-20248d2b7c09", "name": "Stop and Error"}, {"parameters": {"operation": "getAll", "tableId": "requests", "limit": 5, "filters": {"conditions": [{"keyName": "timestamp", "condition": "gt", "keyValue": "={{ $now.toUTC().minus(1, 'minutes').format('yyyy-MM-dd HH:mm:ss') }}"}, {"keyName": "user_id", "condition": "eq", "keyValue": "={{ $('Webhook').item.json.body.user_id }}"}]}}, "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [-1740, 260], "id": "d4618fb3-b68f-4e12-8320-8fa429d748eb", "name": "Supabase2", "alwaysOutputData": true, "credentials": {"supabaseApi": {"id": "Eu6anmgplJHxqZu2", "name": "Studio Test"}}}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "ae9ed956-5249-4c7d-a273-5ddafe604388", "leftValue": "={{ $json.count_id }}", "rightValue": 5, "operator": {"type": "number", "operation": "lt"}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [-1380, 400], "id": "e473adb2-7ae2-41b8-8670-b72e30a73711", "name": "If2"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "be275ce9-093e-4a06-81ec-606a1d7358a3", "leftValue": "={{ $('Webhook').item.json.body.user_id }}", "rightValue": "={{ $json.id }}", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [-1940, 400], "id": "35d16e81-cc6f-44d3-ab67-ded6c9e51b16", "name": "If3"}, {"parameters": {"fieldsToSummarize": {"values": [{"field": "id"}]}, "options": {}}, "type": "n8n-nodes-base.summarize", "typeVersion": 1.1, "position": [-1560, 400], "id": "1a84750e-8199-42eb-a768-b344771a7273", "name": "Summarize1"}, {"parameters": {"respondWith": "json", "responseBody": "{\n  \"output\": \"Rate limit exceeded!\"\n}", "options": {}}, "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1.1, "position": [-1180, 520], "id": "009a6465-9874-4f55-b869-0b3b80b856d7", "name": "Respond to Webhook1"}, {"parameters": {"tableId": "requests", "fieldsUi": {"fieldValues": [{"fieldId": "id", "fieldValue": "={{ $('Webhook').item.json.body.request_id }}"}, {"fieldId": "user_id", "fieldValue": "={{ $('Webhook').item.json.body.user_id }}"}, {"fieldId": "user_query", "fieldValue": "={{ $('Webhook').item.json.body.query }}"}]}}, "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [-940, 160], "id": "9e4bba38-4ac6-41f9-8da1-f044d3e335e0", "name": "Supabase3", "credentials": {"supabaseApi": {"id": "Eu6anmgplJHxqZu2", "name": "Studio Test"}}}], "pinData": {"Tool Start": [{"json": {"tool_type": "image_analysis", "query": "https://drive.google.com/file/d/1vnjEMW9XKgWwmy17VjZO8ci2Mi7RO_9e/view?usp=drive_link"}}]}, "connections": {"OpenAI Chat Model": {"ai_languageModel": [[{"node": "RAG AI Agent", "type": "ai_languageModel", "index": 0}]]}, "Download File": {"main": [[{"node": "Switch", "type": "main", "index": 0}]]}, "File Created": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}]]}, "Extract Document Text": {"main": [[{"node": "Insert into Supabase Vectorstore", "type": "main", "index": 0}]]}, "Embeddings OpenAI1": {"ai_embedding": [[{"node": "Insert into Supabase Vectorstore", "type": "ai_embedding", "index": 0}]]}, "Default Data Loader": {"ai_document": [[{"node": "Insert into Supabase Vectorstore", "type": "ai_document", "index": 0}]]}, "Postgres Chat Memory": {"ai_memory": [[{"node": "RAG AI Agent", "type": "ai_memory", "index": 0}]]}, "Delete Old Doc Rows": {"main": [[{"node": "Delete Old Data Rows", "type": "main", "index": 0}]]}, "Set File ID": {"main": [[{"node": "Delete Old Doc Rows", "type": "main", "index": 0}]]}, "File Updated": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}]]}, "Extract PDF Text": {"main": [[{"node": "Insert into Supabase Vectorstore", "type": "main", "index": 0}]]}, "Aggregate": {"main": [[{"node": "Summarize", "type": "main", "index": 0}]]}, "Summarize": {"main": [[{"node": "<PERSON>", "type": "main", "index": 0}, {"node": "Insert into Supabase Vectorstore", "type": "main", "index": 0}]]}, "RAG AI Agent": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 2}]]}, "Switch": {"main": [[{"node": "Extract PDF Text", "type": "main", "index": 0}], [{"node": "Extract from Excel", "type": "main", "index": 0}], [{"node": "Extract from CSV", "type": "main", "index": 0}], [{"node": "Extract Document Text", "type": "main", "index": 0}], [{"node": "Extract Document Text", "type": "main", "index": 0}]]}, "Extract from Excel": {"main": [[{"node": "Aggregate", "type": "main", "index": 0}, {"node": "Insert Table Rows", "type": "main", "index": 0}]]}, "Set Schema": {"main": [[{"node": "Update <PERSON><PERSON>a for Document Metadata", "type": "main", "index": 0}]]}, "Extract from CSV": {"main": [[{"node": "Aggregate", "type": "main", "index": 0}, {"node": "Insert Table Rows", "type": "main", "index": 0}]]}, "Create Document Metadata Table": {"main": [[]]}, "List Documents": {"ai_tool": [[{"node": "RAG AI Agent", "type": "ai_tool", "index": 0}]]}, "Get File Contents": {"ai_tool": [[{"node": "RAG AI Agent", "type": "ai_tool", "index": 0}]]}, "Query Document Rows": {"ai_tool": [[{"node": "RAG AI Agent", "type": "ai_tool", "index": 0}]]}, "Embeddings OpenAI2": {"ai_embedding": [[{"node": "Document RAG Tool", "type": "ai_embedding", "index": 0}]]}, "Loop Over Items": {"main": [[], [{"node": "Set File ID", "type": "main", "index": 0}]]}, "Insert into Supabase Vectorstore": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}]]}, "Create Documents Table and Match Function": {"main": [[]]}, "Delete Old Data Rows": {"main": [[{"node": "Insert Document Metadata", "type": "main", "index": 0}]]}, "Insert Document Metadata": {"main": [[{"node": "Download File", "type": "main", "index": 0}]]}, "Update Schema for Document Metadata": {"main": [[]]}, "Recursive Character Text Splitter": {"ai_textSplitter": [[{"node": "Default Data Loader", "type": "ai_textSplitter", "index": 0}]]}, "Respond to Webhook": {"main": [[{"node": "Basic LLM Chain", "type": "main", "index": 0}]]}, "Structured Output Parser": {"ai_outputParser": [[{"node": "Basic LLM Chain", "type": "ai_outputParser", "index": 0}]]}, "Embeddings OpenAI": {"ai_embedding": [[{"node": "Supabase Vector Store", "type": "ai_embedding", "index": 0}]]}, "Default Data Loader1": {"ai_document": [[{"node": "Supabase Vector Store", "type": "ai_document", "index": 0}]]}, "Character Text Splitter": {"ai_textSplitter": [[{"node": "Default Data Loader1", "type": "ai_textSplitter", "index": 0}]]}, "Embeddings OpenAI3": {"ai_embedding": [[{"node": "Retrieve Memories Tool", "type": "ai_embedding", "index": 0}]]}, "Retrieve Memories Tool": {"ai_tool": [[{"node": "RAG AI Agent", "type": "ai_tool", "index": 0}]]}, "Document RAG Tool": {"ai_tool": [[{"node": "RAG AI Agent", "type": "ai_tool", "index": 0}]]}, "OpenAI Chat Model2": {"ai_languageModel": [[{"node": "Memory Agent", "type": "ai_languageModel", "index": 0}]]}, "Supabase Vector Store": {"ai_tool": [[]], "main": [[{"node": "Edit Fields1", "type": "main", "index": 0}]]}, "Memory Agent": {"main": [[{"node": "Postgres", "type": "main", "index": 0}]]}, "Embeddings OpenAI4": {"ai_embedding": [[{"node": "Similar Memory Searcher", "type": "ai_embedding", "index": 0}]]}, "If": {"main": [[{"node": "Memory Agent", "type": "main", "index": 0}], [{"node": "Edit Fields1", "type": "main", "index": 0}]]}, "Basic LLM Chain": {"main": [[{"node": "If", "type": "main", "index": 0}]]}, "OpenAI Chat Model1": {"ai_languageModel": [[{"node": "Basic LLM Chain", "type": "ai_languageModel", "index": 0}]]}, "Similar Memory Searcher": {"ai_tool": [[{"node": "Memory Agent", "type": "ai_tool", "index": 0}]]}, "Structured Output Parser1": {"ai_outputParser": [[{"node": "Memory Agent", "type": "ai_outputParser", "index": 0}]]}, "Postgres": {"main": [[{"node": "Supabase Vector Store", "type": "main", "index": 0}]]}, "Code Tool": {"ai_tool": [[{"node": "RAG AI Agent", "type": "ai_tool", "index": 0}]]}, "Web Search Tool": {"ai_tool": [[{"node": "RAG AI Agent", "type": "ai_tool", "index": 0}]]}, "Determine Tool Type": {"main": [[{"node": "Download File1", "type": "main", "index": 0}], [{"node": "Brave Web Search", "type": "main", "index": 0}]]}, "Tool Start": {"main": [[{"node": "Determine Tool Type", "type": "main", "index": 0}]]}, "Brave Web Search": {"main": [[{"node": "Summarize Web Research", "type": "main", "index": 0}]]}, "Download File1": {"main": [[{"node": "OpenAI", "type": "main", "index": 0}]]}, "Image Analysis Tool": {"ai_tool": [[{"node": "RAG AI Agent", "type": "ai_tool", "index": 0}]]}, "Webhook": {"main": [[{"node": "HTTP Request", "type": "main", "index": 0}]]}, "When chat message received": {"main": [[{"node": "<PERSON>", "type": "main", "index": 0}]]}, "Edit Fields": {"main": [[{"node": "RAG AI Agent", "type": "main", "index": 0}]]}, "If1": {"main": [[{"node": "Edit Fields2", "type": "main", "index": 0}, {"node": "Supabase3", "type": "main", "index": 0}], [{"node": "<PERSON>", "type": "main", "index": 0}, {"node": "Supabase3", "type": "main", "index": 0}]]}, "Edit Fields2": {"main": [[{"node": "Supabase1", "type": "main", "index": 0}]]}, "OpenAI Chat Model3": {"ai_languageModel": [[{"node": "Basic LLM Chain1", "type": "ai_languageModel", "index": 0}]]}, "Supabase1": {"main": [[{"node": "Basic LLM Chain1", "type": "main", "index": 0}, {"node": "<PERSON><PERSON>", "type": "main", "index": 1}, {"node": "<PERSON>", "type": "main", "index": 0}]]}, "Basic LLM Chain1": {"main": [[{"node": "Supabase", "type": "main", "index": 0}]]}, "Merge": {"main": [[{"node": "Aggregate1", "type": "main", "index": 0}]]}, "Aggregate1": {"main": [[{"node": "Edit Fields3", "type": "main", "index": 0}]]}, "Edit Fields3": {"main": [[{"node": "Respond to Webhook", "type": "main", "index": 0}]]}, "Supabase": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 0}]]}, "HTTP Request": {"main": [[{"node": "If3", "type": "main", "index": 0}]]}, "Supabase2": {"main": [[{"node": "Summarize1", "type": "main", "index": 0}]]}, "If3": {"main": [[{"node": "Supabase2", "type": "main", "index": 0}], [{"node": "Stop and Error", "type": "main", "index": 0}]]}, "Summarize1": {"main": [[{"node": "If2", "type": "main", "index": 0}]]}, "If2": {"main": [[{"node": "If1", "type": "main", "index": 0}], [{"node": "Respond to Webhook1", "type": "main", "index": 0}]]}}, "active": true, "settings": {"executionOrder": "v1"}, "versionId": "cf851042-8ba6-406d-850c-d83684ce164b", "meta": {"templateCredsSetupCompleted": true, "instanceId": "f65a08c0adc90a3cde2c633d24c6daecde3817033b75588ee10a781b0b7aa3f5"}, "id": "0sbMBGSxACK5BqQA", "tags": []}