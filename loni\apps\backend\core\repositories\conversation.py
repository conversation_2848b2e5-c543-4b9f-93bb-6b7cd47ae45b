"""
Conversation repository implementation.
"""
from typing import List, Optional
from uuid import UUID

from sqlalchemy import select, desc
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload

from ..models.conversation import Conversation, Message
from .base import BaseRepository


class ConversationRepository(BaseRepository[Conversation]):
    """Repository for Conversation model operations."""
    
    def __init__(self, session: AsyncSession):
        super().__init__(Conversation, session)
    
    async def get_user_conversations(
        self, 
        user_id: UUID, 
        limit: int = 50, 
        offset: int = 0
    ) -> List[Conversation]:
        """Get conversations for a specific user, ordered by most recent."""
        query = (
            select(self.model)
            .where(self.model.user_id == user_id)
            .where(self.model.deleted_at.is_(None))
            .order_by(desc(self.model.updated_at))
            .limit(limit)
            .offset(offset)
        )
        result = await self.session.execute(query)
        return list(result.scalars().all())
    
    async def get_with_messages(self, conversation_id: UUID) -> Optional[Conversation]:
        """Get conversation with all its messages loaded."""
        query = (
            select(self.model)
            .options(selectinload(self.model.messages))
            .where(self.model.id == conversation_id)
            .where(self.model.deleted_at.is_(None))
        )
        result = await self.session.execute(query)
        return result.scalar_one_or_none()
    
    async def update_title(self, conversation_id: UUID, title: str) -> Optional[Conversation]:
        """Update conversation title."""
        conversation = await self.get_by_id(conversation_id)
        if not conversation:
            return None
        
        conversation.title = title
        await self.session.commit()
        await self.session.refresh(conversation)
        return conversation
    
    async def update_model_config(
        self, 
        conversation_id: UUID, 
        model_name: str,
        rag_enabled: bool = True,
        temperature: float = 0.7
    ) -> Optional[Conversation]:
        """Update conversation AI model configuration."""
        conversation = await self.get_by_id(conversation_id)
        if not conversation:
            return None
        
        conversation.model_name = model_name
        conversation.rag_enabled = rag_enabled
        conversation.temperature = temperature
        await self.session.commit()
        await self.session.refresh(conversation)
        return conversation


class MessageRepository(BaseRepository[Message]):
    """Repository for Message model operations."""
    
    def __init__(self, session: AsyncSession):
        super().__init__(Message, session)
    
    async def get_conversation_messages(
        self, 
        conversation_id: UUID,
        limit: int = 100,
        offset: int = 0
    ) -> List[Message]:
        """Get messages for a conversation, ordered chronologically."""
        query = (
            select(self.model)
            .where(self.model.conversation_id == conversation_id)
            .where(self.model.deleted_at.is_(None))
            .order_by(self.model.created_at)
            .limit(limit)
            .offset(offset)
        )
        result = await self.session.execute(query)
        return list(result.scalars().all())
    
    async def get_latest_messages(
        self, 
        conversation_id: UUID, 
        count: int = 10
    ) -> List[Message]:
        """Get the latest N messages from a conversation."""
        query = (
            select(self.model)
            .where(self.model.conversation_id == conversation_id)
            .where(self.model.deleted_at.is_(None))
            .order_by(desc(self.model.created_at))
            .limit(count)
        )
        result = await self.session.execute(query)
        messages = list(result.scalars().all())
        return list(reversed(messages))  # Return in chronological order
    
    async def create_message_pair(
        self,
        conversation_id: UUID,
        user_content: str,
        assistant_content: str,
        model_name: str,
        tokens_used: int = 0
    ) -> tuple[Message, Message]:
        """Create both user and assistant messages atomically."""
        user_message = Message(
            conversation_id=conversation_id,
            role="user",
            content=user_content
        )
        
        assistant_message = Message(
            conversation_id=conversation_id,
            role="assistant", 
            content=assistant_content,
            model_name=model_name,
            tokens_used=tokens_used
        )
        
        self.session.add(user_message)
        self.session.add(assistant_message)
        await self.session.commit()
        
        await self.session.refresh(user_message)
        await self.session.refresh(assistant_message)
        
        return user_message, assistant_message