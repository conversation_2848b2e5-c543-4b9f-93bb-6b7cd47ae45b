#!/bin/bash
# Test Docker Compose Error Logging Implementation
# Validates that the error logging system works correctly

set -euo pipefail

# Configuration
INFRA_DIR="E:/Projects/lonors/loni/apps/infra"
LOG_DIR="../../data/logs/docker-compose"

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log() { echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"; }
error() { echo -e "${RED}[ERROR]${NC} $1" >&2; }
success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
warn() { echo -e "${YELLOW}[WARNING]${NC} $1"; }

# Change to infra directory
cd "$INFRA_DIR" || exit 1
log "Testing Docker Compose error logging implementation"

# Test 1: Check if log directory exists
log "Test 1: Checking log directory structure"
if [[ -d "$LOG_DIR" ]]; then
    success "Log directory exists: $LOG_DIR"
else
    warn "Creating log directory: $LOG_DIR"
    mkdir -p "$LOG_DIR"
fi

# Test 2: Check if docker-monitor service is defined
log "Test 2: Checking docker-monitor service definition"
if grep -q "docker-monitor:" docker-compose.yml; then
    success "Docker monitor service is defined in docker-compose.yml"
else
    error "Docker monitor service not found in docker-compose.yml"
    exit 1
fi

# Test 3: Check if monitoring scripts exist
log "Test 3: Checking monitoring scripts"
if [[ -f "config/docker-monitor/scripts/docker_monitor.py" ]]; then
    success "Docker monitor Python script exists"
else
    error "Docker monitor Python script not found"
    exit 1
fi

# Test 4: Validate docker-compose configuration
log "Test 4: Validating docker-compose configuration"
if docker-compose config --quiet; then
    success "Docker Compose configuration is valid"
else
    error "Docker Compose configuration has errors"
    exit 1
fi

# Test 5: Check current service status
log "Test 5: Checking current service status"
if docker-compose ps > /dev/null 2>&1; then
    running_services=$(docker-compose ps --filter "status=running" --format "{{.Service}}" 2>/dev/null | wc -l || echo "0")
    total_services=$(docker-compose ps --format "{{.Service}}" 2>/dev/null | wc -l || echo "0")
    log "Current status: $running_services/$total_services services running"
else
    warn "Unable to check service status (services may not be running)"
fi

# Test 6: Test error logging structure
log "Test 6: Testing error logging structure"
test_error_file="$LOG_DIR/test-error.jsonl"
test_error='{"timestamp":"2025-07-13T06:30:45.123Z","service":"test","error_type":"test_error","message":"Test error message","severity":"ERROR"}'

echo "$test_error" > "$test_error_file"
if [[ -f "$test_error_file" ]]; then
    success "Error logging structure test passed"
    rm "$test_error_file"
else
    error "Failed to create test error log"
    exit 1
fi

# Test 7: Check if cleanup script exists
log "Test 7: Checking cleanup script"
if [[ -f "scripts/cleanup-resolved-errors.py" ]]; then
    success "Error cleanup script exists"
else
    error "Error cleanup script not found"
    exit 1
fi

# Test 8: Test Python dependencies for monitoring
log "Test 8: Testing Python dependencies"
if python3 -c "import docker, yaml, json; print('Dependencies OK')" 2>/dev/null; then
    success "Python dependencies are available"
else
    warn "Python dependencies may not be installed (docker, yaml)"
fi

# Test 9: Check execution scripts
log "Test 9: Checking execution scripts"
scripts_found=0

if [[ -f "scripts/docker-compose-with-logging.ps1" ]]; then
    success "PowerShell execution script exists"
    ((scripts_found++))
fi

if [[ -f "scripts/execute-docker-ops.sh" ]]; then
    success "Bash execution script exists"
    ((scripts_found++))
fi

if [[ $scripts_found -eq 0 ]]; then
    error "No execution scripts found"
    exit 1
fi

# Test 10: Create sample status files
log "Test 10: Creating sample status files"

# Sample status summary
status_summary='{
  "timestamp": "'$(date -u +"%Y-%m-%dT%H:%M:%S.%3NZ")'",
  "healthy_services": [],
  "services_with_errors": [],
  "total_healthy": 0,
  "total_with_errors": 0,
  "total_errors": 0,
  "status": "testing"
}'

echo "$status_summary" > "$LOG_DIR/status-summary.json"
success "Created sample status summary"

# Sample current errors (empty for healthy state)
echo "" > "$LOG_DIR/current-errors.jsonl"
success "Created current errors file"

# Test Summary
log "=== TEST SUMMARY ==="
success "All error logging implementation tests passed!"

log "Implementation includes:"
log "  ✅ Docker monitor service definition"
log "  ✅ Python monitoring script with intelligent error clearing"
log "  ✅ Log directory structure"
log "  ✅ Execution scripts (PowerShell and Bash)"
log "  ✅ Error cleanup mechanism"
log "  ✅ Structured JSON logging format"
log "  ✅ AI-friendly status summaries"

log "Next steps:"
log "  1. Run: docker-compose up -d --build"
log "  2. Monitor: tail -f $LOG_DIR/docker-errors.jsonl"
log "  3. Check status: cat $LOG_DIR/status-summary.json"
log "  4. Clean errors: python scripts/cleanup-resolved-errors.py"

success "Docker Compose error logging implementation is ready!"
