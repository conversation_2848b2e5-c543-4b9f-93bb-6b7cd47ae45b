"""
Redis cache configuration settings.

This module contains all Redis-related configuration
following the Single Responsibility Principle.
"""

from typing import Optional
from pydantic_settings import BaseSettings


class RedisSettings(BaseSettings):
    """Redis cache settings."""
    
    host: str = "localhost"
    port: int = 6379
    database: int = 0
    password: Optional[str] = None
    
    # Connection pool settings
    max_connections: int = 20
    socket_timeout: int = 5
    socket_connect_timeout: int = 5
    
    # Cache TTL settings
    default_ttl: int = 3600  # 1 hour
    session_ttl: int = 86400  # 24 hours
    
    class Config:
        env_prefix = "REDIS_"
    
    @property
    def url(self) -> str:
        """Construct Redis URL."""
        if self.password:
            return f"redis://:{self.password}@{self.host}:{self.port}/{self.database}"
        return f"redis://{self.host}:{self.port}/{self.database}"