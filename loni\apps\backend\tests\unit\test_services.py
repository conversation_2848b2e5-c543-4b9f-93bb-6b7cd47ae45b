"""
Unit tests for service layer.

This module tests the core business logic services.
"""

import pytest
from unittest.mock import AsyncMock, MagicMock, patch
from uuid import uuid4

from core.services.ai.chat_service import ChatService
from core.services.ai.model_service import ModelService
from core.services.ai.rag_service import RAGService
from core.models.user import User
from core.models.conversation import Conversation, Message
from core.models.ai_model import AI<PERSON>ode<PERSON>, ModelProvider, ModelType


class TestChatService:
    """Test ChatService functionality."""
    
    @pytest.fixture
    def mock_model_service(self):
        """Mock model service."""
        service = MagicMock(spec=ModelService)
        service.get_model_info = AsyncMock(return_value={
            "name": "gpt-4",
            "provider": "openai",
            "capabilities": ["chat", "streaming"]
        })
        return service
    
    @pytest.fixture
    def mock_rag_service(self):
        """Mock RAG service."""
        service = MagicMock(spec=RAGService)
        service.get_context = AsyncMock(return_value="Relevant context from documents")
        return service
    
    @pytest.fixture
    def chat_service(self, mock_model_service, mock_rag_service):
        """Create chat service with mocked dependencies."""
        return ChatService(
            model_service=mock_model_service,
            rag_service=mock_rag_service
        )
    
    @pytest.mark.asyncio
    async def test_generate_response_openai(self, chat_service, mock_openai_response):
        """Test response generation with OpenAI."""
        messages = [{"role": "user", "content": "Hello"}]
        
        with patch.object(chat_service, '_clients', {'openai': AsyncMock()}):
            mock_client = chat_service._clients['openai']
            mock_completion = AsyncMock()
            mock_completion.choices = [
                MagicMock(message=MagicMock(content="Hello! How can I help you?"))
            ]
            mock_client.chat.completions.create = AsyncMock(return_value=mock_completion)
            
            response = await chat_service.generate_response(
                messages=messages,
                model_id="gpt-4",
                conversation_id=uuid4()
            )
            
            assert response == "Hello! How can I help you?"
            mock_client.chat.completions.create.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_generate_response_with_rag(self, chat_service, mock_openai_response):
        """Test response generation with RAG context."""
        messages = [{"role": "user", "content": "What is AI?"}]
        conversation_id = uuid4()
        
        with patch.object(chat_service, '_clients', {'openai': AsyncMock()}):
            mock_client = chat_service._clients['openai']
            mock_completion = AsyncMock()
            mock_completion.choices = [
                MagicMock(message=MagicMock(content="AI is artificial intelligence."))
            ]
            mock_client.chat.completions.create = AsyncMock(return_value=mock_completion)
            
            response = await chat_service.generate_response(
                messages=messages,
                model_id="gpt-4",
                conversation_id=conversation_id,
                rag_enabled=True
            )
            
            assert response == "AI is artificial intelligence."
            # Verify RAG service was called
            chat_service.rag_service.get_context.assert_called_once_with(
                query="What is AI?",
                conversation_id=conversation_id
            )
    
    @pytest.mark.asyncio
    async def test_generate_title(self, chat_service):
        """Test conversation title generation."""
        messages = [
            {"role": "user", "content": "What is machine learning?"},
            {"role": "assistant", "content": "Machine learning is..."}
        ]
        
        with patch.object(chat_service, '_clients', {'openai': AsyncMock()}):
            mock_client = chat_service._clients['openai']
            mock_completion = AsyncMock()
            mock_completion.choices = [
                MagicMock(message=MagicMock(content="Machine Learning Basics"))
            ]
            mock_client.chat.completions.create = AsyncMock(return_value=mock_completion)
            
            title = await chat_service.generate_title(messages)
            
            assert title == "Machine Learning Basics"
            mock_client.chat.completions.create.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_generate_response_unsupported_provider(self, chat_service):
        """Test response generation with unsupported provider."""
        messages = [{"role": "user", "content": "Hello"}]
        
        with pytest.raises(ValueError, match="Unsupported model provider"):
            await chat_service.generate_response(
                messages=messages,
                model_id="unknown-model",
                conversation_id=uuid4()
            )
    
    def test_get_model_provider(self, chat_service):
        """Test model provider detection."""
        assert chat_service._get_model_provider("gpt-4") == "openai"
        assert chat_service._get_model_provider("claude-3") == "anthropic"
        assert chat_service._get_model_provider("llama3.2") == "ollama"
    
    def test_format_messages_for_openai(self, chat_service):
        """Test message formatting for OpenAI."""
        messages = [
            {"role": "user", "content": "Hello"},
            {"role": "assistant", "content": "Hi there!"}
        ]
        rag_context = "Relevant context"
        
        formatted = chat_service._format_messages_for_openai(messages, rag_context)
        
        assert len(formatted) == 3  # system + 2 messages
        assert formatted[0]["role"] == "system"
        assert "context" in formatted[0]["content"].lower()
        assert formatted[1]["role"] == "user"
        assert formatted[2]["role"] == "assistant"


class TestModelService:
    """Test ModelService functionality."""
    
    @pytest.fixture
    def model_service(self):
        """Create model service."""
        return ModelService()
    
    @pytest.mark.asyncio
    async def test_get_available_models(self, model_service, test_session):
        """Test getting available models."""
        # Create test models
        model1 = AIModel(
            name="gpt-4",
            provider=ModelProvider.OPENAI,
            model_type=ModelType.CHAT,
            is_enabled=True
        )
        model2 = AIModel(
            name="claude-3",
            provider=ModelProvider.ANTHROPIC,
            model_type=ModelType.CHAT,
            is_enabled=False
        )
        
        test_session.add_all([model1, model2])
        await test_session.commit()
        
        with patch.object(model_service, 'session', test_session):
            models = await model_service.get_available_models()
            
            # Should only return enabled models
            assert len(models) == 1
            assert models[0].name == "gpt-4"
    
    @pytest.mark.asyncio
    async def test_get_model_by_name(self, model_service, test_session):
        """Test getting model by name."""
        model = AIModel(
            name="gpt-4",
            provider=ModelProvider.OPENAI,
            model_type=ModelType.CHAT
        )
        
        test_session.add(model)
        await test_session.commit()
        
        with patch.object(model_service, 'session', test_session):
            found_model = await model_service.get_model_by_name("gpt-4")
            
            assert found_model is not None
            assert found_model.name == "gpt-4"
    
    @pytest.mark.asyncio
    async def test_get_models_by_provider(self, model_service, test_session):
        """Test getting models by provider."""
        model1 = AIModel(
            name="gpt-4",
            provider=ModelProvider.OPENAI,
            model_type=ModelType.CHAT
        )
        model2 = AIModel(
            name="gpt-3.5",
            provider=ModelProvider.OPENAI,
            model_type=ModelType.CHAT
        )
        model3 = AIModel(
            name="claude-3",
            provider=ModelProvider.ANTHROPIC,
            model_type=ModelType.CHAT
        )
        
        test_session.add_all([model1, model2, model3])
        await test_session.commit()
        
        with patch.object(model_service, 'session', test_session):
            openai_models = await model_service.get_models_by_provider(ModelProvider.OPENAI)
            
            assert len(openai_models) == 2
            assert all(model.provider == ModelProvider.OPENAI for model in openai_models)


class TestRAGService:
    """Test RAGService functionality."""
    
    @pytest.fixture
    def rag_service(self):
        """Create RAG service."""
        return RAGService()
    
    @pytest.mark.asyncio
    async def test_get_context_placeholder(self, rag_service):
        """Test RAG context retrieval (placeholder implementation)."""
        # Since RAG service is not fully implemented, test the placeholder
        context = await rag_service.get_context(
            query="What is AI?",
            conversation_id=uuid4()
        )
        
        # Should return placeholder or None
        assert context is None or isinstance(context, str)
    
    @pytest.mark.asyncio
    async def test_process_document_placeholder(self, rag_service):
        """Test document processing (placeholder implementation)."""
        document_id = uuid4()
        
        # Should not raise an error
        result = await rag_service.process_document(document_id)
        
        # Should return some result or None
        assert result is None or isinstance(result, dict)


class TestServiceIntegration:
    """Test service integration scenarios."""
    
    @pytest.mark.asyncio
    async def test_chat_with_rag_integration(self):
        """Test chat service with RAG integration."""
        # Mock services
        model_service = MagicMock(spec=ModelService)
        model_service.get_model_info = AsyncMock(return_value={
            "name": "gpt-4",
            "provider": "openai",
            "capabilities": ["chat", "rag"]
        })
        
        rag_service = MagicMock(spec=RAGService)
        rag_service.get_context = AsyncMock(return_value="Relevant context")
        
        chat_service = ChatService(
            model_service=model_service,
            rag_service=rag_service
        )
        
        messages = [{"role": "user", "content": "What is AI?"}]
        conversation_id = uuid4()
        
        with patch.object(chat_service, '_clients', {'openai': AsyncMock()}):
            mock_client = chat_service._clients['openai']
            mock_completion = AsyncMock()
            mock_completion.choices = [
                MagicMock(message=MagicMock(content="AI response with context"))
            ]
            mock_client.chat.completions.create = AsyncMock(return_value=mock_completion)
            
            response = await chat_service.generate_response(
                messages=messages,
                model_id="gpt-4",
                conversation_id=conversation_id,
                rag_enabled=True
            )
            
            assert response == "AI response with context"
            
            # Verify RAG was called
            rag_service.get_context.assert_called_once_with(
                query="What is AI?",
                conversation_id=conversation_id
            )
            
            # Verify model service was called
            model_service.get_model_info.assert_called_once_with("gpt-4")
    
    @pytest.mark.asyncio
    async def test_error_handling_in_services(self):
        """Test error handling across services."""
        # Mock services that raise errors
        model_service = MagicMock(spec=ModelService)
        model_service.get_model_info = AsyncMock(side_effect=Exception("Model not found"))
        
        rag_service = MagicMock(spec=RAGService)
        
        chat_service = ChatService(
            model_service=model_service,
            rag_service=rag_service
        )
        
        messages = [{"role": "user", "content": "Hello"}]
        
        with pytest.raises(Exception):
            await chat_service.generate_response(
                messages=messages,
                model_id="invalid-model",
                conversation_id=uuid4()
            )
