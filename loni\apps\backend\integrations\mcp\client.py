"""
MCP Client implementation.

This module provides the MCP client for communicating with MCP servers
following the Model Context Protocol specification.
"""

import async<PERSON>
import json
from typing import Any, Dict, List, Optional
from uuid import uuid4

import httpx
from loguru import logger

from .types import (
    MCPRequest, MCPResponse, MCPTool, MCPResource, MCPServerInfo,
    MCPMethods, MCPErrorCodes, MCPToolCall, MCPToolResult
)


class MCPClient:
    """
    MCP Client implementation.
    
    Provides client-side MCP protocol communication for interacting
    with MCP servers.
    """
    
    def __init__(self, server_url: str, timeout: float = 30.0):
        """
        Initialize MCP client.
        
        Args:
            server_url: MCP server URL
            timeout: Request timeout in seconds
        """
        self.server_url = server_url.rstrip('/')
        self.timeout = timeout
        self.session_id: Optional[str] = None
        self.server_info: Optional[MCPServerInfo] = None
        self.available_tools: List[MCPTool] = []
        self.available_resources: List[MCPResource] = []
        
        # HTTP client for communication
        self._client = httpx.AsyncClient(timeout=timeout)
    
    async def __aenter__(self):
        """Async context manager entry."""
        await self.initialize()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        await self.close()
    
    async def initialize(self) -> Dict[str, Any]:
        """
        Initialize connection with MCP server.
        
        Returns:
            Server initialization response
        """
        try:
            request = MCPRequest(
                id=str(uuid4()),
                method=MCPMethods.INITIALIZE,
                params={
                    "client_info": {
                        "name": "loni-mcp-client",
                        "version": "1.0.0"
                    }
                }
            )
            
            response = await self._send_request(request)
            
            if response.error:
                raise Exception(f"Initialization failed: {response.error}")
            
            # Store session information
            result = response.result
            self.session_id = result.get("session_id")
            
            server_info_data = result.get("server_info", {})
            self.server_info = MCPServerInfo(**server_info_data)
            
            # Load available tools and resources
            await self._load_capabilities()
            
            logger.info(f"MCP client initialized with server: {self.server_info.name}")
            return result
            
        except Exception as e:
            logger.error(f"MCP client initialization failed: {e}")
            raise
    
    async def ping(self) -> bool:
        """
        Ping the MCP server.
        
        Returns:
            True if server responds, False otherwise
        """
        try:
            request = MCPRequest(
                id=str(uuid4()),
                method=MCPMethods.PING
            )
            
            response = await self._send_request(request)
            return response.error is None and response.result.get("pong") is True
            
        except Exception as e:
            logger.warning(f"MCP ping failed: {e}")
            return False
    
    async def list_tools(self) -> List[MCPTool]:
        """
        Get list of available tools from server.
        
        Returns:
            List of available tools
        """
        try:
            request = MCPRequest(
                id=str(uuid4()),
                method=MCPMethods.LIST_TOOLS
            )
            
            response = await self._send_request(request)
            
            if response.error:
                raise Exception(f"List tools failed: {response.error}")
            
            tools_data = response.result.get("tools", [])
            tools = [MCPTool(**tool_data) for tool_data in tools_data]
            
            self.available_tools = tools
            return tools
            
        except Exception as e:
            logger.error(f"Failed to list tools: {e}")
            raise
    
    async def call_tool(self, tool_name: str, arguments: Optional[Dict[str, Any]] = None) -> Any:
        """
        Call a tool on the MCP server.
        
        Args:
            tool_name: Name of the tool to call
            arguments: Tool arguments
            
        Returns:
            Tool execution result
        """
        try:
            request = MCPRequest(
                id=str(uuid4()),
                method=MCPMethods.CALL_TOOL,
                params={
                    "name": tool_name,
                    "arguments": arguments or {}
                }
            )
            
            response = await self._send_request(request)
            
            if response.error:
                raise Exception(f"Tool call failed: {response.error}")
            
            return response.result.get("result")
            
        except Exception as e:
            logger.error(f"Tool {tool_name} call failed: {e}")
            raise
    
    async def list_resources(self) -> List[MCPResource]:
        """
        Get list of available resources from server.
        
        Returns:
            List of available resources
        """
        try:
            request = MCPRequest(
                id=str(uuid4()),
                method=MCPMethods.LIST_RESOURCES
            )
            
            response = await self._send_request(request)
            
            if response.error:
                raise Exception(f"List resources failed: {response.error}")
            
            resources_data = response.result.get("resources", [])
            resources = [MCPResource(**resource_data) for resource_data in resources_data]
            
            self.available_resources = resources
            return resources
            
        except Exception as e:
            logger.error(f"Failed to list resources: {e}")
            raise
    
    async def read_resource(self, uri: str) -> Any:
        """
        Read a resource from the MCP server.
        
        Args:
            uri: Resource URI
            
        Returns:
            Resource content
        """
        try:
            request = MCPRequest(
                id=str(uuid4()),
                method=MCPMethods.READ_RESOURCE,
                params={"uri": uri}
            )
            
            response = await self._send_request(request)
            
            if response.error:
                raise Exception(f"Read resource failed: {response.error}")
            
            return response.result.get("content")
            
        except Exception as e:
            logger.error(f"Failed to read resource {uri}: {e}")
            raise
    
    async def get_tool_by_name(self, name: str) -> Optional[MCPTool]:
        """
        Get tool by name.
        
        Args:
            name: Tool name
            
        Returns:
            Tool definition or None if not found
        """
        for tool in self.available_tools:
            if tool.name == name:
                return tool
        return None
    
    async def get_resource_by_uri(self, uri: str) -> Optional[MCPResource]:
        """
        Get resource by URI.
        
        Args:
            uri: Resource URI
            
        Returns:
            Resource definition or None if not found
        """
        for resource in self.available_resources:
            if resource.uri == uri:
                return resource
        return None
    
    async def _load_capabilities(self) -> None:
        """Load server capabilities."""
        try:
            await self.list_tools()
            await self.list_resources()
        except Exception as e:
            logger.warning(f"Failed to load some capabilities: {e}")
    
    async def _send_request(self, request: MCPRequest) -> MCPResponse:
        """
        Send request to MCP server.
        
        Args:
            request: MCP request
            
        Returns:
            MCP response
        """
        try:
            # Prepare request data
            request_data = request.json()
            
            # Send HTTP request
            http_response = await self._client.post(
                f"{self.server_url}/mcp",
                content=request_data,
                headers={
                    "Content-Type": "application/json",
                    "X-Session-ID": self.session_id or ""
                }
            )
            
            http_response.raise_for_status()
            
            # Parse response
            response_data = http_response.json()
            return MCPResponse(**response_data)
            
        except httpx.HTTPError as e:
            logger.error(f"HTTP error in MCP request: {e}")
            raise Exception(f"MCP communication failed: {str(e)}")
        except Exception as e:
            logger.error(f"MCP request failed: {e}")
            raise
    
    async def close(self) -> None:
        """Close the MCP client connection."""
        try:
            await self._client.aclose()
            logger.info("MCP client connection closed")
        except Exception as e:
            logger.warning(f"Error closing MCP client: {e}")
    
    def is_connected(self) -> bool:
        """Check if client is connected to server."""
        return self.session_id is not None and self.server_info is not None


# Example usage
async def example_usage():
    """Example of how to use the MCP client."""
    async with MCPClient("http://localhost:8000") as client:
        # List available tools
        tools = await client.list_tools()
        print(f"Available tools: {[tool.name for tool in tools]}")
        
        # Call a tool
        if tools:
            result = await client.call_tool(
                tools[0].name,
                {"query": "example search", "limit": 5}
            )
            print(f"Tool result: {result}")
        
        # List and read resources
        resources = await client.list_resources()
        if resources:
            content = await client.read_resource(resources[0].uri)
            print(f"Resource content: {content}")
        
        # Test connectivity
        is_alive = await client.ping()
        print(f"Server is alive: {is_alive}")
