{"name": "AI Agent Mastery Prototype", "nodes": [{"parameters": {"options": {}}, "id": "244827ee-f794-468f-af5a-04787c389bdf", "name": "OpenAI Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1, "position": [-1080, 560], "credentials": {"openAiApi": {"id": "05Q6PbnSdyEcu9Ze", "name": "OpenAi account"}}}, {"parameters": {"jsonMode": "expressionData", "jsonData": "={{ $json.data || $json.text || $json.concatenated_data }}", "options": {"metadata": {"metadataValues": [{"name": "=file_id", "value": "={{ $('Set File ID').first().json.file_id }}"}, {"name": "file_title", "value": "={{ $('Set File ID').first().json.file_title }}"}, {"name": "file_url", "value": "={{ $('Set File ID').first().json.file_url }}"}]}}}, "id": "2e60b11a-011f-4fb1-8808-9c401a4429b3", "name": "Default Data Loader", "type": "@n8n/n8n-nodes-langchain.documentDefaultDataLoader", "typeVersion": 1, "position": [1660, 1600]}, {"parameters": {"model": "text-embedding-3-small", "options": {}}, "id": "629f2ec3-1b24-4ede-a52d-6b3bad078a3c", "name": "Embeddings OpenAI1", "type": "@n8n/n8n-nodes-langchain.embeddingsOpenAi", "typeVersion": 1, "position": [1420, 1600], "credentials": {"openAiApi": {"id": "05Q6PbnSdyEcu9Ze", "name": "OpenAi account"}}}, {"parameters": {"content": "## Agent Tools for Agentic RAG", "height": 389, "width": 523, "color": 4}, "id": "3895552d-8135-4e08-88aa-08e274c3c6f7", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-220, 600]}, {"parameters": {"content": "## Tool to Add Google Drive Files to Vector DB", "height": 867, "width": 3373, "color": 5}, "id": "cc7b8d99-c750-4452-95f5-6096059db652", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-1280, 1000]}, {"parameters": {"operation": "download", "fileId": {"__rl": true, "value": "={{ $('Set File ID').item.json.file_id }}", "mode": "id"}, "options": {"googleFileConversion": {"conversion": {"docsToFormat": "text/plain"}}}}, "id": "7c3e72cf-bc30-4afa-84fb-e03b83ea8658", "name": "Download File", "type": "n8n-nodes-base.googleDrive", "typeVersion": 3, "position": [80, 1280], "executeOnce": true, "credentials": {"googleDriveOAuth2Api": {"id": "p411dEzjKc4VbQGi", "name": "Google Drive account"}}}, {"parameters": {"pollTimes": {"item": [{"mode": "everyMinute"}]}, "triggerOn": "specificFolder", "folderToWatch": {"__rl": true, "value": "1HtaIIK3kWwjbwhsmEbtJ-upalxVn3Ek9", "mode": "list", "cachedResultName": "n8n Documents", "cachedResultUrl": "https://drive.google.com/drive/folders/1HtaIIK3kWwjbwhsmEbtJ-upalxVn3Ek9"}, "event": "fileCreated", "options": {}}, "id": "0f4f779a-bc95-4e90-a089-23c9123dadf4", "name": "File Created", "type": "n8n-nodes-base.googleDriveTrigger", "typeVersion": 1, "position": [-1220, 1120], "credentials": {"googleDriveOAuth2Api": {"id": "p411dEzjKc4VbQGi", "name": "Google Drive account"}}}, {"parameters": {"pollTimes": {"item": [{"mode": "everyMinute"}]}, "triggerOn": "specificFolder", "folderToWatch": {"__rl": true, "value": "1HtaIIK3kWwjbwhsmEbtJ-upalxVn3Ek9", "mode": "list", "cachedResultName": "n8n Documents", "cachedResultUrl": "https://drive.google.com/drive/folders/1HtaIIK3kWwjbwhsmEbtJ-upalxVn3Ek9"}, "event": "fileUpdated", "options": {}}, "id": "8fda5d45-550e-42d9-a565-3e35c2689be7", "name": "File Updated", "type": "n8n-nodes-base.googleDriveTrigger", "typeVersion": 1, "position": [-1220, 1280], "credentials": {"googleDriveOAuth2Api": {"id": "p411dEzjKc4VbQGi", "name": "Google Drive account"}}}, {"parameters": {"operation": "text", "options": {}}, "id": "2e342b8f-b59e-4179-ae68-a03e293d20dc", "name": "Extract Document Text", "type": "n8n-nodes-base.extractFromFile", "typeVersion": 1, "position": [860, 1600], "alwaysOutputData": true}, {"parameters": {}, "id": "e470167b-1d18-4639-9d06-4f3bec8c73ff", "name": "Postgres Chat Memory", "type": "@n8n/n8n-nodes-langchain.memoryPostgresChat", "typeVersion": 1, "position": [-920, 560], "notesInFlow": false, "credentials": {"postgres": {"id": "PPVUOCGy8dTN3kdl", "name": "Live Agent Studio Test"}}}, {"parameters": {"operation": "delete", "tableId": "documents", "filterType": "string", "filterString": "=metadata->>file_id=like.*{{ $json.file_id }}*"}, "id": "862efa69-080e-4d7d-ac47-63fe42bc0720", "name": "Delete Old Doc Rows", "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [-600, 1120], "alwaysOutputData": true, "credentials": {"supabaseApi": {"id": "Eu6anmgplJHxqZu2", "name": "Studio Test"}}}, {"parameters": {"assignments": {"assignments": [{"id": "10646eae-ae46-4327-a4dc-9987c2d76173", "name": "file_id", "value": "={{ $json.id }}", "type": "string"}, {"id": "f4536df5-d0b1-4392-bf17-b8137fb31a44", "name": "file_type", "value": "={{ $json.mimeType }}", "type": "string"}, {"id": "77d782de-169d-4a46-8a8e-a3831c04d90f", "name": "file_title", "value": "={{ $json.name }}", "type": "string"}, {"id": "9bde4d7f-e4f3-4ebd-9338-dce1350f9eab", "name": "file_url", "value": "={{ $json.webViewLink }}", "type": "string"}]}, "options": {}}, "id": "b0f9bf22-898c-407e-b366-45345cd0005a", "name": "Set File ID", "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [-820, 1260]}, {"parameters": {"content": "## RAG AI Agent with Cha<PERSON> Interface", "height": 765, "width": 1036}, "id": "1b1457ff-03c5-41fd-bfc4-9e82e60e2397", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-1280, 220]}, {"parameters": {"options": {}}, "id": "2948567d-a176-45b7-9efb-9db18f2c9b9b", "name": "Respond to Webhook", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1.1, "position": [-420, 300]}, {"parameters": {"assignments": {"assignments": [{"id": "9a9a245e-f1a1-4282-bb02-a81ffe629f0f", "name": "chatInput", "value": "={{ $json?.chatInput || $json.body.chatInput }}", "type": "string"}, {"id": "b80831d8-c653-4203-8706-adedfdb98f77", "name": "sessionId", "value": "={{ $json?.sessionId || $json.body.sessionId}}", "type": "string"}]}, "options": {}}, "id": "7fdcc506-5011-4b32-aad5-9e2d4e741766", "name": "<PERSON>", "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [-980, 300]}, {"parameters": {"public": true, "options": {}}, "id": "90aa72c5-d49d-4cbe-8a38-acec7a257dcf", "name": "When chat message received", "type": "@n8n/n8n-nodes-langchain.chatTrigger", "typeVersion": 1.1, "position": [-1240, 300], "webhookId": "d7abb39a-2f82-4293-bd00-c7dee58009c6"}, {"parameters": {"httpMethod": "POST", "path": "4c433857-2d9a-4946-85e3-cb3a449e41fe", "authentication": "headerAuth", "responseMode": "responseNode", "options": {}}, "id": "0649ba45-6ab3-4e68-b81d-12395e5bf0f4", "name": "Webhook", "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [-1240, 500], "webhookId": "4c433857-2d9a-4946-85e3-cb3a449e41fe", "credentials": {"httpHeaderAuth": {"id": "6wzSkRM1jflKXEHm", "name": "<PERSON><PERSON><PERSON>"}}}, {"parameters": {"operation": "pdf", "options": {}}, "id": "ef3e03c7-3f8c-41fc-91ab-0eb576d3e566", "name": "Extract PDF Text", "type": "n8n-nodes-base.extractFromFile", "typeVersion": 1, "position": [860, 1040]}, {"parameters": {"aggregate": "aggregateAllItemData", "options": {}}, "id": "f3accb01-a91c-4a37-b92b-eb7f8b31009f", "name": "Aggregate", "type": "n8n-nodes-base.aggregate", "typeVersion": 1, "position": [900, 1220]}, {"parameters": {"fieldsToSummarize": {"values": [{"aggregation": "concatenate", "field": "data"}]}, "options": {}}, "id": "63835aef-c2bb-4bda-8f70-b0dd9626e618", "name": "Summarize", "type": "n8n-nodes-base.summarize", "typeVersion": 1, "position": [1100, 1300]}, {"parameters": {"promptType": "define", "text": "={{ $json.chatInput }}", "options": {"systemMessage": "=You are an intelligent AI assistant with advanced research and analysis capabilities. You excel at retrieving, processing, and synthesizing information from diverse document types to provide accurate, comprehensive answers. You are intuitive, friendly, and proactive, always aiming to deliver the most relevant information while maintaining clarity and precision.\n\nGoal:\n\nYour goal is to provide accurate, relevant, and well-sourced information by utilizing your suite of tools. You aim to streamline the user's research process, offer insightful analysis, and ensure they receive reliable answers to their queries. You help users by delivering thoughtful, well-researched responses that save them time and enhance their understanding of complex topics.\n\nTool Instructions:\n\n- Always begin with Memory: Before doing anything, use the memory tool to fetch relevant memories. You prioritize using this tool first and you always use it if the answer needs to be personalized to the user in ANY way!\n\n- Document Retrieval Strategy:\nFor general information queries: Use RAG first. Then analyze individual documents if RAG is insufficient.\nFor numerical analysis or data queries: Use SQL on tabular data\n\n- Knowledge Boundaries: Explicitly acknowledge when you cannot find an answer in the available resources.\n\nFor the rest of the tools, use them as necessary based on their descriptions.\n\nOutput Format:\n\nStructure your responses to be clear, concise, and well-organized. Begin with a direct answer to the user's query when possible, followed by supporting information and your reasoning process.\n\nMisc Instructions:\n\n- Query Clarification:\nRequest clarification when queries are ambiguous - but check memories first because that might clarify things.\n\nData Analysis Best Practices:\n- Explain your analytical approach when executing code or SQL queries\nPresent numerical findings with appropriate context and units\n\n- Source Prioritization:\nPrioritize the most recent and authoritative documents when information varies\n\n- Transparency About Limitations:\nClearly state when information appears outdated or incomplete\nAcknowledge when web search might provide more current information than your document corpus"}}, "id": "b6316a71-3e1a-48c7-9aef-23db8ad6bf2e", "name": "RAG AI Agent", "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.6, "position": [-760, 300]}, {"parameters": {"rules": {"values": [{"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 1}, "conditions": [{"leftValue": "={{ $('Set File ID').item.json.file_type }}", "rightValue": "application/pdf", "operator": {"type": "string", "operation": "equals"}}], "combinator": "and"}}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 1}, "conditions": [{"id": "2ae7faa7-a936-4621-a680-60c512163034", "leftValue": "={{ $('Set File ID').item.json.file_type }}", "rightValue": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 1}, "conditions": [{"id": "fc193b06-363b-4699-a97d-e5a850138b0e", "leftValue": "={{ $('Set File ID').item.json.file_type }}", "rightValue": "=application/vnd.google-apps.spreadsheet", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 1}, "conditions": [{"id": "b69f5605-0179-4b02-9a32-e34bb085f82d", "leftValue": "={{ $('Set File ID').item.json.file_type }}", "rightValue": "application/vnd.google-apps.document", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}}]}, "options": {"fallbackOutput": "extra"}}, "id": "5aaadf70-4280-4421-a369-9b2018ed2e66", "name": "Switch", "type": "n8n-nodes-base.switch", "typeVersion": 3, "position": [320, 1260]}, {"parameters": {"mode": "insert", "tableName": {"__rl": true, "value": "documents", "mode": "list", "cachedResultName": "documents"}, "options": {"queryName": "match_documents"}}, "id": "277930cc-f0b5-4c49-83c9-9385c70310f7", "name": "Insert into Supabase Vectorstore", "type": "@n8n/n8n-nodes-langchain.vectorStoreSupabase", "typeVersion": 1, "position": [1600, 1380], "credentials": {"supabaseApi": {"id": "Eu6anmgplJHxqZu2", "name": "Studio Test"}}}, {"parameters": {"operation": "xlsx", "options": {}}, "id": "316f70f2-7430-40a6-8340-89fed93f956c", "name": "Extract from Excel", "type": "n8n-nodes-base.extractFromFile", "typeVersion": 1, "position": [680, 1220]}, {"parameters": {"assignments": {"assignments": [{"id": "f422e2e0-381c-46ea-8f38-3f58c501d8b9", "name": "schema", "value": "={{ $('Extract from Excel').isExecuted ? $('Extract from Excel').first().json.keys().toJsonString() : $('Extract from CSV').first().json.keys().toJsonString() }}", "type": "string"}, {"id": "bb07c71e-5b60-4795-864c-cc3845b6bc46", "name": "data", "value": "={{ $json.concatenated_data }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [1600, 1160], "id": "6d29db22-a13d-435c-9803-bae3ffef76d7", "name": "<PERSON>"}, {"parameters": {"options": {}}, "type": "n8n-nodes-base.extractFromFile", "typeVersion": 1, "position": [680, 1400], "id": "21b5b88b-3217-4766-bb1d-46c3a955ff70", "name": "Extract from CSV"}, {"parameters": {"content": "## Run Each Node Once to Set Up Database Tables", "height": 380, "width": 1040, "color": 3}, "type": "n8n-nodes-base.stickyNote", "position": [-1280, -180], "typeVersion": 1, "id": "9cf0b7e0-e4bb-4a52-8151-e341fa2ff3fd", "name": "Sticky Note3"}, {"parameters": {"operation": "execute<PERSON>uery", "query": "CREATE TABLE document_metadata (\n    id TEXT PRIMARY KEY,\n    title TEXT,\n    url TEXT,\n    created_at TIMESTAMP DEFAULT NOW(),\n    schema TEXT\n);", "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.5, "position": [-720, -60], "id": "959802e3-8884-4b5f-9fee-4b32916bb151", "name": "Create Document Metadata Table", "credentials": {"postgres": {"id": "PPVUOCGy8dTN3kdl", "name": "Live Agent Studio Test"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "CREATE TABLE document_rows (\n    id SERIAL PRIMARY KEY,\n    dataset_id TEXT REFERENCES document_metadata(id),\n    row_data JSONB  -- Store the actual row data\n);", "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.5, "position": [-460, -60], "id": "50aa23f2-65f5-45f8-ae8b-a38708fa93cf", "name": "Create Document Rows Table (for Tabular Data)", "credentials": {"postgres": {"id": "PPVUOCGy8dTN3kdl", "name": "Live Agent Studio Test"}}}, {"parameters": {"descriptionType": "manual", "toolDescription": "Use this tool to fetch all available documents, including the table schema if the file is a CSV or Excel file.", "operation": "select", "schema": {"__rl": true, "mode": "list", "value": "public"}, "table": {"__rl": true, "value": "document_metadata", "mode": "list", "cachedResultName": "document_metadata"}, "returnAll": true, "options": {}}, "type": "n8n-nodes-base.postgresTool", "typeVersion": 2.5, "position": [-180, 840], "id": "d2aa69c8-4b2d-4128-8445-9127b6e0da1e", "name": "List Documents", "credentials": {"postgres": {"id": "PPVUOCGy8dTN3kdl", "name": "Live Agent Studio Test"}}}, {"parameters": {"descriptionType": "manual", "toolDescription": "Given a file ID, fetches the text from the document.", "operation": "execute<PERSON>uery", "query": "SELECT \n    string_agg(content, ' ') as document_text\nFROM documents\n  WHERE metadata->>'file_id' = $1\nGROUP BY metadata->>'file_id';", "options": {"queryReplacement": "={{ $fromAI('file_id') }}"}}, "type": "n8n-nodes-base.postgresTool", "typeVersion": 2.5, "position": [-100, 720], "id": "1f45cb12-db27-4fde-ba8d-904c7e48098b", "name": "Get File Contents", "credentials": {"postgres": {"id": "PPVUOCGy8dTN3kdl", "name": "Live Agent Studio Test"}}}, {"parameters": {"descriptionType": "manual", "toolDescription": "Run a SQL query - use this to query from the document_rows table once you know the file ID you are querying. dataset_id is the file_id and you are always using the row_data for filtering, which is a jsonb field that has all the keys from the file schema given in the document_metadata table.\n\nExample query:\n\nSELECT AVG((row_data->>'revenue')::numeric)\nFROM document_rows\nWHERE dataset_id = '123';\n\nExample query 2:\n\nSELECT \n    row_data->>'category' as category,\n    SUM((row_data->>'sales')::numeric) as total_sales\nFROM dataset_rows\nWHERE dataset_id = '123'\nGROUP BY row_data->>'category';", "operation": "execute<PERSON>uery", "query": "{{ $fromAI('sql_query') }}", "options": {}}, "type": "n8n-nodes-base.postgresTool", "typeVersion": 2.5, "position": [0, 840], "id": "9e8e4433-5430-4c31-9a45-29de1d51d8f8", "name": "Query Document Rows", "credentials": {"postgres": {"id": "PPVUOCGy8dTN3kdl", "name": "Live Agent Studio Test"}}}, {"parameters": {"options": {}}, "type": "@n8n/n8n-nodes-langchain.embeddingsOpenAi", "typeVersion": 1.2, "position": [160, 840], "id": "2fbc5005-6cf0-430d-ba2b-7e15e336671b", "name": "Embeddings OpenAI2", "credentials": {"openAiApi": {"id": "05Q6PbnSdyEcu9Ze", "name": "OpenAi account"}}}, {"parameters": {"options": {"reset": false}}, "type": "n8n-nodes-base.splitInBatches", "typeVersion": 3, "position": [-1000, 1120], "id": "29ba8913-7a64-4f59-a862-ab25ea4539f8", "name": "Loop Over Items"}, {"parameters": {"operation": "execute<PERSON>uery", "query": "-- Enable the pgvector extension to work with embedding vectors\ncreate extension vector;\n\n-- Create a table to store your documents\ncreate table documents (\n  id bigserial primary key,\n  content text, -- corresponds to Document.pageContent\n  metadata jsonb, -- corresponds to Document.metadata\n  embedding vector(1536) -- 1536 works for OpenAI embeddings, change if needed\n);\n\n-- Create a function to search for documents\ncreate function match_documents (\n  query_embedding vector(1536),\n  match_count int default null,\n  filter jsonb DEFAULT '{}'\n) returns table (\n  id bigint,\n  content text,\n  metadata jsonb,\n  similarity float\n)\nlanguage plpgsql\nas $$\n#variable_conflict use_column\nbegin\n  return query\n  select\n    id,\n    content,\n    metadata,\n    1 - (documents.embedding <=> query_embedding) as similarity\n  from documents\n  where metadata @> filter\n  order by documents.embedding <=> query_embedding\n  limit match_count;\nend;\n$$;", "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.5, "position": [-1220, -60], "id": "9ca7c47d-7b1c-407b-ba87-8f05fccd003b", "name": "Create Documents Table and Match Function", "credentials": {"postgres": {"id": "PPVUOCGy8dTN3kdl", "name": "Live Agent Studio Test"}}}, {"parameters": {"operation": "delete", "tableId": "document_rows", "filters": {"conditions": [{"keyName": "dataset_id", "condition": "eq", "keyValue": "={{ $('Set File ID').item.json.file_id }}"}]}}, "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [-380, 1280], "id": "9b6e7d04-86ba-42ab-8312-f80d057b10be", "name": "Delete Old Data Rows", "alwaysOutputData": true, "executeOnce": true, "credentials": {"supabaseApi": {"id": "Eu6anmgplJHxqZu2", "name": "Studio Test"}}}, {"parameters": {"operation": "upsert", "schema": {"__rl": true, "mode": "list", "value": "public"}, "table": {"__rl": true, "value": "document_metadata", "mode": "list", "cachedResultName": "document_metadata"}, "columns": {"mappingMode": "defineBelow", "value": {"id": "={{ $('Set File ID').item.json.file_id }}", "title": "={{ $('Set File ID').item.json.file_title }}", "url": "={{ $('Set File ID').item.json.file_url }}"}, "matchingColumns": ["id"], "schema": [{"id": "id", "displayName": "id", "required": true, "defaultMatch": true, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "title", "displayName": "title", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": false}, {"id": "url", "displayName": "url", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": false, "removed": false}, {"id": "created_at", "displayName": "created_at", "required": false, "defaultMatch": false, "display": true, "type": "dateTime", "canBeUsedToMatch": false}, {"id": "schema", "displayName": "schema", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": false, "removed": true}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.5, "position": [-140, 1140], "id": "9318949d-7223-4591-89cb-f18549a9c308", "name": "Insert Document Metadata", "executeOnce": true, "credentials": {"postgres": {"id": "PPVUOCGy8dTN3kdl", "name": "Live Agent Studio Test"}}}, {"parameters": {"schema": {"__rl": true, "mode": "list", "value": "public"}, "table": {"__rl": true, "value": "document_rows", "mode": "list", "cachedResultName": "document_rows"}, "columns": {"mappingMode": "defineBelow", "value": {"dataset_id": "={{ $('Set File ID').item.json.file_id }}", "row_data": "={{ $json.toJsonString().replaceAll(/'/g, \"''\") }}"}, "matchingColumns": ["id"], "schema": [{"id": "id", "displayName": "id", "required": false, "defaultMatch": true, "display": true, "type": "number", "canBeUsedToMatch": true, "removed": true}, {"id": "dataset_id", "displayName": "dataset_id", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "row_data", "displayName": "row_data", "required": false, "defaultMatch": false, "display": true, "type": "object", "canBeUsedToMatch": true}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.5, "position": [900, 1400], "id": "93217ffa-092d-4709-b009-7ae5c326cf17", "name": "Insert Table Rows", "credentials": {"postgres": {"id": "PPVUOCGy8dTN3kdl", "name": "Live Agent Studio Test"}}}, {"parameters": {"operation": "upsert", "schema": {"__rl": true, "mode": "list", "value": "public"}, "table": {"__rl": true, "value": "document_metadata", "mode": "list", "cachedResultName": "document_metadata"}, "columns": {"mappingMode": "defineBelow", "value": {"id": "={{ $('Set File ID').item.json.file_id }}", "schema": "={{ $json.schema }}"}, "matchingColumns": ["id"], "schema": [{"id": "id", "displayName": "id", "required": true, "defaultMatch": true, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "title", "displayName": "title", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": false, "removed": true}, {"id": "url", "displayName": "url", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": false, "removed": true}, {"id": "created_at", "displayName": "created_at", "required": false, "defaultMatch": false, "display": true, "type": "dateTime", "canBeUsedToMatch": false}, {"id": "schema", "displayName": "schema", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": false, "removed": false}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.5, "position": [1800, 1160], "id": "e38d6d04-03d5-4d22-91f5-a21a36b68bca", "name": "Update <PERSON><PERSON>a for Document Metadata", "credentials": {"postgres": {"id": "PPVUOCGy8dTN3kdl", "name": "Live Agent Studio Test"}}}, {"parameters": {"chunkSize": 400, "options": {}}, "type": "@n8n/n8n-nodes-langchain.textSplitterRecursiveCharacterTextSplitter", "typeVersion": 1, "position": [1560, 1720], "id": "3938a1c5-4248-43c5-a0d1-9443b12d8932", "name": "Recursive Character Text Splitter"}, {"parameters": {"content": "## Save Long Term Memories", "height": 360, "width": 1620, "color": 6}, "type": "n8n-nodes-base.stickyNote", "position": [-220, 220], "typeVersion": 1, "id": "c96cd339-e677-44b7-b2e4-ab2332183b3c", "name": "Sticky Note4"}, {"parameters": {"jsonSchemaExample": "{\n\t\"memories\": [\"Memory 1\", \"Memory 2\", \"Memory 3\"]\n}"}, "type": "@n8n/n8n-nodes-langchain.outputParserStructured", "typeVersion": 1.2, "position": [60, 440], "id": "0f5e90ac-8c55-4539-8df3-85b0252ba3d3", "name": "Structured Output Parser"}, {"parameters": {"operation": "execute<PERSON>uery", "query": "-- Create a table to store your documents\ncreate table memories (\n  id bigserial primary key,\n  content text, -- corresponds to Document.pageContent\n  metadata jsonb, -- corresponds to Document.metadata\n  embedding vector(1536) -- 1536 works for OpenAI embeddings, change if needed\n);\n\n-- Create a function to search for documents\ncreate function match_memories (\n  query_embedding vector(1536),\n  match_count int default null,\n  filter jsonb DEFAULT '{}'\n) returns table (\n  id bigint,\n  content text,\n  metadata jsonb,\n  similarity float\n)\nlanguage plpgsql\nas $$\n#variable_conflict use_column\nbegin\n  return query\n  select\n    id,\n    content,\n    metadata,\n    1 - (memories.embedding <=> query_embedding) as similarity\n  from memories\n  where metadata @> filter\n  order by memories.embedding <=> query_embedding\n  limit match_count;\nend;\n$$;", "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.5, "position": [-960, -60], "id": "bc331c91-bb4c-4244-bdd4-0523d3f0bfc0", "name": "Create Memories Table and Match Function", "credentials": {"postgres": {"id": "PPVUOCGy8dTN3kdl", "name": "Live Agent Studio Test"}}}, {"parameters": {"options": {}}, "type": "@n8n/n8n-nodes-langchain.embeddingsOpenAi", "typeVersion": 1.2, "position": [960, 820], "id": "********-4a58-4664-9fb8-bfd9cf655cc1", "name": "Embeddings OpenAI", "credentials": {"openAiApi": {"id": "05Q6PbnSdyEcu9Ze", "name": "OpenAi account"}}}, {"parameters": {"jsonMode": "expressionData", "jsonData": "={{ $('Basic LLM Chain').item.json.output.memories }}", "options": {"metadata": {"metadataValues": [{"name": "timestamp", "value": "={{ $now }}"}]}}}, "type": "@n8n/n8n-nodes-langchain.documentDefaultDataLoader", "typeVersion": 1, "position": [1060, 680], "id": "ece0de79-e2ad-455e-a2bf-a5c5d0e41888", "name": "Default Data Loader1"}, {"parameters": {"chunkSize": 400}, "type": "@n8n/n8n-nodes-langchain.textSplitterCharacterTextSplitter", "typeVersion": 1, "position": [1160, 840], "id": "364be302-c80e-4b69-996b-ca48612dd9e3", "name": "Character Text Splitter"}, {"parameters": {"assignments": {"assignments": [{"id": "636ddfda-13fd-4f21-ad30-fa21472ed9e7", "name": "output", "value": "={{ $('RAG AI Agent').item.json.output }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [1180, 420], "id": "2b3e83a6-3f2a-45b3-b609-4ab3689fb738", "name": "Edit Fields1", "executeOnce": true}, {"parameters": {"options": {}}, "type": "@n8n/n8n-nodes-langchain.embeddingsOpenAi", "typeVersion": 1.2, "position": [-440, 820], "id": "4bc60f60-93d0-4361-b967-78c498a63566", "name": "Embeddings OpenAI3", "credentials": {"openAiApi": {"id": "05Q6PbnSdyEcu9Ze", "name": "OpenAi account"}}}, {"parameters": {"mode": "retrieve-as-tool", "toolName": "memories", "toolDescription": "Use this tool to fetch memories from previous conversations if needed to answer a question for the user or continue the conversation.", "tableName": {"__rl": true, "value": "memories", "mode": "list", "cachedResultName": "memories"}, "options": {"queryName": "match_memories"}}, "type": "@n8n/n8n-nodes-langchain.vectorStoreSupabase", "typeVersion": 1, "position": [-540, 660], "id": "7527f3ef-d502-49bf-9861-42b6479df941", "name": "Retrieve Memories Tool", "credentials": {"supabaseApi": {"id": "Eu6anmgplJHxqZu2", "name": "Studio Test"}}}, {"parameters": {"mode": "retrieve-as-tool", "toolName": "documents", "toolDescription": "Use RAG to look up information in the knowledgebase.", "tableName": {"__rl": true, "value": "documents", "mode": "list", "cachedResultName": "documents"}, "options": {"queryName": "match_documents"}}, "type": "@n8n/n8n-nodes-langchain.vectorStoreSupabase", "typeVersion": 1, "position": [40, 700], "id": "effe0626-941e-4022-8f51-7f34f5cae8eb", "name": "Document RAG Tool", "credentials": {"supabaseApi": {"id": "Eu6anmgplJHxqZu2", "name": "Studio Test"}}}, {"parameters": {"promptType": "define", "text": "=You are an expert at maintaining a memory database for a user, making sure that there aren't duplicate or contradictory memories.\n\nThe current memories being added to the database are:\n\n{{ $json.output.memories }}\n\nYour job is to:\n- Search the memory database to find any duplicate or contradictory memories. The tool will return memories from the search but you have to decide if they are duplicates or contradictory of the memories being added or not.\n- If you find any duplicate/contradictory memories, output a list of memories to delete (the content of the memory). Otherwise, if you find no memories to delete, output an empty list. You determine based on all the memories returned from the search if they are duplicates/contradictory. The memories you output have to match EXACTLY with the content received.", "hasOutputParser": true, "options": {}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.7, "position": [340, 280], "id": "d376aa6d-4757-4983-94c5-1dc146091bd7", "name": "Memory Agent"}, {"parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4o-mini"}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1.2, "position": [420, 800], "id": "a5eca3ac-83bd-42b0-aabd-08e7ffec8d19", "name": "OpenAI Chat Model2", "credentials": {"openAiApi": {"id": "05Q6PbnSdyEcu9Ze", "name": "OpenAi account"}}}, {"parameters": {"mode": "insert", "tableName": {"__rl": true, "value": "memories", "mode": "list", "cachedResultName": "memories"}, "options": {"queryName": "match_memories"}}, "type": "@n8n/n8n-nodes-langchain.vectorStoreSupabase", "typeVersion": 1, "position": [880, 280], "id": "b75e5e3d-9e01-413a-91cb-9213872da4f0", "name": "Supabase Vector Store", "executeOnce": true, "credentials": {"supabaseApi": {"id": "Eu6anmgplJHxqZu2", "name": "Studio Test"}}}, {"parameters": {"options": {}}, "type": "@n8n/n8n-nodes-langchain.embeddingsOpenAi", "typeVersion": 1.2, "position": [640, 820], "id": "90f42fd0-4a88-49e4-9ae8-624e829c3505", "name": "Embeddings OpenAI4", "credentials": {"openAiApi": {"id": "05Q6PbnSdyEcu9Ze", "name": "OpenAi account"}}}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "cb436122-f430-4ea5-98d8-b99e8c52eeaa", "leftValue": "={{ $json.output.memories }}", "rightValue": "", "operator": {"type": "array", "operation": "notEmpty", "singleValue": true}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [160, 280], "id": "10a002a8-d219-427e-a606-fcb966d16779", "name": "If"}, {"parameters": {"content": "## Memory Searcher", "height": 380, "width": 520, "color": 6}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [340, 600], "id": "f7330f18-4d8a-4350-b148-1545943c4724", "name": "Sticky Note5"}, {"parameters": {"promptType": "define", "text": "=You are an expert at extract key information from conversations to store as long term memories for RAG.\n\nThe user said:\n\n{{ $('Edit Fields').item.json.chatInput }}\n\nYour goal is to output a list of key memories to extract from the conversation.\n\nJust output a empty list if there is nothing worth saving, like if the user is just asking a basic question without providing additional context.", "hasOutputParser": true}, "type": "@n8n/n8n-nodes-langchain.chainLlm", "typeVersion": 1.5, "position": [-180, 280], "id": "a6831d07-8be5-42c0-9aa6-************", "name": "Basic LLM Chain"}, {"parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4o-mini"}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1.2, "position": [-120, 440], "id": "58f91e51-73fa-42f0-8a37-af69e405cde0", "name": "OpenAI Chat Model1", "credentials": {"openAiApi": {"id": "05Q6PbnSdyEcu9Ze", "name": "OpenAi account"}}}, {"parameters": {"content": "## Memory Chunking & Embedding", "height": 380, "width": 520, "color": 6}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [880, 600], "id": "d303e64f-b6e2-4d1f-9b31-3b9ed970124d", "name": "Sticky Note6"}, {"parameters": {"mode": "retrieve-as-tool", "toolName": "memories", "toolDescription": "Use this tool to retrieve memories from the database. You specifically want to use this to find similar memories to what you are planning on adding so that you can do what it takes to remove duplicates.", "tableName": {"__rl": true, "value": "memories", "mode": "list", "cachedResultName": "memories"}, "topK": 8, "options": {"queryName": "match_memories"}}, "type": "@n8n/n8n-nodes-langchain.vectorStoreSupabase", "typeVersion": 1, "position": [520, 680], "id": "4feb9f3b-234f-4d94-89c4-47e19d5103eb", "name": "Similar Memory Searcher", "credentials": {"supabaseApi": {"id": "Eu6anmgplJHxqZu2", "name": "Studio Test"}}}, {"parameters": {"jsonSchemaExample": "[\"Memory 1\", \"Memory 2\", \"Memory 3\"]"}, "type": "@n8n/n8n-nodes-langchain.outputParserStructured", "typeVersion": 1.2, "position": [640, 440], "id": "4bf5c6f3-a0fd-426d-a6b0-910622072c38", "name": "Structured Output Parser1"}, {"parameters": {"operation": "execute<PERSON>uery", "query": "DELETE FROM memories\nWHERE content IN ({{ $json.output.concat($('Basic LLM Chain').item.json.output.memories).map(item => `'${item.replace(/'/g, \"''\")}'`).join(', ') || '__IMPOSSIBLE_VALUE__' }})", "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.5, "position": [680, 280], "id": "dd408846-7373-4260-ac29-93f584ababd8", "name": "Postgres", "credentials": {"postgres": {"id": "PPVUOCGy8dTN3kdl", "name": "Live Agent Studio Test"}}}, {"parameters": {"name": "execute_code", "description": "Call this tool to execute JavaScript code that you create with parameters that you specify too. All code must be a single line and must be JavaScript code.", "jsCode": "/**\n * Execute arbitrary JavaScript code and capture its console output\n * @param {string} codeString - JavaScript code to execute\n * @returns {string} The captured console output\n */\nfunction executeCode(codeString) {\n  // Save the original console.log function\n  const originalLog = console.log;\n  \n  // Create an array to store the output\n  const outputLines = [];\n  \n  // Override console.log to capture output\n  console.log = function(...args) {\n    // Convert all arguments to strings and join them\n    const output = args.map(arg => \n      typeof arg === 'object' ? JSON.stringify(arg) : String(arg)\n    ).join(' ');\n    \n    // Add to our captured output\n    outputLines.push(output);\n  };\n  \n  // Variable to store the result\n  let result;\n  \n  try {\n    // Execute the code\n    result = eval(codeString);\n  } catch (error) {\n    // Restore the original console.log\n    console.log = originalLog;\n    return `Error executing code: ${error.message}`;\n  } finally {\n    // Restore the original console.log\n    console.log = originalLog;\n  }\n  \n  // Join all captured output lines\n  const output = outputLines.join('\\n');\n  \n  // If there's a result but no console output, return the result\n  if (output === '' && result !== undefined) {\n    return String(result);\n  }\n  \n  return output;\n}\n\nreturn executeCode(query.code_to_execute);", "specifyInputSchema": true, "jsonSchemaExample": "{\n\t\"code_to_execute\": \"console.log('test')\"\n}"}, "type": "@n8n/n8n-nodes-langchain.toolCode", "typeVersion": 1.1, "position": [-620, 780], "id": "f8d9a6f3-1a59-40e7-a466-064abc0b54b1", "name": "Code Tool"}, {"parameters": {"content": "## Workflow Agent Tools", "height": 380, "width": 1100, "color": 4}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-220, -180], "id": "ac44e48f-5107-49e9-99c8-551959b50dd5", "name": "Sticky Note7"}, {"parameters": {"name": "web_search", "description": "Call this tool to do an advanced web search based on a query you define.", "workflowId": {"__rl": true, "value": "={{ $workflow.id }}", "mode": "id"}, "workflowInputs": {"mappingMode": "defineBelow", "value": {"query": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('query', ``, 'string') }}", "tool_type": "web_search"}, "matchingColumns": [], "schema": [{"id": "tool_type", "displayName": "tool_type", "required": false, "defaultMatch": false, "display": true, "canBeUsedToMatch": true, "type": "string", "removed": false}, {"id": "query", "displayName": "query", "required": false, "defaultMatch": false, "display": true, "canBeUsedToMatch": true, "type": "string", "removed": false}], "attemptToConvertTypes": false, "convertFieldsToString": false}}, "type": "@n8n/n8n-nodes-langchain.toolWorkflow", "typeVersion": 2, "position": [-860, 720], "id": "d1741351-2233-4773-83d2-54b74d5e927a", "name": "Web Search Tool"}, {"parameters": {"rules": {"values": [{"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"leftValue": "={{ $json.tool_type }}", "rightValue": "image_analysis", "operator": {"type": "string", "operation": "equals"}, "id": "b22be562-3277-48fc-87be-f3a3930f7fd6"}], "combinator": "and"}}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "a50a5a5a-cc87-4654-97ea-05090efdb416", "leftValue": "={{ $json.tool_type }}", "rightValue": "web_search", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}}]}, "options": {"fallbackOutput": "none"}}, "type": "n8n-nodes-base.switch", "typeVersion": 3.2, "position": [80, -60], "id": "b5f2a351-683e-4d38-959d-087b5990d21b", "name": "Determine Tool Type"}, {"parameters": {"url": "=https://api.search.brave.com/res/v1/web/search?q={{ $('Tool Start').item.json.query }} }}&summary=1", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Accept", "value": "application/json"}, {"name": "Accept-Encoding", "value": "gzip"}]}, "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [360, 40], "id": "5368b460-7c99-4846-9b78-55a9bc37513e", "name": "Brave Web Search", "credentials": {"httpHeaderAuth": {"id": "vQywoUkt33Kl6bxG", "name": "Brave API"}}}, {"parameters": {"workflowInputs": {"values": [{"name": "tool_type"}, {"name": "query"}, {"name": "image_url"}]}}, "type": "n8n-nodes-base.executeWorkflowTrigger", "typeVersion": 1.1, "position": [-140, -60], "id": "b05d16b0-2d0d-4bc2-a0f9-8ea0823de01d", "name": "Tool Start"}, {"parameters": {"url": "=https://api.search.brave.com/res/v1/summarizer/search?key={{ $json.summarizer.key }}&entity_info=1", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Accept", "value": "application/json"}, {"name": "Accept-Encoding", "value": "gzip"}]}, "options": {}}, "id": "0988e884-e3cb-4a5b-8dc5-8b1dd3297f12", "name": "Summarize Web Research", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [620, 40], "credentials": {"httpHeaderAuth": {"id": "vQywoUkt33Kl6bxG", "name": "Brave API"}}}, {"parameters": {"operation": "download", "fileId": {"__rl": true, "value": "={{ $json.image_url }}", "mode": "url"}, "options": {"googleFileConversion": {"conversion": {"docsToFormat": "text/plain"}}}}, "id": "254f1d7f-bc77-453b-971e-9db4a7af975d", "name": "Download File1", "type": "n8n-nodes-base.googleDrive", "typeVersion": 3, "position": [360, -140], "executeOnce": true, "credentials": {"googleDriveOAuth2Api": {"id": "p411dEzjKc4VbQGi", "name": "Google Drive account"}}}, {"parameters": {"resource": "image", "operation": "analyze", "modelId": {"__rl": true, "value": "gpt-4o-mini", "mode": "list", "cachedResultName": "GPT-4O-MINI"}, "text": "={{ $('Tool Start').item.json.query }}", "inputType": "base64", "options": {}}, "type": "@n8n/n8n-nodes-langchain.openAi", "typeVersion": 1.8, "position": [620, -140], "id": "d4ae6990-111f-448b-b7ef-5a0306b419e1", "name": "OpenAI", "credentials": {"openAiApi": {"id": "05Q6PbnSdyEcu9Ze", "name": "OpenAi account"}}}, {"parameters": {"name": "image_analysis", "description": "Call this tool to analyze an image based on an image URL that you supply as image_url. The image URL needs to be a URL to a Google Drive image. Also supply query, which is the prompt to the LLM to extract information from the image.\n\nUse the list_documents tool to get the URL for the image.", "workflowId": {"__rl": true, "value": "={{ $workflow.id }}", "mode": "id"}, "workflowInputs": {"mappingMode": "defineBelow", "value": {"query": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('query', ``, 'string') }}", "tool_type": "image_analysis", "image_url": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('image_url', ``, 'string') }}"}, "matchingColumns": [], "schema": [{"id": "tool_type", "displayName": "tool_type", "required": false, "defaultMatch": false, "display": true, "canBeUsedToMatch": true, "type": "string", "removed": false}, {"id": "query", "displayName": "query", "required": false, "defaultMatch": false, "display": true, "canBeUsedToMatch": true, "type": "string", "removed": false}, {"id": "image_url", "displayName": "image_url", "required": false, "defaultMatch": false, "display": true, "canBeUsedToMatch": true, "type": "string", "removed": false}], "attemptToConvertTypes": false, "convertFieldsToString": false}}, "type": "@n8n/n8n-nodes-langchain.toolWorkflow", "typeVersion": 2, "position": [-720, 660], "id": "f695d29e-77d9-417a-88cc-ed9fe7e0e3b1", "name": "Image Analysis Tool"}, {"parameters": {"content": "# Dynamous AI Agent Mastery - Cloud n8n Agent\nThis workflow implements a powerful AI agent leveraging cloud services through n8n. Use this as a template to build AI agents that utilize state-of-the-art cloud LLMs and have the core set of tools used in most agents!\n\n## Features\n- **Cloud LLM Integration**: Uses OpenAI models for high-quality conversations and embeddings. The LLM provider can be switched easily to something like Anthropic or OpenRouter too!\n- **Agentic RAG**: Google Drive integration and cloud vector storage for knowledge retrieval\n- **Long-term Memory**: Persistent conversation memory through cloud Supabase\n- **Web Search**: Brave API for comprehensive web search capabilities\n- **Code Execution**: Generates and runs JavaScript code\n- **Image Analysis**: Processes images with vision-capable cloud LLMs\n- **Google Drive Integration**: Automatically processes files from your Drive for knowledge retrieval\n\n## Setup Requirements\n1. **Cloud Services**:\n   - Supabase project\n   - OpenAI API key (or the key for another provider if you switch)\n   - Brave API key\n   - Google Drive API credentials\n2. **Environment Configuration**:\n   - Set up credentials in n8n for OpenAI, Supabase, Brave, and Google Drive\n   - Set appropriate model selections in the workflow (gpt-4o-mini, text-embedding-3-small, etc.)\n3. **Database Setup**:\n   - Run the nodes in the top left to set up the database tables (just have to do this once)\n   - Standard dimension of 1536 for OpenAI embeddings\n\n## Workflow Structure\nThe RAG pipeline (bottom part of the workflow in blue) is responsible for syncing a Google Drive folder with the Supabase knowledge base. Make sure the workflow is toggled to active in the top right for this pipeline to work!\n\nThe primary agent is in the yellow box in the middle of the workflow. All of the tools are connected either directly to the agent (in the same box or agentic RAG in the middle green box) or as \"sub-workflows\" which are in the green top box.\n\nThe process of creating long term memories is in the purple boxes.\n\n## Key Differentiators for the Cloud Implementation (Compared to Local)\n- Access to state-of-the-art LLM capabilities (GPT-4o, Claude 3.7, etc.)\n- Reduced computational load on your machine\n- Google Drive integration for seamless document processing\n- Scalable infrastructure that can handle growing data needs\n- Comprehensive web search via Brave API", "height": 1240, "width": 640, "color": 5}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-1940, 220], "id": "487ada93-caa0-4a67-b9b1-ac1b30ea19ef", "name": "Sticky Note8"}], "pinData": {"Tool Start": [{"json": {"tool_type": "image_analysis", "query": "https://drive.google.com/file/d/1vnjEMW9XKgWwmy17VjZO8ci2Mi7RO_9e/view?usp=drive_link"}}]}, "connections": {"OpenAI Chat Model": {"ai_languageModel": [[{"node": "RAG AI Agent", "type": "ai_languageModel", "index": 0}]]}, "Download File": {"main": [[{"node": "Switch", "type": "main", "index": 0}]]}, "File Created": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}]]}, "Extract Document Text": {"main": [[{"node": "Insert into Supabase Vectorstore", "type": "main", "index": 0}]]}, "Embeddings OpenAI1": {"ai_embedding": [[{"node": "Insert into Supabase Vectorstore", "type": "ai_embedding", "index": 0}]]}, "Default Data Loader": {"ai_document": [[{"node": "Insert into Supabase Vectorstore", "type": "ai_document", "index": 0}]]}, "Postgres Chat Memory": {"ai_memory": [[{"node": "RAG AI Agent", "type": "ai_memory", "index": 0}]]}, "Delete Old Doc Rows": {"main": [[{"node": "Delete Old Data Rows", "type": "main", "index": 0}]]}, "Set File ID": {"main": [[{"node": "Delete Old Doc Rows", "type": "main", "index": 0}]]}, "File Updated": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}]]}, "Edit Fields": {"main": [[{"node": "RAG AI Agent", "type": "main", "index": 0}]]}, "When chat message received": {"main": [[{"node": "<PERSON>", "type": "main", "index": 0}]]}, "Webhook": {"main": [[{"node": "<PERSON>", "type": "main", "index": 0}]]}, "Extract PDF Text": {"main": [[{"node": "Insert into Supabase Vectorstore", "type": "main", "index": 0}]]}, "Aggregate": {"main": [[{"node": "Summarize", "type": "main", "index": 0}]]}, "Summarize": {"main": [[{"node": "<PERSON>", "type": "main", "index": 0}, {"node": "Insert into Supabase Vectorstore", "type": "main", "index": 0}]]}, "RAG AI Agent": {"main": [[{"node": "Respond to Webhook", "type": "main", "index": 0}]]}, "Switch": {"main": [[{"node": "Extract PDF Text", "type": "main", "index": 0}], [{"node": "Extract from Excel", "type": "main", "index": 0}], [{"node": "Extract from CSV", "type": "main", "index": 0}], [{"node": "Extract Document Text", "type": "main", "index": 0}], [{"node": "Extract Document Text", "type": "main", "index": 0}]]}, "Extract from Excel": {"main": [[{"node": "Aggregate", "type": "main", "index": 0}, {"node": "Insert Table Rows", "type": "main", "index": 0}]]}, "Set Schema": {"main": [[{"node": "Update <PERSON><PERSON>a for Document Metadata", "type": "main", "index": 0}]]}, "Extract from CSV": {"main": [[{"node": "Aggregate", "type": "main", "index": 0}, {"node": "Insert Table Rows", "type": "main", "index": 0}]]}, "Create Document Metadata Table": {"main": [[]]}, "List Documents": {"ai_tool": [[{"node": "RAG AI Agent", "type": "ai_tool", "index": 0}]]}, "Get File Contents": {"ai_tool": [[{"node": "RAG AI Agent", "type": "ai_tool", "index": 0}]]}, "Query Document Rows": {"ai_tool": [[{"node": "RAG AI Agent", "type": "ai_tool", "index": 0}]]}, "Embeddings OpenAI2": {"ai_embedding": [[{"node": "Document RAG Tool", "type": "ai_embedding", "index": 0}]]}, "Loop Over Items": {"main": [[], [{"node": "Set File ID", "type": "main", "index": 0}]]}, "Insert into Supabase Vectorstore": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}]]}, "Create Documents Table and Match Function": {"main": [[]]}, "Delete Old Data Rows": {"main": [[{"node": "Insert Document Metadata", "type": "main", "index": 0}]]}, "Insert Document Metadata": {"main": [[{"node": "Download File", "type": "main", "index": 0}]]}, "Update Schema for Document Metadata": {"main": [[]]}, "Recursive Character Text Splitter": {"ai_textSplitter": [[{"node": "Default Data Loader", "type": "ai_textSplitter", "index": 0}]]}, "Respond to Webhook": {"main": [[{"node": "Basic LLM Chain", "type": "main", "index": 0}]]}, "Structured Output Parser": {"ai_outputParser": [[{"node": "Basic LLM Chain", "type": "ai_outputParser", "index": 0}]]}, "Embeddings OpenAI": {"ai_embedding": [[{"node": "Supabase Vector Store", "type": "ai_embedding", "index": 0}]]}, "Default Data Loader1": {"ai_document": [[{"node": "Supabase Vector Store", "type": "ai_document", "index": 0}]]}, "Character Text Splitter": {"ai_textSplitter": [[{"node": "Default Data Loader1", "type": "ai_textSplitter", "index": 0}]]}, "Embeddings OpenAI3": {"ai_embedding": [[{"node": "Retrieve Memories Tool", "type": "ai_embedding", "index": 0}]]}, "Retrieve Memories Tool": {"ai_tool": [[{"node": "RAG AI Agent", "type": "ai_tool", "index": 0}]]}, "Document RAG Tool": {"ai_tool": [[{"node": "RAG AI Agent", "type": "ai_tool", "index": 0}]]}, "OpenAI Chat Model2": {"ai_languageModel": [[{"node": "Memory Agent", "type": "ai_languageModel", "index": 0}]]}, "Supabase Vector Store": {"ai_tool": [[]], "main": [[{"node": "Edit Fields1", "type": "main", "index": 0}]]}, "Memory Agent": {"main": [[{"node": "Postgres", "type": "main", "index": 0}]]}, "Embeddings OpenAI4": {"ai_embedding": [[{"node": "Similar Memory Searcher", "type": "ai_embedding", "index": 0}]]}, "If": {"main": [[{"node": "Memory Agent", "type": "main", "index": 0}], [{"node": "Edit Fields1", "type": "main", "index": 0}]]}, "Basic LLM Chain": {"main": [[{"node": "If", "type": "main", "index": 0}]]}, "OpenAI Chat Model1": {"ai_languageModel": [[{"node": "Basic LLM Chain", "type": "ai_languageModel", "index": 0}]]}, "Similar Memory Searcher": {"ai_tool": [[{"node": "Memory Agent", "type": "ai_tool", "index": 0}]]}, "Structured Output Parser1": {"ai_outputParser": [[{"node": "Memory Agent", "type": "ai_outputParser", "index": 0}]]}, "Postgres": {"main": [[{"node": "Supabase Vector Store", "type": "main", "index": 0}]]}, "Code Tool": {"ai_tool": [[{"node": "RAG AI Agent", "type": "ai_tool", "index": 0}]]}, "Web Search Tool": {"ai_tool": [[{"node": "RAG AI Agent", "type": "ai_tool", "index": 0}]]}, "Determine Tool Type": {"main": [[{"node": "Download File1", "type": "main", "index": 0}], [{"node": "Brave Web Search", "type": "main", "index": 0}]]}, "Tool Start": {"main": [[{"node": "Determine Tool Type", "type": "main", "index": 0}]]}, "Brave Web Search": {"main": [[{"node": "Summarize Web Research", "type": "main", "index": 0}]]}, "Download File1": {"main": [[{"node": "OpenAI", "type": "main", "index": 0}]]}, "Image Analysis Tool": {"ai_tool": [[{"node": "RAG AI Agent", "type": "ai_tool", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "341c6957-25c2-42ff-a383-2171c41820f6", "meta": {"templateCredsSetupCompleted": true, "instanceId": "f65a08c0adc90a3cde2c633d24c6daecde3817033b75588ee10a781b0b7aa3f5"}, "id": "qp9LiDk2AsYxkF1W", "tags": []}