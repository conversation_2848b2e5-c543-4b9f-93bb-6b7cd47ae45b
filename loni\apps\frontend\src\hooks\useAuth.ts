/**
 * Authentication React hooks
 */
import { useState, useEffect, useCallback } from 'react'
import { authService } from '@/lib/auth'
import type { User, LoginRequest, RegisterRequest } from '@/types'

interface UseAuthReturn {
  user: User | null
  isAuthenticated: boolean
  isLoading: boolean
  login: (credentials: LoginRequest) => Promise<void>
  register: (userData: RegisterRequest) => Promise<void>
  logout: () => Promise<void>
  updateProfile: (data: Partial<User>) => Promise<void>
  refreshUser: () => Promise<void>
}

export function useAuth(): UseAuthReturn {
  const [user, setUser] = useState<User | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  const isAuthenticated = !!user

  const refreshUser = useCallback(async () => {
    if (!authService.isAuthenticated()) {
      setUser(null)
      setIsLoading(false)
      return
    }

    try {
      const userData = await authService.getCurrentUser()
      setUser(userData)
    } catch (error) {
      console.error('Failed to refresh user:', error)
      setUser(null)
      authService.logout() // Clear invalid token
    } finally {
      setIsLoading(false)
    }
  }, [])

  const login = useCallback(async (credentials: LoginRequest) => {
    setIsLoading(true)
    try {
      await authService.login(credentials)
      await refreshUser()
    } catch (error) {
      setIsLoading(false)
      throw error
    }
  }, [refreshUser])

  const register = useCallback(async (userData: RegisterRequest) => {
    setIsLoading(true)
    try {
      await authService.register(userData)
      // Auto-login after registration
      await login({ username: userData.email, password: userData.password })
    } catch (error) {
      setIsLoading(false)
      throw error
    }
  }, [login])

  const logout = useCallback(async () => {
    setIsLoading(true)
    try {
      await authService.logout()
      setUser(null)
    } catch (error) {
      console.error('Logout error:', error)
    } finally {
      setIsLoading(false)
    }
  }, [])

  const updateProfile = useCallback(async (data: Partial<User>) => {
    if (!user) throw new Error('No user logged in')
    
    try {
      const updatedUser = await authService.updateProfile(data)
      setUser(updatedUser)
    } catch (error) {
      throw error
    }
  }, [user])

  // Initialize auth state on mount
  useEffect(() => {
    refreshUser()
  }, [refreshUser])

  return {
    user,
    isAuthenticated,
    isLoading,
    login,
    register,
    logout,
    updateProfile,
    refreshUser,
  }
}