# Docker Compose Error Monitor
# Monitors Docker Compose services and logs only errors and failures

FROM python:3.11-alpine

# Install system dependencies
RUN apk add --no-cache \
    curl \
    jq \
    docker-cli \
    && pip install --no-cache-dir \
    docker \
    pyyaml \
    requests

# Create working directory
WORKDIR /app

# Create non-root user
RUN addgroup -g 1001 -S monitor && \
    adduser -S monitor -u 1001 -G monitor

# Copy monitoring scripts
COPY scripts/ /app/scripts/
RUN chmod +x /app/scripts/*.py /app/scripts/*.sh

# Create log directories
RUN mkdir -p /var/log/docker-compose && \
    chown -R monitor:monitor /app /var/log/docker-compose

# Switch to non-root user
USER monitor

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8080/health || exit 1

# Default command
CMD ["python", "/app/scripts/docker_monitor.py"]
