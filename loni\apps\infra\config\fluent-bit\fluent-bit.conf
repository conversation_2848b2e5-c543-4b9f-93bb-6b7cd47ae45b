# Fluent Bit Configuration for LONI Platform
# Centralized logging with error-only filtering and AI-friendly output

[SERVICE]
    Flush         5
    Daemon        off
    Log_Level     info
    Parsers_File  parsers.conf
    Plugins_File  plugins.conf
    HTTP_Server   On
    HTTP_Listen   0.0.0.0
    HTTP_Port     2020
    storage.path  /tmp/flb-storage/
    storage.sync  normal
    storage.checksum off
    storage.backlog.mem_limit 5M

# Input: Docker container logs via forward protocol
[INPUT]
    Name              forward
    Listen            0.0.0.0
    Port              24224
    Buffer_Chunk_Size 1M
    Buffer_Max_Size   6M

# Input: Application logs from mounted volumes
[INPUT]
    Name              tail
    Path              /fluent-bit/logs/applications/*.log
    Path_Key          filename
    Tag               app.*
    Multiline         On
    Parser_Firstline  multiline_start
    Buffer_Chunk_Size 1M
    Buffer_Max_Size   6M
    Skip_Long_Lines   On
    Refresh_Interval  5

# Input: Container logs from mounted volumes
[INPUT]
    Name              tail
    Path              /fluent-bit/logs/containers/*.log
    Path_Key          filename
    Tag               container.*
    Parser            docker
    Buffer_Chunk_Size 1M
    Buffer_Max_Size   6M
    Skip_Long_Lines   On
    Refresh_Interval  5

# Input: Development tool logs
[INPUT]
    Name              tail
    Path              /fluent-bit/logs/development/*.log
    Path_Key          filename
    Tag               dev.*
    Parser            json
    Buffer_Chunk_Size 1M
    Buffer_Max_Size   6M
    Skip_Long_Lines   On
    Refresh_Interval  5

# Filter: Add timestamp and hostname
[FILTER]
    Name              record_modifier
    Match             *
    Record            hostname ${HOSTNAME}
    Record            timestamp ${time}
    Record            platform loni

# Filter: Error-only filtering for applications
[FILTER]
    Name              grep
    Match             app.*
    Regex             level (ERROR|FATAL|CRITICAL|error|fatal|critical|Error|Fatal|Critical)

# Filter: Error-only filtering for containers
[FILTER]
    Name              grep
    Match             container.*
    Regex             log (ERROR|FATAL|CRITICAL|error|fatal|critical|Error|Fatal|Critical|Exception|exception|EXCEPTION|failed|Failed|FAILED)

# Filter: Error-only filtering for development tools
[FILTER]
    Name              grep
    Match             dev.*
    Regex             level (ERROR|FATAL|CRITICAL|error|fatal|critical|Error|Fatal|Critical)

# Filter: Parse and structure logs for AI consumption
[FILTER]
    Name              parser
    Match             *
    Key_Name          log
    Parser            ai_structured
    Reserve_Data      On
    Preserve_Key      On

# Output: Structured logs for AI parsing
[OUTPUT]
    Name              file
    Match             *
    Path              /fluent-bit/logs/structured/
    File              ai_logs_${time:%Y%m%d}.jsonl
    Format            json_lines
    json_date_key     timestamp
    json_date_format  iso8601

# Output: Error summary for quick review
[OUTPUT]
    Name              file
    Match             *
    Path              /fluent-bit/logs/summary/
    File              error_summary_${time:%Y%m%d_%H}.log
    Format            template
    Template          [{timestamp}] {level} {service} {container_name}: {message}

# Output: Write to stdout for development
[OUTPUT]
    Name              stdout
    Match             *
    Format            json_lines