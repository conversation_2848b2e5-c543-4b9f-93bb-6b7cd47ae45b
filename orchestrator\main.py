#!/usr/bin/env python3
"""
Main entry point for the Project Template Orchestrator.

Provides a command-line interface for intelligent project generation
with template selection, dependency resolution, and conflict prevention.
"""

import sys
import os
from pathlib import Path

# Add the orchestrator package to Python path
sys.path.insert(0, str(Path(__file__).parent))

from ui.cli import main

if __name__ == "__main__":
    main()