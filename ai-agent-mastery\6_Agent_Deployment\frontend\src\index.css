
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 220 70% 35%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 220 70% 55%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;

    --radius: 0.75rem;

    --sidebar-background: 224 71% 4%;
    --sidebar-foreground: 210 40% 98%;
    --sidebar-primary: 220 70% 35%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 225 25% 20%;
    --sidebar-accent-foreground: 210 40% 98%;
    --sidebar-border: 225 25% 15%;
    --sidebar-ring: 220 70% 35%;

    --chat-user: 215 25% 27%;
    --chat-assistant: 225 25% 18%;
  }

  .dark {
    --background: 224 71% 4%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 220 70% 35%;
    --primary-foreground: 210 40% 98%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 220 70% 55%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;

    --chat-user: 215 25% 27%;
    --chat-assistant: 225 25% 18%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
}

@layer components {
  .code-block {
    @apply bg-gray-900 rounded-md p-4 my-2 overflow-x-auto;
  }
  
  .code-block code {
    @apply text-gray-100 text-sm font-mono;
  }
}

/* For code syntax highlighting */
.hljs-keyword {
  @apply text-blue-400;
}

.hljs-string {
  @apply text-green-400;
}

.hljs-number {
  @apply text-orange-400;
}

.hljs-comment {
  @apply text-gray-500;
}

.hljs-function {
  @apply text-blue-400;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  @apply bg-primary/30 rounded-full;
}

::-webkit-scrollbar-thumb:hover {
  @apply bg-primary/50;
}
