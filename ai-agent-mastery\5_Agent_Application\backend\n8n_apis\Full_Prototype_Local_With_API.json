{"name": "AI Agent Mastery Local Prototype with API", "nodes": [{"parameters": {"jsonMode": "expressionData", "jsonData": "={{ $json.data || $json.text || $json.concatenated_data }}", "options": {"metadata": {"metadataValues": [{"name": "=file_id", "value": "={{ $('Set File ID').first().json.file_id }}"}, {"name": "file_title", "value": "={{ $('Set File ID').first().json.file_title }}"}]}}}, "id": "7fd899e9-cb98-453c-8338-89b5a389b178", "name": "Default Data Loader", "type": "@n8n/n8n-nodes-langchain.documentDefaultDataLoader", "typeVersion": 1, "position": [40, 1140]}, {"parameters": {"content": "## Agent Tools for Agentic RAG", "height": 409, "width": 583, "color": 4}, "id": "702ab2cc-3634-49c3-8f01-e54012b2b452", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [200, 120]}, {"parameters": {"content": "## Tool to Add a Google Drive File to Vector DB", "height": 867, "width": 3133, "color": 5}, "id": "74a6cc7f-6516-4600-90e1-c00176f52f09", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-2760, 540]}, {"parameters": {"operation": "text", "options": {}}, "id": "8fed6763-0f70-4fcf-ad2a-dc7e0a6df0e4", "name": "Extract Document Text", "type": "n8n-nodes-base.extractFromFile", "typeVersion": 1, "position": [-760, 1140], "alwaysOutputData": true}, {"parameters": {"sessionIdType": "customKey", "sessionKey": "={{ $json.sessionId }}", "tableName": "messages"}, "id": "05326d27-a581-4961-b05a-c008d1c70c70", "name": "Postgres Chat Memory", "type": "@n8n/n8n-nodes-langchain.memoryPostgresChat", "typeVersion": 1, "position": [-1080, 20], "notesInFlow": false, "credentials": {"postgres": {"id": "UaTmh0frrACTMPxG", "name": "Postgres account"}}}, {"parameters": {"assignments": {"assignments": [{"id": "10646eae-ae46-4327-a4dc-9987c2d76173", "name": "file_id", "value": "={{ $json.path }}", "type": "string"}, {"id": "f4536df5-d0b1-4392-bf17-b8137fb31a44", "name": "file_type", "value": "={{ $json.path.split(/[\\\\/]/).pop().split('.').pop(); }}", "type": "string"}, {"id": "77d782de-169d-4a46-8a8e-a3831c04d90f", "name": "file_title", "value": "={{ $json.path.split(/[\\\\/]/).pop().split('.').slice(0, -1).join('.'); }}", "type": "string"}]}, "options": {}}, "id": "1ba0268c-8e22-4afd-a1d8-edb60d566773", "name": "Set File ID", "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [-2280, 820]}, {"parameters": {"content": "## RAG AI Agent with Chat Interface and API Endpoint", "height": 1245, "width": 2936}, "id": "c2cad174-f1ce-46e6-ba6e-6f73b86822b0", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-2760, -720]}, {"parameters": {"options": {}}, "id": "3594f9a2-d9b2-4041-b574-187e39d9601b", "name": "Respond to Webhook", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1.1, "position": [0, -260]}, {"parameters": {"assignments": {"assignments": [{"id": "9a9a245e-f1a1-4282-bb02-a81ffe629f0f", "name": "chatInput", "value": "={{ $json.chatInput || $('Webhook').item.json.body.query }}", "type": "string"}, {"id": "b80831d8-c653-4203-8706-adedfdb98f77", "name": "sessionId", "value": "={{ $json.sessionId || $json.session_id || $('Webhook').item.json.body.session_id }}", "type": "string"}]}, "options": {}}, "id": "a49f0e16-4490-4db1-a7f0-e6fc2b46a243", "name": "<PERSON>", "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [-1120, -220]}, {"parameters": {"public": true, "options": {}}, "id": "c93c4240-477c-4522-a9fb-51b944808070", "name": "When chat message received", "type": "@n8n/n8n-nodes-langchain.chatTrigger", "typeVersion": 1.1, "position": [-1400, -120], "webhookId": "3f12d5d7-b61e-4bba-bc48-d94837cb5c25"}, {"parameters": {"httpMethod": "POST", "path": "0caeb84d-594a-42d5-871b-2ee9cfafe82c", "responseMode": "responseNode", "options": {}}, "id": "0cca9581-391c-4866-a3bc-0bb9adf0c033", "name": "Webhook", "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [-2680, -220], "webhookId": "0caeb84d-594a-42d5-871b-2ee9cfafe82c"}, {"parameters": {"operation": "pdf", "options": {}}, "id": "3e4a62fe-0cb1-4a35-9326-c3bc4c8d6052", "name": "Extract PDF Text", "type": "n8n-nodes-base.extractFromFile", "typeVersion": 1, "position": [-760, 580]}, {"parameters": {"aggregate": "aggregateAllItemData", "options": {}}, "id": "165bc9fa-8a4f-405a-b9a0-44009352adbd", "name": "Aggregate", "type": "n8n-nodes-base.aggregate", "typeVersion": 1, "position": [-720, 760]}, {"parameters": {"fieldsToSummarize": {"values": [{"aggregation": "concatenate", "field": "data"}]}, "options": {}}, "id": "ca4b5004-d035-4124-81db-6f18e0154883", "name": "Summarize", "type": "n8n-nodes-base.summarize", "typeVersion": 1, "position": [-520, 840]}, {"parameters": {"promptType": "define", "text": "={{ $json.chatInput }}", "options": {"systemMessage": "=You are an intelligent AI assistant with advanced research and analysis capabilities. You excel at retrieving, processing, and synthesizing information from diverse document types to provide accurate, comprehensive answers. You are intuitive, friendly, and proactive, always aiming to deliver the most relevant information while maintaining clarity and precision.\n\nGoal:\n\nYour goal is to provide accurate, relevant, and well-sourced information by utilizing your suite of tools. You aim to streamline the user's research process, offer insightful analysis, and ensure they receive reliable answers to their queries. You help users by delivering thoughtful, well-researched responses that save them time and enhance their understanding of complex topics.\n\nTool Instructions:\n\n- Always begin with Memory: Before doing anything, use the memory tool to fetch relevant memories. You prioritize using this tool first and you always use it if the answer needs to be personalized to the user in ANY way!\n\n- Document Retrieval Strategy:\nFor general information queries: Use RAG first. Then analyze individual documents if RAG is insufficient.\nFor numerical analysis or data queries: Use SQL on tabular data\n\n- Knowledge Boundaries: Explicitly acknowledge when you cannot find an answer in the available resources.\n\nFor the rest of the tools, use them as necessary based on their descriptions.\n\nOutput Format:\n\nStructure your responses to be clear, concise, and well-organized. Begin with a direct answer to the user's query when possible, followed by supporting information and your reasoning process.\n\nMisc Instructions:\n\n- Query Clarification:\nRequest clarification when queries are ambiguous - but check memories first because that might clarify things.\n\nData Analysis Best Practices:\n- Explain your analytical approach when executing code or SQL queries\nPresent numerical findings with appropriate context and units\n\n- Source Prioritization:\nPrioritize the most recent and authoritative documents when information varies\n\n- Transparency About Limitations:\nClearly state when information appears outdated or incomplete\nAcknowledge when web search might provide more current information than your document corpus"}}, "id": "0a1ebeb5-690f-4585-9229-6ceac7e00289", "name": "RAG AI Agent", "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.6, "position": [-880, -220]}, {"parameters": {"rules": {"values": [{"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 1}, "conditions": [{"leftValue": "={{ $('Set File ID').item.json.file_type }}", "rightValue": "pdf", "operator": {"type": "string", "operation": "equals"}}], "combinator": "and"}}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 1}, "conditions": [{"id": "2ae7faa7-a936-4621-a680-60c512163034", "leftValue": "={{ $('Set File ID').item.json.file_type }}", "rightValue": "xlsx", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 1}, "conditions": [{"id": "fc193b06-363b-4699-a97d-e5a850138b0e", "leftValue": "={{ $('Set File ID').item.json.file_type }}", "rightValue": "=csv", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 1}, "conditions": [{"id": "b69f5605-0179-4b02-9a32-e34bb085f82d", "leftValue": "={{ $('Set File ID').item.json.file_type }}", "rightValue": "txt", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}}]}, "options": {"fallbackOutput": "extra"}}, "id": "a07590c1-7869-433d-bc0d-9b9560654a1e", "name": "Switch", "type": "n8n-nodes-base.switch", "typeVersion": 3, "position": [-1420, 800]}, {"parameters": {"operation": "xlsx", "options": {}}, "id": "bd3737ce-82ff-46b0-886f-cb8be70ce597", "name": "Extract from Excel", "type": "n8n-nodes-base.extractFromFile", "typeVersion": 1, "position": [-940, 760]}, {"parameters": {"assignments": {"assignments": [{"id": "f422e2e0-381c-46ea-8f38-3f58c501d8b9", "name": "schema", "value": "={{ $('Extract from Excel').isExecuted ? $('Extract from Excel').first().json.keys().toJsonString() : $('Extract from CSV').first().json.keys().toJsonString() }}", "type": "string"}, {"id": "bb07c71e-5b60-4795-864c-cc3845b6bc46", "name": "data", "value": "={{ $json.concatenated_data }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [-80, 700], "id": "4d97bdeb-aeb8-4dac-b1cf-ddb2da6fdb7d", "name": "<PERSON>"}, {"parameters": {"options": {}}, "type": "n8n-nodes-base.extractFromFile", "typeVersion": 1, "position": [-940, 940], "id": "c6ba32ea-e35b-41ba-890d-67c705502d58", "name": "Extract from CSV"}, {"parameters": {"content": "## Run Each Node Once to Set Up Database Tables", "height": 320, "width": 800, "color": 3}, "type": "n8n-nodes-base.stickyNote", "position": [-3580, -720], "typeVersion": 1, "id": "8afa94a0-6419-4d8d-affb-69805d2217b9", "name": "Sticky Note3"}, {"parameters": {"operation": "execute<PERSON>uery", "query": "CREATE TABLE document_metadata (\n    id TEXT PRIMARY KEY,\n    title TEXT,\n    created_at TIMESTAMP DEFAULT NOW(),\n    schema TEXT\n);", "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.5, "position": [-3500, -620], "id": "fc544db9-2599-4318-a65b-a94a808823fb", "name": "Create Document Metadata Table", "credentials": {"postgres": {"id": "UaTmh0frrACTMPxG", "name": "Postgres account"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "CREATE TABLE document_rows (\n    id SERIAL PRIMARY KEY,\n    dataset_id TEXT REFERENCES document_metadata(id),\n    row_data JSONB  -- Store the actual row data\n);", "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.5, "position": [-2980, -620], "id": "d71e05e8-1d07-4674-8a58-10bc99aa54cc", "name": "Create Document Rows Table (for Tabular Data)", "credentials": {"postgres": {"id": "UaTmh0frrACTMPxG", "name": "Postgres account"}}}, {"parameters": {"descriptionType": "manual", "toolDescription": "Use this tool to fetch all available documents, including the table schema if the file is a CSV or Excel file.", "operation": "select", "schema": {"__rl": true, "mode": "list", "value": "public"}, "table": {"__rl": true, "value": "document_metadata", "mode": "list", "cachedResultName": "document_metadata"}, "returnAll": true, "options": {}}, "type": "n8n-nodes-base.postgresTool", "typeVersion": 2.5, "position": [220, 380], "id": "a76eeecb-20d8-4f64-97dc-c816e8afc79b", "name": "List Documents", "credentials": {"postgres": {"id": "UaTmh0frrACTMPxG", "name": "Postgres account"}}}, {"parameters": {"descriptionType": "manual", "toolDescription": "Given a file ID, fetches the text from the document.", "operation": "execute<PERSON>uery", "query": "SELECT \n    string_agg(text, ' ') as document_text\nFROM documents_pg\n  WHERE metadata->>'file_id' = $1\nGROUP BY metadata->>'file_id';", "options": {"queryReplacement": "={{ $fromAI('file_id') }}"}}, "type": "n8n-nodes-base.postgresTool", "typeVersion": 2.5, "position": [320, 260], "id": "3c33d749-b9c0-4f69-88b0-1f72b65d41fb", "name": "Get File Contents", "credentials": {"postgres": {"id": "UaTmh0frrACTMPxG", "name": "Postgres account"}}}, {"parameters": {"descriptionType": "manual", "toolDescription": "Run a SQL query - use this to query from the document_rows table once you know the file ID (which is the file path) you are querying. dataset_id is the file_id (file path) and you are always using the row_data for filtering, which is a jsonb field that has all the keys from the file schema given in the document_metadata table.\n\nExample query:\n\nSELECT AVG((row_data->>'revenue')::numeric)\nFROM document_rows\nWHERE dataset_id = '/data/shared/document.csv';\n\nExample query 2:\n\nSELECT \n    row_data->>'category' as category,\n    SUM((row_data->>'sales')::numeric) as total_sales\nFROM dataset_rows\nWHERE dataset_id = '/data/shared/document2.csv'\nGROUP BY row_data->>'category';", "operation": "execute<PERSON>uery", "query": "{{ $fromAI('sql_query') }}", "options": {}}, "type": "n8n-nodes-base.postgresTool", "typeVersion": 2.5, "position": [420, 380], "id": "e0c98072-247f-42de-915d-382181f5dd35", "name": "Query Document Rows", "credentials": {"postgres": {"id": "UaTmh0frrACTMPxG", "name": "Postgres account"}}}, {"parameters": {"options": {"reset": false}}, "type": "n8n-nodes-base.splitInBatches", "typeVersion": 3, "position": [-2460, 660], "id": "1f14e23a-315d-4853-89c1-8da7b4574c54", "name": "Loop Over Items"}, {"parameters": {"operation": "upsert", "schema": {"__rl": true, "mode": "list", "value": "public"}, "table": {"__rl": true, "value": "document_metadata", "mode": "list", "cachedResultName": "document_metadata"}, "columns": {"mappingMode": "defineBelow", "value": {"id": "={{ $('Set File ID').item.json.file_id }}", "title": "={{ $('Set File ID').item.json.file_title }}"}, "matchingColumns": ["id"], "schema": [{"id": "id", "displayName": "id", "required": true, "defaultMatch": true, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "title", "displayName": "title", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": false}, {"id": "url", "displayName": "url", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": false, "removed": true}, {"id": "created_at", "displayName": "created_at", "required": false, "defaultMatch": false, "display": true, "type": "dateTime", "canBeUsedToMatch": false}, {"id": "schema", "displayName": "schema", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": false, "removed": true}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.5, "position": [-1780, 680], "id": "0613b300-6986-469e-ade0-753b1339c7f0", "name": "Insert Document Metadata", "executeOnce": true, "credentials": {"postgres": {"id": "UaTmh0frrACTMPxG", "name": "Postgres account"}}}, {"parameters": {"schema": {"__rl": true, "mode": "list", "value": "public"}, "table": {"__rl": true, "value": "document_rows", "mode": "list", "cachedResultName": "document_rows"}, "columns": {"mappingMode": "defineBelow", "value": {"dataset_id": "={{ $('Set File ID').item.json.file_id }}", "row_data": "={{ $json.toJsonString().replaceAll(/'/g, \"''\") }}"}, "matchingColumns": ["id"], "schema": [{"id": "id", "displayName": "id", "required": false, "defaultMatch": true, "display": true, "type": "number", "canBeUsedToMatch": true, "removed": true}, {"id": "dataset_id", "displayName": "dataset_id", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "row_data", "displayName": "row_data", "required": false, "defaultMatch": false, "display": true, "type": "object", "canBeUsedToMatch": true, "removed": false}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.5, "position": [-720, 940], "id": "238ef715-7697-4fe8-89ec-d6e16a2c2704", "name": "Insert Table Rows", "credentials": {"postgres": {"id": "UaTmh0frrACTMPxG", "name": "Postgres account"}}}, {"parameters": {"operation": "upsert", "schema": {"__rl": true, "mode": "list", "value": "public"}, "table": {"__rl": true, "value": "document_metadata", "mode": "list", "cachedResultName": "document_metadata"}, "columns": {"mappingMode": "defineBelow", "value": {"id": "={{ $('Set File ID').item.json.file_id }}", "schema": "={{ $json.schema }}"}, "matchingColumns": ["id"], "schema": [{"id": "id", "displayName": "id", "required": true, "defaultMatch": true, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "title", "displayName": "title", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": false, "removed": true}, {"id": "url", "displayName": "url", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": false, "removed": true}, {"id": "created_at", "displayName": "created_at", "required": false, "defaultMatch": false, "display": true, "type": "dateTime", "canBeUsedToMatch": false}, {"id": "schema", "displayName": "schema", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": false, "removed": false}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.5, "position": [140, 700], "id": "6adb6e81-ae13-42a6-9ff9-b8f1bd7a5765", "name": "Update <PERSON><PERSON>a for Document Metadata", "credentials": {"postgres": {"id": "UaTmh0frrACTMPxG", "name": "Postgres account"}}}, {"parameters": {"triggerOn": "folder", "path": "/data/shared", "events": ["add", "change"], "options": {"followSymlinks": true, "usePolling": true}}, "type": "n8n-nodes-base.localFileTrigger", "typeVersion": 1, "position": [-2680, 660], "id": "d4339e2b-26a5-4413-ae16-a3efaf5a0c45", "name": "Local File Trigger"}, {"parameters": {"fileSelector": "={{ $('Set File ID').item.json.file_id }}", "options": {"dataPropertyName": "=data"}}, "type": "n8n-nodes-base.readWriteFile", "typeVersion": 1, "position": [-1620, 820], "id": "650d49a7-328e-45d6-a95f-a25737a76206", "name": "Read/Write Files from Disk"}, {"parameters": {"model": "nomic-embed-text:latest"}, "type": "@n8n/n8n-nodes-langchain.embeddingsOllama", "typeVersion": 1, "position": [-200, 1140], "id": "0950635c-1a66-4b9f-a80e-3bccdfd40699", "name": "Embeddings <PERSON><PERSON><PERSON>", "credentials": {"ollamaApi": {"id": "GwjiKiEsG5HnTaAf", "name": "Ollama account"}}}, {"parameters": {"model": "nomic-embed-text:latest"}, "type": "@n8n/n8n-nodes-langchain.embeddingsOllama", "typeVersion": 1, "position": [600, 380], "id": "d652df11-cf32-4e98-badb-4175ad023896", "name": "Embeddings Ollama1", "credentials": {"ollamaApi": {"id": "GwjiKiEsG5HnTaAf", "name": "Ollama account"}}}, {"parameters": {"chunkSize": 400, "options": {}}, "type": "@n8n/n8n-nodes-langchain.textSplitterRecursiveCharacterTextSplitter", "typeVersion": 1, "position": [-60, 1260], "id": "********-519f-4f66-8b86-ffd620b493cd", "name": "Recursive Character Text Splitter"}, {"parameters": {"model": {"__rl": true, "value": "qwen2.5:14b-8k", "mode": "list", "cachedResultName": "qwen2.5:14b-8k"}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1.2, "position": [-1220, 20], "id": "41c55992-e963-4959-91ff-e5a47f6a7968", "name": "Ollama (Change Base URL)", "credentials": {"openAiApi": {"id": "yjDbkWOXNXC959ei", "name": "OpenAi account"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "DO $$\nBEGIN\n    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'documents_pg') THEN\n        EXECUTE 'DELETE FROM documents_pg WHERE metadata->>''file_id'' LIKE ''%' || $1 || '%''';\n    END IF;\nEND\n$$;", "options": {"queryReplacement": "={{ $json.file_id }}"}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.5, "position": [-2100, 680], "id": "39ad3752-2ed5-4a6c-a8f5-292d43f687be", "name": "Delete Old Doc Records", "credentials": {"postgres": {"id": "UaTmh0frrACTMPxG", "name": "Postgres account"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "DELETE FROM document_rows\nWHERE dataset_id LIKE '%' || $1 || '%';", "options": {"queryReplacement": "={{ $('Set File ID').item.json.file_id }}"}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.5, "position": [-1940, 820], "id": "da5a7ecd-0515-43fd-88e0-bc1057760913", "name": "Delete Old Data Records", "credentials": {"postgres": {"id": "UaTmh0frrACTMPxG", "name": "Postgres account"}}}, {"parameters": {"mode": "insert", "tableName": "documents_pg", "options": {}}, "type": "@n8n/n8n-nodes-langchain.vectorStorePGVector", "typeVersion": 1, "position": [-80, 920], "id": "15cae984-4a96-43d7-9f1e-ed1a2d53ea71", "name": "Postgres PGVector Store", "credentials": {"postgres": {"id": "UaTmh0frrACTMPxG", "name": "Postgres account"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "-- Create a table to store your documents\ncreate table memories (\n  id bigserial primary key,\n  text text, -- corresponds to Document.pageContent\n  metadata jsonb, -- corresponds to Document.metadata\n  embedding vector(768) -- 768 works for nomic-embed-text embeddings, change if needed\n);\n  \n-- Create a function to search for documents\ncreate function match_memories (\n  query_embedding vector(768),\n  match_count int default null,\n  filter jsonb DEFAULT '{}'\n) returns table (\n  id bigint,\n  text text,\n  metadata jsonb,\n  similarity float\n)\nlanguage plpgsql\nas $$\n#variable_conflict use_column\nbegin\n  return query\n  select\n    id,\n    text,\n    metadata,\n    1 - (memories.embedding <=> query_embedding) as similarity\n  from memories\n  where metadata @> filter\n  order by memories.embedding <=> query_embedding\n  limit match_count;\nend;\n$$;", "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.5, "position": [-3240, -620], "id": "56ca35a0-8a49-4586-b43a-4368e433f42f", "name": "Create Memories Table and Match Function", "credentials": {"postgres": {"id": "UaTmh0frrACTMPxG", "name": "Postgres account"}}}, {"parameters": {"mode": "retrieve-as-tool", "toolName": "memories", "toolDescription": "Use this tool to fetch memories from previous conversations if needed to answer a question for the user or continue the conversation.", "tableName": "memories", "options": {}}, "type": "@n8n/n8n-nodes-langchain.vectorStorePGVector", "typeVersion": 1, "position": [-680, 100], "id": "5f00e12e-6354-4654-822a-6aeccb86d6c3", "name": "Retrieve Memories Tool", "credentials": {"postgres": {"id": "UaTmh0frrACTMPxG", "name": "Postgres account"}}}, {"parameters": {"model": "nomic-embed-text:latest"}, "type": "@n8n/n8n-nodes-langchain.embeddingsOllama", "typeVersion": 1, "position": [-500, 340], "id": "f2e68af3-2cb6-4833-b185-33ea60c8a9b4", "name": "Embeddings Ollama2", "credentials": {"ollamaApi": {"id": "GwjiKiEsG5HnTaAf", "name": "Ollama account"}}}, {"parameters": {"mode": "retrieve-as-tool", "toolName": "documents", "toolDescription": "Use RAG to look up information in the knowledgebase.", "tableName": "documents_pg", "options": {}}, "type": "@n8n/n8n-nodes-langchain.vectorStorePGVector", "typeVersion": 1, "position": [480, 200], "id": "55ce5f81-4aec-49b6-a640-e4b830b75184", "name": "Document RAG Tool", "credentials": {"postgres": {"id": "UaTmh0frrACTMPxG", "name": "Postgres account"}}}, {"parameters": {"content": "## Workflow Agent Tools", "height": 380, "width": 1660, "color": 4}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [200, -720], "id": "f081b275-59c4-4bf3-8ce3-7fabecd0851a", "name": "Sticky Note7"}, {"parameters": {"rules": {"values": [{"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"leftValue": "={{ $json.tool_type }}", "rightValue": "image_analysis", "operator": {"type": "string", "operation": "equals"}}], "combinator": "and"}}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "a50a5a5a-cc87-4654-97ea-05090efdb416", "leftValue": "={{ $json.tool_type }}", "rightValue": "web_search", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}}]}, "options": {"fallbackOutput": "none"}}, "type": "n8n-nodes-base.switch", "typeVersion": 3.2, "position": [500, -600], "id": "abfe6eba-0b87-4e49-8bfc-a4912e1fa925", "name": "Determine Tool Type"}, {"parameters": {"workflowInputs": {"values": [{"name": "tool_type"}, {"name": "query"}, {"name": "image_path"}]}}, "type": "n8n-nodes-base.executeWorkflowTrigger", "typeVersion": 1.1, "position": [280, -600], "id": "d2b984aa-1a26-4bc0-bce8-d499416e7dce", "name": "Tool Start"}, {"parameters": {"name": "web_search", "description": "Call this tool to do an advanced web search based on a query you define.\n\nThis tool will return the contents of the 3 most relevant web pages from the search.", "workflowId": {"__rl": true, "value": "={{ $workflow.id }}", "mode": "id"}, "workflowInputs": {"mappingMode": "defineBelow", "value": {"query": "={{ $fromAI('query') }}", "tool_type": "web_search"}, "matchingColumns": [], "schema": [{"id": "tool_type", "displayName": "tool_type", "required": false, "defaultMatch": false, "display": true, "canBeUsedToMatch": true, "type": "string", "removed": false}, {"id": "query", "displayName": "query", "required": false, "defaultMatch": false, "display": true, "canBeUsedToMatch": true, "type": "string", "removed": false}, {"id": "image_path", "displayName": "image_path", "required": false, "defaultMatch": false, "display": true, "canBeUsedToMatch": true, "type": "string", "removed": true}], "attemptToConvertTypes": false, "convertFieldsToString": false}}, "type": "@n8n/n8n-nodes-langchain.toolWorkflow", "typeVersion": 2, "position": [-1040, 220], "id": "590c08f2-f784-4e5c-b124-9b137763f783", "name": "Web Search Tool"}, {"parameters": {"name": "image_analysis", "description": "Call this tool to analyze an image based on an image path that you supply as image_path. Call the \"List Documents\" tool to get a list of files to get the path to the image to analyze. Also supply query, which is the prompt to the LLM to extract information from the image.", "workflowId": {"__rl": true, "value": "={{ $workflow.id }}", "mode": "id"}, "workflowInputs": {"mappingMode": "defineBelow", "value": {"query": "={{ $fromAI('query') }}", "tool_type": "image_analysis", "image_path": "={{ $fromAI('file_path') }}"}, "matchingColumns": [], "schema": [{"id": "tool_type", "displayName": "tool_type", "required": false, "defaultMatch": false, "display": true, "canBeUsedToMatch": true, "type": "string", "removed": false}, {"id": "query", "displayName": "query", "required": false, "defaultMatch": false, "display": true, "canBeUsedToMatch": true, "type": "string", "removed": false}, {"id": "image_path", "displayName": "image_path", "required": false, "defaultMatch": false, "display": true, "canBeUsedToMatch": true, "type": "string", "removed": false}], "attemptToConvertTypes": false, "convertFieldsToString": false}}, "type": "@n8n/n8n-nodes-langchain.toolWorkflow", "typeVersion": 2, "position": [-920, 140], "id": "64ea64a6-af41-4267-8e03-606ff7f010f9", "name": "Image Analysis Tool"}, {"parameters": {"name": "execute_code", "description": "Call this tool to execute JavaScript code that you create with parameters that you specify too. All code must be a single line and must be JavaScript code.", "jsCode": "/**\n * Execute arbitrary JavaScript code and capture its console output\n * @param {string} codeString - JavaScript code to execute\n * @returns {string} The captured console output\n */\nfunction executeCode(codeString) {\n  // Save the original console.log function\n  const originalLog = console.log;\n  \n  // Create an array to store the output\n  const outputLines = [];\n  \n  // Override console.log to capture output\n  console.log = function(...args) {\n    // Convert all arguments to strings and join them\n    const output = args.map(arg => \n      typeof arg === 'object' ? JSON.stringify(arg) : String(arg)\n    ).join(' ');\n    \n    // Add to our captured output\n    outputLines.push(output);\n  };\n  \n  // Variable to store the result\n  let result;\n  \n  try {\n    // Execute the code\n    result = eval(codeString);\n  } catch (error) {\n    // Restore the original console.log\n    console.log = originalLog;\n    return `Error executing code: ${error.message}`;\n  } finally {\n    // Restore the original console.log\n    console.log = originalLog;\n  }\n  \n  // Join all captured output lines\n  const output = outputLines.join('\\n');\n  \n  // If there's a result but no console output, return the result\n  if (output === '' && result !== undefined) {\n    return String(result);\n  }\n  \n  return output;\n}\n\nreturn executeCode(query.code_to_execute);", "specifyInputSchema": true, "jsonSchemaExample": "{\n\t\"code_to_execute\": \"console.log('test')\"\n}"}, "type": "@n8n/n8n-nodes-langchain.toolCode", "typeVersion": 1.1, "position": [-800, 260], "id": "01cf4b46-ec17-42ef-992e-94ffbb42d8b9", "name": "Code Tool"}, {"parameters": {"jsonSchemaExample": "{\n\t\"memories\": [\"Memory 1\", \"Memory 2\", \"Memory 3\"]\n}"}, "type": "@n8n/n8n-nodes-langchain.outputParserStructured", "typeVersion": 1.2, "position": [480, -40], "id": "bdc88677-e895-4265-b36a-c7cdd1156dc9", "name": "Structured Output Parser"}, {"parameters": {"assignments": {"assignments": [{"id": "636ddfda-13fd-4f21-ad30-fa21472ed9e7", "name": "output", "value": "={{ $('RAG AI Agent').item.json.output }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [1640, -80], "id": "5ab6535f-9a9b-471d-b1f3-bff353664c73", "name": "Edit Fields1", "executeOnce": true}, {"parameters": {"promptType": "define", "text": "=You are an expert at maintaining a memory database for a user, making sure that there aren't duplicate or contradictory memories.\n\nThe current memories being added to the database are:\n\n{{ $json.output.memories }}\n\nYour job is to:\n- Search the memory database to find any duplicate or contradictory memories. The tool will return memories from the search but you have to decide if they are duplicates or contradictory of the memories being added or not.\n- If you find any duplicate/contradictory memories, output a list of memories to delete (the content of the memory). Otherwise, if you find no memories to delete, output an empty list. You determine based on all the memories returned from the search if they are duplicates/contradictory. The memories you output have to match EXACTLY with the content received.", "hasOutputParser": true, "options": {}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.7, "position": [760, -220], "id": "25442b00-6b59-4e76-9ddd-0ff7160fbcb2", "name": "Memory Agent"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "cb436122-f430-4ea5-98d8-b99e8c52eeaa", "leftValue": "={{ $json.output.memories }}", "rightValue": "", "operator": {"type": "array", "operation": "notEmpty", "singleValue": true}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [580, -220], "id": "c40fc27e-caae-406c-9743-da1fd67176ea", "name": "If"}, {"parameters": {"promptType": "define", "text": "=You are an expert at extract key information from conversations to store as long term memories for RAG.\n\nThe user said:\n\n{{ $('Edit Fields').item.json.chatInput }}\n\nYour goal is to output a list of key memories to extract from the conversation.\n\nJust output a empty list if there is nothing worth saving, like if the user is just asking a basic question without providing additional context.", "hasOutputParser": true}, "type": "@n8n/n8n-nodes-langchain.chainLlm", "typeVersion": 1.5, "position": [240, -220], "id": "78f5cb08-d6d6-4e55-9b49-3520f723a750", "name": "Basic LLM Chain", "onError": "continueRegularOutput"}, {"parameters": {"operation": "execute<PERSON>uery", "query": "DELETE FROM memories\nWHERE text IN ({{ $json.output.concat($('Basic LLM Chain').item.json.output.memories).map(item => `'${item.replace(/'/g, \"''\")}'`).join(', ') || '__IMPOSSIBLE_VALUE__' }})", "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.5, "position": [1100, -220], "id": "23eeb76a-43c9-484a-9a3a-c232cefacae2", "name": "Postgres", "credentials": {"postgres": {"id": "UaTmh0frrACTMPxG", "name": "Postgres account"}}}, {"parameters": {"content": "## Save Long Term Memories", "height": 420, "width": 1660, "color": 6}, "type": "n8n-nodes-base.stickyNote", "position": [200, -320], "typeVersion": 1, "id": "598af15d-fee9-4a92-94bd-e1b5ca633f56", "name": "Sticky Note5"}, {"parameters": {"model": "qwen2.5:14b-8k", "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOllama", "typeVersion": 1, "position": [300, -40], "id": "d15f3ba5-c62e-4934-9551-15bd64ddc00c", "name": "<PERSON><PERSON><PERSON> Chat Model", "credentials": {"ollamaApi": {"id": "GwjiKiEsG5HnTaAf", "name": "Ollama account"}}}, {"parameters": {"mode": "insert", "tableName": "memories", "options": {}}, "type": "@n8n/n8n-nodes-langchain.vectorStorePGVector", "typeVersion": 1, "position": [1300, -220], "id": "8cf95a28-2109-46d8-a788-656e25ea11ec", "name": "Postgres PGVector Store1", "credentials": {"postgres": {"id": "UaTmh0frrACTMPxG", "name": "Postgres account"}}}, {"parameters": {"jsonMode": "expressionData", "jsonData": "={{ $('Basic LLM Chain').item.json.output.memories }}", "options": {"metadata": {"metadataValues": [{"name": "timestamp", "value": "={{ $now }}"}]}}}, "type": "@n8n/n8n-nodes-langchain.documentDefaultDataLoader", "typeVersion": 1, "position": [1520, 220], "id": "1a331cd2-a94a-43e7-a6e6-b0925c0721ae", "name": "Default Data Loader1"}, {"parameters": {"chunkSize": 400}, "type": "@n8n/n8n-nodes-langchain.textSplitterCharacterTextSplitter", "typeVersion": 1, "position": [1620, 380], "id": "16be4009-d2fb-4c64-9b81-08a5009aac1a", "name": "Character Text Splitter"}, {"parameters": {"content": "## Memory Searcher", "height": 400, "width": 520, "color": 6}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [800, 120], "id": "2c7895db-edb9-4d1c-8cfd-3592ba98cfe5", "name": "Sticky Note8"}, {"parameters": {"content": "## Memory Chunking & Embedding", "height": 400, "width": 520, "color": 6}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [1340, 120], "id": "85c6b587-37c3-48c3-8af2-a47e2f70e7bf", "name": "Sticky Note6"}, {"parameters": {"mode": "retrieve-as-tool", "toolName": "memories", "toolDescription": "Use this tool to retrieve memories from the database. You specifically want to use this to find similar memories to what you are planning on adding so that you can do what it takes to remove duplicates.", "tableName": "memories", "topK": 8, "options": {}}, "type": "@n8n/n8n-nodes-langchain.vectorStorePGVector", "typeVersion": 1, "position": [1000, 240], "id": "41f58017-c0fa-43c0-98c3-78de1296ab5b", "name": "Postgres PGVector Store2", "credentials": {"postgres": {"id": "UaTmh0frrACTMPxG", "name": "Postgres account"}}}, {"parameters": {"model": "nomic-embed-text:latest"}, "type": "@n8n/n8n-nodes-langchain.embeddingsOllama", "typeVersion": 1, "position": [1120, 360], "id": "caeef6c8-833a-45db-8bef-670135b3b0e8", "name": "Embeddings Ollama3", "credentials": {"ollamaApi": {"id": "GwjiKiEsG5HnTaAf", "name": "Ollama account"}}}, {"parameters": {"model": "nomic-embed-text:latest"}, "type": "@n8n/n8n-nodes-langchain.embeddingsOllama", "typeVersion": 1, "position": [1400, 340], "id": "55bb5d9e-9f6a-455a-9362-1998f2093836", "name": "Embeddings Ollama4", "credentials": {"ollamaApi": {"id": "GwjiKiEsG5HnTaAf", "name": "Ollama account"}}}, {"parameters": {"model": {"__rl": true, "value": "qwen2.5:14b-8k", "mode": "list", "cachedResultName": "qwen2.5:14b-8k"}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1.2, "position": [860, 340], "id": "e1e0cb44-ece4-4acb-8822-ed320536ce15", "name": "Ollama (Change Base URL)1", "credentials": {"openAiApi": {"id": "yjDbkWOXNXC959ei", "name": "OpenAi account"}}}, {"parameters": {"jsonSchemaExample": "[\"Memory 1\", \"Memory 2\", \"Memory 3\"]"}, "type": "@n8n/n8n-nodes-langchain.outputParserStructured", "typeVersion": 1.2, "position": [1080, -40], "id": "2212d0ee-2f9d-421b-b4e3-e2a819bd949a", "name": "Structured Output Parser1"}, {"parameters": {"fileSelector": "={{ $json.image_path }}", "options": {}}, "type": "n8n-nodes-base.readWriteFile", "typeVersion": 1, "position": [740, -680], "id": "21022cbf-130e-4e74-abff-01ce64f951d3", "name": "Read/Write Files from Disk1"}, {"parameters": {"model": "llava:7b", "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOllama", "typeVersion": 1, "position": [1320, -660], "id": "764a814f-01f1-4ff1-92af-4ef2b80f8216", "name": "Ollama Chat Model1", "credentials": {"ollamaApi": {"id": "GwjiKiEsG5HnTaAf", "name": "Ollama account"}}}, {"parameters": {"promptType": "define", "text": "={{ $('Determine Tool Type').item.json.query }}", "messages": {"messageValues": [{"type": "HumanMessagePromptTemplate", "messageType": "imageBinary"}]}}, "type": "@n8n/n8n-nodes-langchain.chainLlm", "typeVersion": 1.5, "position": [940, -680], "id": "6feaaa9e-46c1-4b31-9475-48ddad3e4797", "name": "Image Analysis"}, {"parameters": {"url": "=http://searxng:8080/search?q={{ $json.query }}&format=json", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [740, -500], "id": "a080b7b5-f448-4684-9e24-d954acad2b2b", "name": "SearXNG"}, {"parameters": {"fieldToSplitOut": "results", "options": {}}, "type": "n8n-nodes-base.splitOut", "typeVersion": 1, "position": [900, -500], "id": "999c867f-0c80-4a17-bc92-d18a4ccb3798", "name": "Split Out"}, {"parameters": {"assignments": {"assignments": [{"id": "169ce734-0077-4c34-b7f1-40a35184fad6", "name": "url", "value": "={{ $json.url }}", "type": "string"}, {"id": "310e45f1-904e-4350-971f-a8519a49ab91", "name": "title", "value": "={{ $json.title }}", "type": "string"}, {"id": "f6ac5cd2-4504-4f37-a766-33bc6ef09d47", "name": "content", "value": "={{ $json.content }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [1060, -500], "id": "c01d0121-5560-4cdf-be98-49988388a20a", "name": "Edit Fields2"}, {"parameters": {"maxItems": 3}, "type": "n8n-nodes-base.limit", "typeVersion": 1, "position": [1220, -500], "id": "8c5908de-27f1-4322-8a56-dee021a7274e", "name": "Limit"}, {"parameters": {"aggregate": "aggregateAllItemData", "destinationFieldName": "search_results", "options": {}}, "type": "n8n-nodes-base.aggregate", "typeVersion": 1, "position": [1680, -500], "id": "a302f619-8003-4700-ac27-0ee15fb358df", "name": "Aggregate1"}, {"parameters": {"url": "={{ $json.url }}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1380, -500], "id": "59b01b78-6204-45c2-95cf-f334a5b3bdff", "name": "HTTP Request", "alwaysOutputData": true, "onError": "continueRegularOutput"}, {"parameters": {"operation": "extractHtmlContent", "extractionValues": {"values": [{"key": "site_html", "cssSelector": "body"}]}, "options": {}}, "type": "n8n-nodes-base.html", "typeVersion": 1.2, "position": [1520, -500], "id": "59ea89e2-bad7-411e-816d-e3d4fcc2863f", "name": "HTML", "alwaysOutputData": true, "onError": "continueRegularOutput"}, {"parameters": {"content": "# Dynamous AI Agent Mastery - Local n8n Agent\n\nThis workflow implements a powerful 100% local AI agent using n8n. Use this as a template to build AI agents that runs entirely on your machine and have the core set of tools used in most agents!\n\n## Features\n\n- **Local LLM Integration**: Uses Ollama models for both conversations and embeddings\n- **Agentic RAG**: Local file processing and vector storage for knowledge retrieval\n- **Long-term Memory**: Persistent conversation memory stored in local Supabase\n- **Local Web Search**: Uses SearXNG for web queries without external API keys\n- **Code Execution**: Generates and runs JavaScript code\n- **Image Analysis**: Processes images with vision-capable local LLMs\n- **Self-contained Architecture**: All components run locally for privacy and control\n\n## Setup Requirements\n\n1. **Local Infrastructure**:\n   - [Local AI package](https://github.com/coleam00/local-ai-packaged) (recommended)\n   - OR manually installed Supabase/Postgres, Ollama, and SearXNG\n\n2. **Environment Configuration**:\n   - Use 'ollama pull' in your Ollama container to download the primary LLM for your agent and a vision capable LLM (they can be the same if you wish) if you don't have them already. Example: 'ollama pull mistral-small3.1'\n   - Use 'ollama pull' to download the embedding model if you don't have it already. Example: 'ollama pull nomic-embed-text'\n   - Set up credentials in n8n for Postgres (Supabase) and Ollama (no API key needed)  \n\n\n3. **Database Setup**:\n   - Run the nodes in the top left to set up the database tables (just have to do this once)\n   - Be sure to modify vector dimensions based on your emebdding model (768 for nomic-embed-text)\n\n## Workflow Structure\n\nThe RAG pipeline (bottom part of the workflow in blue) is responsible for syncing a local folder with the Supabase (Postgres) knowledge base. Make sure the workflow is toggled to active in the top right for this pipeline to work!\n\nThe primary agent is in the yellow box in the middle of the workflow. All of the tools are connected either directly to the agent (in the same box or agentic RAG in the middle green box) or as \"sub-workflows\" which are in the green top box.\n\nThe process of creating long term memories is in the purple boxes.\n\n## Key Differentiators for the Local Implementation (Compared to Cloud)\n\n- All processing happens on your machine - no data leaves your environment\n- Lower latency for certain operations due to local processing\n- No usage limits or API costs\n- Complete control over your data and agent behavior\n- Requires more computational resources on your machine", "height": 1280, "width": 800, "color": 5}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-3580, -380], "id": "88fa331e-1ab8-4b6c-877e-fc07b73d8c20", "name": "Sticky Note4"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "201ba6c4-9be3-4ae1-b7ca-9321c0542549", "leftValue": "={{ $json.body.session_id }}", "rightValue": "", "operator": {"type": "string", "operation": "empty", "singleValue": true}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [-1540, -220], "id": "20a6f588-2724-485b-958f-444a12626e33", "name": "If1"}, {"parameters": {"promptType": "define", "text": "=Based on the user message below, create a 4-6 word sentence for the conversation description since this is the first message in the description.\n\n{{ $('Webhook').item.json.body.query }}"}, "type": "@n8n/n8n-nodes-langchain.chainLlm", "typeVersion": 1.6, "position": [-900, -560], "id": "617e82c4-dfdb-4ad0-9ab3-4374fb61fafc", "name": "Basic LLM Chain1"}, {"parameters": {"assignments": {"assignments": [{"id": "57be6196-201b-43fa-8c37-06fb9f53f7c8", "name": "session_id", "value": "={{ $('Webhook').item.json.body.user_id }}~{{ [...Array(10)].map(() => Math.random().toString(36).charAt(2)).join('') }}", "type": "string"}]}, "includeOtherFields": true, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [-1320, -560], "id": "3c458a2f-ec2a-4d58-a96f-8a73675908ad", "name": "Edit Fields3"}, {"parameters": {"numberInputs": 3}, "type": "n8n-nodes-base.merge", "typeVersion": 3.1, "position": [-380, -260], "id": "74760b02-109b-4dac-89e7-09a807e1b089", "name": "<PERSON><PERSON>"}, {"parameters": {"fieldsToAggregate": {"fieldToAggregate": [{"fieldToAggregate": "output"}, {"fieldToAggregate": "title", "renameField": true, "outputFieldName": "conversation_title"}, {"fieldToAggregate": "session_id"}]}, "options": {}}, "type": "n8n-nodes-base.aggregate", "typeVersion": 1, "position": [-260, -260], "id": "530f7db7-fdb0-4f8a-9ce7-5d6dc9c80e2f", "name": "Aggregate2"}, {"parameters": {"assignments": {"assignments": [{"id": "c05ad106-f118-4d0d-a361-724945332d6f", "name": "output", "value": "={{ $json.output[0] }}", "type": "string"}, {"id": "cb080a99-02ae-43c2-a5b5-25ac3617c523", "name": "conversation_title", "value": "={{ $json.conversation_title[0] }}", "type": "string"}, {"id": "8bb827bb-1b97-4e3b-be3b-da37ede6b052", "name": "session_id", "value": "={{ $json.session_id[0] }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [-140, -260], "id": "6366119c-2e46-4843-8fd8-5504e8fd4f89", "name": "Edit Fields4"}, {"parameters": {"model": "qwen2.5:14b-8k", "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOllama", "typeVersion": 1, "position": [-820, -360], "id": "e36a0ccf-6060-4991-bd87-75cd44dc913c", "name": "Ollama Chat Model2", "credentials": {"ollamaApi": {"id": "GwjiKiEsG5HnTaAf", "name": "Ollama account"}}}, {"parameters": {"schema": {"__rl": true, "mode": "list", "value": "public"}, "table": {"__rl": true, "value": "conversations", "mode": "list", "cachedResultName": "conversations"}, "columns": {"mappingMode": "defineBelow", "value": {"user_id": "={{ $('Webhook').item.json.body.user_id }}", "session_id": "={{ $json.session_id }}"}, "matchingColumns": [], "schema": [{"id": "session_id", "displayName": "session_id", "required": true, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "user_id", "displayName": "user_id", "required": true, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "title", "displayName": "title", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": true}, {"id": "created_at", "displayName": "created_at", "required": false, "defaultMatch": false, "display": true, "type": "dateTime", "canBeUsedToMatch": true, "removed": true}, {"id": "last_message_at", "displayName": "last_message_at", "required": false, "defaultMatch": false, "display": true, "type": "dateTime", "canBeUsedToMatch": true, "removed": true}, {"id": "is_archived", "displayName": "is_archived", "required": false, "defaultMatch": false, "display": true, "type": "boolean", "canBeUsedToMatch": true, "removed": true}, {"id": "metadata", "displayName": "metadata", "required": false, "defaultMatch": false, "display": true, "type": "object", "canBeUsedToMatch": true, "removed": true}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [-1120, -560], "id": "c973e71c-17c0-4bdb-960e-1a63e12c585b", "name": "Postgres1", "credentials": {"postgres": {"id": "UaTmh0frrACTMPxG", "name": "Postgres account"}}}, {"parameters": {"operation": "update", "schema": {"__rl": true, "mode": "list", "value": "public"}, "table": {"__rl": true, "value": "conversations", "mode": "list", "cachedResultName": "conversations"}, "columns": {"mappingMode": "defineBelow", "value": {"session_id": "={{ $('Edit Fields3').item.json.session_id }}", "title": "={{ $json.text }}"}, "matchingColumns": ["session_id"], "schema": [{"id": "session_id", "displayName": "session_id", "required": true, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "user_id", "displayName": "user_id", "required": true, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": true}, {"id": "title", "displayName": "title", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "created_at", "displayName": "created_at", "required": false, "defaultMatch": false, "display": true, "type": "dateTime", "canBeUsedToMatch": true, "removed": true}, {"id": "last_message_at", "displayName": "last_message_at", "required": false, "defaultMatch": false, "display": true, "type": "dateTime", "canBeUsedToMatch": true, "removed": true}, {"id": "is_archived", "displayName": "is_archived", "required": false, "defaultMatch": false, "display": true, "type": "boolean", "canBeUsedToMatch": true, "removed": true}, {"id": "metadata", "displayName": "metadata", "required": false, "defaultMatch": false, "display": true, "type": "object", "canBeUsedToMatch": true, "removed": true}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [-540, -560], "id": "a73c7f9f-a25d-45fe-bab5-f859199d25cd", "name": "Postgres2", "credentials": {"postgres": {"id": "UaTmh0frrACTMPxG", "name": "Postgres account"}}}, {"parameters": {"errorMessage": "Unauthorized call to the agent API!"}, "type": "n8n-nodes-base.stopAndError", "typeVersion": 1, "position": [-2120, -100], "id": "9c5fac13-06f2-4958-bcd2-5b27155d1414", "name": "Stop and Error"}, {"parameters": {"operation": "getAll", "tableId": "requests", "limit": 5, "filters": {"conditions": [{"keyName": "timestamp", "condition": "gt", "keyValue": "={{ $now.toUTC().minus(1, 'minutes').format('yyyy-MM-dd HH:mm:ss') }}"}, {"keyName": "user_id", "condition": "eq", "keyValue": "={{ $('Webhook').item.json.body.user_id }}"}]}}, "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [-2120, -360], "id": "f877739c-0948-4277-9065-a9757c42c32b", "name": "Supabase2", "alwaysOutputData": true, "credentials": {"supabaseApi": {"id": "3tjCDwujGZ7BlK7R", "name": "Supabase account"}}}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "ae9ed956-5249-4c7d-a273-5ddafe604388", "leftValue": "={{ $json.count_id }}", "rightValue": 5, "operator": {"type": "number", "operation": "lt"}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [-1760, -220], "id": "c30b80e8-0fc3-4dbd-8606-cc9f2f6b0164", "name": "If2"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "be275ce9-093e-4a06-81ec-606a1d7358a3", "leftValue": "={{ $('Webhook').item.json.body.user_id }}", "rightValue": "={{ $json.id }}", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [-2320, -220], "id": "5d7bdb3a-f822-490d-85f9-6e0b3041ccb7", "name": "If3"}, {"parameters": {"fieldsToSummarize": {"values": [{"field": "id"}]}, "options": {}}, "type": "n8n-nodes-base.summarize", "typeVersion": 1.1, "position": [-1940, -220], "id": "ca9cf6f8-eabc-4462-9683-da3fdfe3f21f", "name": "Summarize1"}, {"parameters": {"respondWith": "json", "responseBody": "{\n  \"output\": \"Rate limit exceeded!\"\n}", "options": {}}, "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1.1, "position": [-1540, 20], "id": "daa4505e-ed96-47e3-9465-85015258cf3f", "name": "Respond to Webhook1"}, {"parameters": {"tableId": "requests", "fieldsUi": {"fieldValues": [{"fieldId": "id", "fieldValue": "={{ $('Webhook').item.json.body.request_id }}"}, {"fieldId": "user_id", "fieldValue": "={{ $('Webhook').item.json.body.user_id }}"}, {"fieldId": "user_query", "fieldValue": "={{ $('Webhook').item.json.body.query }}"}]}}, "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [-1320, -340], "id": "611063d5-1650-458c-b304-205f7f342a1b", "name": "Supabase3", "credentials": {"supabaseApi": {"id": "3tjCDwujGZ7BlK7R", "name": "Supabase account"}}}, {"parameters": {"url": "https://your-supabase-url/auth/v1/user", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Authorization", "value": "={{ $json.headers.authorization }}"}, {"name": "apikey", "value": "your-supabase-anon-key"}]}, "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [-2500, -360], "id": "b8ebf4bd-07e0-442d-bfa0-c2dd69f525d7", "name": "HTTP Request1"}], "pinData": {"Tool Start": [{"json": {"tool_type": "web_search", "query": "Best AI Agent Frameworks", "tool_type 2": "image_analysis", "query 2": "Describe this image", "image_path": "/data/shared/ArchonMCPThumbnail.jpg"}}]}, "connections": {"Extract Document Text": {"main": [[{"node": "Postgres PGVector Store", "type": "main", "index": 0}]]}, "Default Data Loader": {"ai_document": [[{"node": "Postgres PGVector Store", "type": "ai_document", "index": 0}]]}, "Postgres Chat Memory": {"ai_memory": [[{"node": "RAG AI Agent", "type": "ai_memory", "index": 0}]]}, "Set File ID": {"main": [[{"node": "Delete Old Doc Records", "type": "main", "index": 0}]]}, "Edit Fields": {"main": [[{"node": "RAG AI Agent", "type": "main", "index": 0}]]}, "When chat message received": {"main": [[{"node": "<PERSON>", "type": "main", "index": 0}]]}, "Webhook": {"main": [[{"node": "HTTP Request1", "type": "main", "index": 0}]]}, "Extract PDF Text": {"main": [[{"node": "Postgres PGVector Store", "type": "main", "index": 0}]]}, "Aggregate": {"main": [[{"node": "Summarize", "type": "main", "index": 0}]]}, "Summarize": {"main": [[{"node": "<PERSON>", "type": "main", "index": 0}, {"node": "Postgres PGVector Store", "type": "main", "index": 0}]]}, "RAG AI Agent": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 2}]]}, "Switch": {"main": [[{"node": "Extract PDF Text", "type": "main", "index": 0}], [{"node": "Extract from Excel", "type": "main", "index": 0}], [{"node": "Extract from CSV", "type": "main", "index": 0}], [{"node": "Extract Document Text", "type": "main", "index": 0}], [{"node": "Extract Document Text", "type": "main", "index": 0}]]}, "Extract from Excel": {"main": [[{"node": "Aggregate", "type": "main", "index": 0}, {"node": "Insert Table Rows", "type": "main", "index": 0}]]}, "Set Schema": {"main": [[{"node": "Update <PERSON><PERSON>a for Document Metadata", "type": "main", "index": 0}]]}, "Extract from CSV": {"main": [[{"node": "Aggregate", "type": "main", "index": 0}, {"node": "Insert Table Rows", "type": "main", "index": 0}]]}, "Create Document Metadata Table": {"main": [[]]}, "List Documents": {"ai_tool": [[{"node": "RAG AI Agent", "type": "ai_tool", "index": 0}]]}, "Get File Contents": {"ai_tool": [[{"node": "RAG AI Agent", "type": "ai_tool", "index": 0}]]}, "Query Document Rows": {"ai_tool": [[{"node": "RAG AI Agent", "type": "ai_tool", "index": 0}]]}, "Loop Over Items": {"main": [[], [{"node": "Set File ID", "type": "main", "index": 0}]]}, "Insert Document Metadata": {"main": [[{"node": "Read/Write Files from Disk", "type": "main", "index": 0}]]}, "Local File Trigger": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}]]}, "Read/Write Files from Disk": {"main": [[{"node": "Switch", "type": "main", "index": 0}]]}, "Embeddings Ollama": {"ai_embedding": [[{"node": "Postgres PGVector Store", "type": "ai_embedding", "index": 0}]]}, "Embeddings Ollama1": {"ai_embedding": [[{"node": "Document RAG Tool", "type": "ai_embedding", "index": 0}]]}, "Recursive Character Text Splitter": {"ai_textSplitter": [[{"node": "Default Data Loader", "type": "ai_textSplitter", "index": 0}]]}, "Ollama (Change Base URL)": {"ai_languageModel": [[{"node": "RAG AI Agent", "type": "ai_languageModel", "index": 0}]]}, "Delete Old Doc Records": {"main": [[{"node": "Delete Old Data Records", "type": "main", "index": 0}]]}, "Delete Old Data Records": {"main": [[{"node": "Insert Document Metadata", "type": "main", "index": 0}]]}, "Postgres PGVector Store": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}]]}, "Retrieve Memories Tool": {"ai_tool": [[{"node": "RAG AI Agent", "type": "ai_tool", "index": 0}]]}, "Embeddings Ollama2": {"ai_embedding": [[{"node": "Retrieve Memories Tool", "type": "ai_embedding", "index": 0}]]}, "Document RAG Tool": {"ai_tool": [[{"node": "RAG AI Agent", "type": "ai_tool", "index": 0}]]}, "Determine Tool Type": {"main": [[{"node": "Read/Write Files from Disk1", "type": "main", "index": 0}], [{"node": "SearXNG", "type": "main", "index": 0}]]}, "Tool Start": {"main": [[{"node": "Determine Tool Type", "type": "main", "index": 0}]]}, "Web Search Tool": {"ai_tool": [[{"node": "RAG AI Agent", "type": "ai_tool", "index": 0}]]}, "Image Analysis Tool": {"ai_tool": [[{"node": "RAG AI Agent", "type": "ai_tool", "index": 0}]]}, "Code Tool": {"ai_tool": [[{"node": "RAG AI Agent", "type": "ai_tool", "index": 0}]]}, "Structured Output Parser": {"ai_outputParser": [[{"node": "Basic LLM Chain", "type": "ai_outputParser", "index": 0}]]}, "Memory Agent": {"main": [[{"node": "Postgres", "type": "main", "index": 0}]]}, "If": {"main": [[{"node": "Memory Agent", "type": "main", "index": 0}], [{"node": "Edit Fields1", "type": "main", "index": 0}]]}, "Basic LLM Chain": {"main": [[{"node": "If", "type": "main", "index": 0}]]}, "Postgres": {"main": [[{"node": "Postgres PGVector Store1", "type": "main", "index": 0}]]}, "Respond to Webhook": {"main": [[{"node": "Basic LLM Chain", "type": "main", "index": 0}]]}, "Ollama Chat Model": {"ai_languageModel": [[{"node": "Basic LLM Chain", "type": "ai_languageModel", "index": 0}]]}, "Postgres PGVector Store1": {"main": [[{"node": "Edit Fields1", "type": "main", "index": 0}]]}, "Character Text Splitter": {"ai_textSplitter": [[{"node": "Default Data Loader1", "type": "ai_textSplitter", "index": 0}]]}, "Postgres PGVector Store2": {"ai_tool": [[{"node": "Memory Agent", "type": "ai_tool", "index": 0}]]}, "Embeddings Ollama3": {"ai_embedding": [[{"node": "Postgres PGVector Store2", "type": "ai_embedding", "index": 0}]]}, "Embeddings Ollama4": {"ai_embedding": [[{"node": "Postgres PGVector Store1", "type": "ai_embedding", "index": 0}]]}, "Default Data Loader1": {"ai_document": [[{"node": "Postgres PGVector Store1", "type": "ai_document", "index": 0}]]}, "Ollama (Change Base URL)1": {"ai_languageModel": [[{"node": "Memory Agent", "type": "ai_languageModel", "index": 0}]]}, "Structured Output Parser1": {"ai_outputParser": [[{"node": "Memory Agent", "type": "ai_outputParser", "index": 0}]]}, "Read/Write Files from Disk1": {"main": [[{"node": "Image Analysis", "type": "main", "index": 0}]]}, "Ollama Chat Model1": {"ai_languageModel": [[{"node": "Image Analysis", "type": "ai_languageModel", "index": 0}]]}, "SearXNG": {"main": [[{"node": "Split Out", "type": "main", "index": 0}]]}, "Split Out": {"main": [[{"node": "Edit Fields2", "type": "main", "index": 0}]]}, "Edit Fields2": {"main": [[{"node": "Limit", "type": "main", "index": 0}]]}, "Limit": {"main": [[{"node": "HTTP Request", "type": "main", "index": 0}]]}, "HTTP Request": {"main": [[{"node": "HTML", "type": "main", "index": 0}]]}, "HTML": {"main": [[{"node": "Aggregate1", "type": "main", "index": 0}]]}, "If1": {"main": [[{"node": "Edit Fields3", "type": "main", "index": 0}, {"node": "Supabase3", "type": "main", "index": 0}], [{"node": "<PERSON>", "type": "main", "index": 0}, {"node": "Supabase3", "type": "main", "index": 0}]]}, "Basic LLM Chain1": {"main": [[{"node": "Postgres2", "type": "main", "index": 0}]]}, "Edit Fields3": {"main": [[{"node": "Postgres1", "type": "main", "index": 0}]]}, "Merge": {"main": [[{"node": "Aggregate2", "type": "main", "index": 0}]]}, "Aggregate2": {"main": [[{"node": "Edit Fields4", "type": "main", "index": 0}]]}, "Edit Fields4": {"main": [[{"node": "Respond to Webhook", "type": "main", "index": 0}]]}, "Ollama Chat Model2": {"ai_languageModel": [[{"node": "Basic LLM Chain1", "type": "ai_languageModel", "index": 0}]]}, "Postgres1": {"main": [[{"node": "Basic LLM Chain1", "type": "main", "index": 0}, {"node": "<PERSON><PERSON>", "type": "main", "index": 1}, {"node": "<PERSON>", "type": "main", "index": 0}]]}, "Postgres2": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 0}]]}, "Supabase2": {"main": [[{"node": "Summarize1", "type": "main", "index": 0}]]}, "If2": {"main": [[{"node": "If1", "type": "main", "index": 0}], [{"node": "Respond to Webhook1", "type": "main", "index": 0}]]}, "If3": {"main": [[{"node": "Supabase2", "type": "main", "index": 0}], [{"node": "Stop and Error", "type": "main", "index": 0}]]}, "Summarize1": {"main": [[{"node": "If2", "type": "main", "index": 0}]]}, "HTTP Request1": {"main": [[{"node": "If3", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "54fa655e-9566-44ef-8bd6-cbe9232ffdc4", "meta": {"templateCredsSetupCompleted": true, "instanceId": "73cb7a3e883df514bb47e8d1b34526d30e2abb8f56cd99f10d5948a1e11b25aa"}, "id": "vBNenOjQbMmKoMTo", "tags": []}