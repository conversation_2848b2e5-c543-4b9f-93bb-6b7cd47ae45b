"""
Authentication routes.
"""
from fastapi import APIRouter

from ..auth import auth_backend, fastapi_users
from ..schemas.user import UserR<PERSON>, UserCreate, UserUpdate


def create_auth_router() -> APIRouter:
    """Create authentication router with all auth endpoints."""
    router = APIRouter()
    
    # Include FastAPI-Users auth routes
    router.include_router(
        fastapi_users.get_auth_router(auth_backend),
        prefix="/jwt",
        tags=["auth"]
    )
    
    # Include user registration routes
    router.include_router(
        fastapi_users.get_register_router(UserRead, UserCreate),
        prefix="/register",
        tags=["auth"]
    )
    
    # Include password reset routes
    router.include_router(
        fastapi_users.get_reset_password_router(),
        prefix="/reset-password",
        tags=["auth"]
    )
    
    # Include email verification routes
    router.include_router(
        fastapi_users.get_verify_router(UserRead),
        prefix="/verify",
        tags=["auth"]
    )
    
    return router