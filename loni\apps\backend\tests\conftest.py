"""
Test configuration and fixtures for LONI backend tests.

This module provides shared test fixtures and configuration
for the entire test suite.
"""

import asyncio
import os
from typing import AsyncGenerator, Generator
from uuid import uuid4

import pytest
import pytest_asyncio
from fastapi.testclient import TestClient
from httpx import AsyncClient
from sqlalchemy import create_engine
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine
from sqlalchemy.orm import sessionmaker
from sqlalchemy.pool import StaticPool

from core.config.application import get_settings
from core.database import DatabaseManager
from core.models.base import BaseModel
from core.models.user import User
from core.models.conversation import Conversation, Message
from core.models.document import Document
from core.models.ai_model import AIModel
from main import app


# Test database URL
TEST_DATABASE_URL = "sqlite+aiosqlite:///:memory:"


@pytest.fixture(scope="session")
def event_loop() -> Generator:
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture(scope="session")
async def test_engine():
    """Create test database engine."""
    engine = create_async_engine(
        TEST_DATABASE_URL,
        echo=False,
        poolclass=StaticPool,
        connect_args={"check_same_thread": False}
    )
    
    # Create all tables
    async with engine.begin() as conn:
        await conn.run_sync(BaseModel.metadata.create_all)
    
    yield engine
    
    # Cleanup
    await engine.dispose()


@pytest.fixture
async def test_session(test_engine) -> AsyncGenerator[AsyncSession, None]:
    """Create test database session."""
    async_session = sessionmaker(
        test_engine, class_=AsyncSession, expire_on_commit=False
    )
    
    async with async_session() as session:
        yield session
        await session.rollback()


@pytest.fixture
def test_client() -> TestClient:
    """Create test client."""
    return TestClient(app)


@pytest.fixture
async def async_client() -> AsyncGenerator[AsyncClient, None]:
    """Create async test client."""
    async with AsyncClient(app=app, base_url="http://test") as client:
        yield client


@pytest.fixture
async def test_user(test_session: AsyncSession) -> User:
    """Create test user."""
    user = User(
        email="<EMAIL>",
        hashed_password="hashed_password",
        name="Test User",
        is_active=True,
        is_verified=True
    )
    test_session.add(user)
    await test_session.commit()
    await test_session.refresh(user)
    return user


@pytest.fixture
async def test_superuser(test_session: AsyncSession) -> User:
    """Create test superuser."""
    user = User(
        email="<EMAIL>",
        hashed_password="hashed_password",
        name="Admin User",
        is_active=True,
        is_verified=True,
        is_superuser=True
    )
    test_session.add(user)
    await test_session.commit()
    await test_session.refresh(user)
    return user


@pytest.fixture
async def test_conversation(test_session: AsyncSession, test_user: User) -> Conversation:
    """Create test conversation."""
    conversation = Conversation(
        title="Test Conversation",
        user_id=test_user.id,
        model_name="gpt-4",
        temperature=0.7
    )
    test_session.add(conversation)
    await test_session.commit()
    await test_session.refresh(conversation)
    return conversation


@pytest.fixture
async def test_message(test_session: AsyncSession, test_conversation: Conversation) -> Message:
    """Create test message."""
    message = Message(
        conversation_id=test_conversation.id,
        role="user",
        content="Hello, how are you?"
    )
    test_session.add(message)
    await test_session.commit()
    await test_session.refresh(message)
    return message


@pytest.fixture
async def test_document(test_session: AsyncSession, test_user: User) -> Document:
    """Create test document."""
    document = Document(
        title="Test Document",
        content="This is a test document content.",
        content_type="text/plain",
        user_id=test_user.id
    )
    test_session.add(document)
    await test_session.commit()
    await test_session.refresh(document)
    return document


@pytest.fixture
async def test_ai_model(test_session: AsyncSession) -> AIModel:
    """Create test AI model."""
    model = AIModel(
        name="gpt-4",
        display_name="GPT-4",
        provider="openai",
        model_type="chat",
        capabilities={"streaming": True, "function_calling": True},
        context_length=8192,
        is_enabled=True
    )
    test_session.add(model)
    await test_session.commit()
    await test_session.refresh(model)
    return model


@pytest.fixture
def mock_openai_response():
    """Mock OpenAI API response."""
    return {
        "id": "chatcmpl-123",
        "object": "chat.completion",
        "created": **********,
        "model": "gpt-4",
        "choices": [
            {
                "index": 0,
                "message": {
                    "role": "assistant",
                    "content": "Hello! I'm doing well, thank you for asking."
                },
                "finish_reason": "stop"
            }
        ],
        "usage": {
            "prompt_tokens": 10,
            "completion_tokens": 12,
            "total_tokens": 22
        }
    }


@pytest.fixture
def mock_anthropic_response():
    """Mock Anthropic API response."""
    return {
        "id": "msg_123",
        "type": "message",
        "role": "assistant",
        "content": [
            {
                "type": "text",
                "text": "Hello! I'm doing well, thank you for asking."
            }
        ],
        "model": "claude-3-sonnet-20240229",
        "stop_reason": "end_turn",
        "stop_sequence": None,
        "usage": {
            "input_tokens": 10,
            "output_tokens": 12
        }
    }


@pytest.fixture
def mock_ollama_response():
    """Mock Ollama API response."""
    return {
        "model": "llama3.2:latest",
        "message": {
            "role": "assistant",
            "content": "Hello! I'm doing well, thank you for asking."
        },
        "done": True,
        "total_duration": 1000000000,
        "load_duration": 100000000,
        "prompt_eval_count": 10,
        "prompt_eval_duration": 200000000,
        "eval_count": 12,
        "eval_duration": 300000000
    }


@pytest.fixture
def test_settings():
    """Override settings for testing."""
    settings = get_settings()
    settings.database.url = TEST_DATABASE_URL
    settings.testing = True
    return settings


# Test data factories
class UserFactory:
    """Factory for creating test users."""
    
    @staticmethod
    def create_user_data(**kwargs):
        """Create user data for testing."""
        default_data = {
            "email": f"user_{uuid4().hex[:8]}@example.com",
            "password": "testpassword123",
            "name": "Test User",
            "is_active": True,
            "is_verified": True
        }
        default_data.update(kwargs)
        return default_data


class ConversationFactory:
    """Factory for creating test conversations."""
    
    @staticmethod
    def create_conversation_data(user_id, **kwargs):
        """Create conversation data for testing."""
        default_data = {
            "title": "Test Conversation",
            "user_id": user_id,
            "model_name": "gpt-4",
            "temperature": 0.7,
            "rag_enabled": True
        }
        default_data.update(kwargs)
        return default_data


class MessageFactory:
    """Factory for creating test messages."""
    
    @staticmethod
    def create_message_data(conversation_id, **kwargs):
        """Create message data for testing."""
        default_data = {
            "conversation_id": conversation_id,
            "role": "user",
            "content": "Test message content"
        }
        default_data.update(kwargs)
        return default_data


class DocumentFactory:
    """Factory for creating test documents."""
    
    @staticmethod
    def create_document_data(user_id, **kwargs):
        """Create document data for testing."""
        default_data = {
            "title": "Test Document",
            "content": "This is test document content.",
            "content_type": "text/plain",
            "user_id": user_id
        }
        default_data.update(kwargs)
        return default_data


# Test utilities
def assert_response_success(response, expected_status=200):
    """Assert that response is successful."""
    assert response.status_code == expected_status
    assert response.headers["content-type"] == "application/json"


def assert_response_error(response, expected_status=400):
    """Assert that response is an error."""
    assert response.status_code == expected_status
    data = response.json()
    assert "detail" in data or "error" in data


def assert_model_fields(model_dict, expected_fields):
    """Assert that model dictionary contains expected fields."""
    for field in expected_fields:
        assert field in model_dict, f"Field '{field}' missing from model"


# Pytest configuration
def pytest_configure(config):
    """Configure pytest."""
    # Set test environment variables
    os.environ["TESTING"] = "true"
    os.environ["DATABASE_URL"] = TEST_DATABASE_URL
