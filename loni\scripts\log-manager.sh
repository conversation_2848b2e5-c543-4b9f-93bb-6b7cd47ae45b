#!/bin/bash

# LONI Smart Log Management System
# Provides AI observability through intelligent log collection and management

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
LOG_DIR="/mnt/e/Projects/lonors/loni/data/logs"
TIMESTAMP=$(date '+%Y-%m-%d %H:%M:%S')
MAX_LOG_SIZE="10M"
RETENTION_DAYS=7

# Function to print colored output
print_status() {
    echo -e "${GREEN}[LOG-INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[LOG-WARN]${NC} $1"
}

print_error() {
    echo -e "${RED}[LOG-ERROR]${NC} $1"
}

print_header() {
    echo -e "${BLUE}=== $1 ===${NC}"
}

# Initialize log directory structure
init_log_structure() {
    print_header "Initializing Log Structure"
    
    # Create directory structure
    mkdir -p "$LOG_DIR"/{containers,development/{frontend,backend},applications,system}
    
    # Create initial log files with headers
    for category in containers applications system; do
        for service in postgres redis qdrant prometheus grafana n8n ollama nginx frontend backend; do
            log_file="$LOG_DIR/$category/$service.log"
            if [[ ! -f "$log_file" ]]; then
                echo "# LONI $service Log - Created: $TIMESTAMP" > "$log_file"
                echo "# This log updates on each startup and contains only errors/warnings" >> "$log_file"
                echo "" >> "$log_file"
            fi
        done
    done
    
    # Create development tool logs
    for tool in lint typecheck build test coverage; do
        echo "# LONI Frontend $tool Log - Created: $TIMESTAMP" > "$LOG_DIR/development/frontend/$tool.log"
        echo "# LONI Backend $tool Log - Created: $TIMESTAMP" > "$LOG_DIR/development/backend/$tool.log"
    done
    
    print_status "Log structure initialized"
}

# Update log file with new content (smart update)
update_log() {
    local log_file="$1"
    local content="$2"
    local source="$3"
    
    # Create temporary file
    local temp_file=$(mktemp)
    
    # Write header
    echo "# LONI Log Update - $TIMESTAMP" > "$temp_file"
    echo "# Source: $source" >> "$temp_file"
    echo "# Status: $([ -n "$content" ] && echo "ERRORS_DETECTED" || echo "CLEAN")" >> "$temp_file"
    echo "" >> "$temp_file"
    
    # Add content if exists
    if [[ -n "$content" ]]; then
        echo "$content" >> "$temp_file"
    else
        echo "# No errors detected during last run" >> "$temp_file"
    fi
    
    # Replace original file
    mv "$temp_file" "$log_file"
    
    # Set appropriate permissions
    chmod 644 "$log_file"
}

# Container health check and logging
check_container_health() {
    print_header "Container Health Check"
    
    local containers=("loni-postgres" "loni-redis" "loni-qdrant" "loni-prometheus" "loni-grafana" "loni-n8n" "loni-ollama" "loni-nginx" "loni-frontend" "loni-backend")
    
    for container in "${containers[@]}"; do
        local service_name=$(echo "$container" | sed 's/loni-//')
        local log_file="$LOG_DIR/containers/$service_name.log"
        
        if docker ps --format "table {{.Names}}" | grep -q "^$container$"; then
            # Container is running, check for errors
            local errors=$(docker logs "$container" --since="5m" 2>&1 | grep -i "error\|fail\|exception\|fatal" || true)
            
            if [[ -n "$errors" ]]; then
                print_warning "$container has errors"
                update_log "$log_file" "$errors" "docker logs $container"
            else
                print_status "$container is healthy"
                update_log "$log_file" "" "docker logs $container"
            fi
        else
            print_error "$container is not running"
            update_log "$log_file" "Container not running at $TIMESTAMP" "container status check"
        fi
    done
}

# Generate AI-friendly summary
generate_ai_summary() {
    local summary_file="$LOG_DIR/AI_OBSERVABILITY_SUMMARY.json"
    
    print_header "Generating AI Observability Summary"
    
    # Create JSON summary for AI agents
    cat > "$summary_file" << EOF
{
  "timestamp": "$TIMESTAMP",
  "system_status": {
    "docker_available": $(docker info >/dev/null 2>&1 && echo "true" || echo "false"),
    "containers_running": $(docker ps --format "table {{.Names}}" | grep "loni-" | wc -l),
    "total_containers_expected": 10
  },
  "error_summary": {
    "container_errors": $(find "$LOG_DIR/containers" -name "*.log" -exec grep -l "error\|fail\|exception" {} \; 2>/dev/null | wc -l),
    "application_errors": $(find "$LOG_DIR/applications" -name "*.log" -exec grep -l "error\|fail\|exception" {} \; 2>/dev/null | wc -l),
    "development_errors": $(find "$LOG_DIR/development" -name "*.log" -exec grep -l "error\|fail\|exception" {} \; 2>/dev/null | wc -l)
  },
  "log_locations": {
    "containers": "$LOG_DIR/containers/",
    "applications": "$LOG_DIR/applications/",
    "development": "$LOG_DIR/development/",
    "system": "$LOG_DIR/system/"
  },
  "ai_recommendations": [
    "Check container logs for startup issues",
    "Review development tool outputs for code quality",
    "Monitor application logs for runtime errors",
    "Verify system resources and Docker health"
  ]
}
EOF
    
    print_status "AI observability summary generated: $summary_file"
}

# Main execution function
main() {
    case "${1:-init}" in
        "init")
            init_log_structure
            ;;
        "containers")
            check_container_health
            ;;
        "summary")
            generate_ai_summary
            ;;
        "test")
            init_log_structure
            check_container_health
            generate_ai_summary
            ;;
        *)
            echo "Usage: $0 {init|containers|summary|test}"
            echo ""
            echo "Commands:"
            echo "  init         - Initialize log directory structure"
            echo "  containers   - Check Docker container health"
            echo "  summary      - Generate AI observability summary"
            echo "  test         - Run basic test (init + containers + summary)"
            exit 1
            ;;
    esac
    
    print_status "Log management completed at $TIMESTAMP"
}

# Run main function
main "$@"