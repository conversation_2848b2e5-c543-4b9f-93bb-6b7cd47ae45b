# ---------------------------------------------------------------------------
# RAG PIPELINE SERVICE ENVIRONMENT VARIABLES
# ---------------------------------------------------------------------------
# These variables are used by the RAG pipeline background worker.
# Copy these values to your Render rag-pipeline service environment settings.

# ---------------------------------------------------------------------------
# RAG PIPELINE CONFIGURATION
# ---------------------------------------------------------------------------

# Controls which pipeline to run:
# - "local": Watch local files in mounted volume
# - "google_drive": Watch Google Drive files via API
RAG_PIPELINE_TYPE=google_drive

# Controls how the RAG pipeline runs:
# - "continuous": Runs continuously, checking for changes at regular intervals
# - "single": Performs one check for changes and exits (for cron jobs, cloud schedulers)
RUN_MODE=continuous

# Interval in seconds between checks (continuous mode only)
# Recommended: 300 (5 minutes) for production, 60 for development
CHECK_INTERVAL=300

# Unique identifier for this pipeline instance (required for single-run mode)
# Used for database state management to track last_check_time and known_files
# Examples: "prod-drive-pipeline", "dev-local-pipeline", "staging-pipeline"
# For Render deployment, use: "render-web"
RAG_PIPELINE_ID=render-web

# ---------------------------------------------------------------------------
# DATABASE CONFIGURATION
# ---------------------------------------------------------------------------

# Your Supabase project URL
# Get this from: Supabase Dashboard -> Project Settings -> API
# https://supabase.com/dashboard/project/<project-id>/settings/api
# Format: https://YOUR_SUPABASE_PROJECT_ID.supabase.co
# Example: https://abcd1234xyz.supabase.co
SUPABASE_URL=https://YOUR_SUPABASE_PROJECT_ID.supabase.co

# Your Supabase service role key (secret key)
# Get this from: Supabase Dashboard -> Project Settings -> API
# https://supabase.com/dashboard/project/<project-id>/settings/api
# WARNING: Keep this secret! This key has full database access.
SUPABASE_SERVICE_KEY=YOUR_SUPABASE_SERVICE_KEY

# ---------------------------------------------------------------------------
# EMBEDDING CONFIGURATION
# ---------------------------------------------------------------------------

# Base URL for the OpenAI compatible instance that has embedding models (default is https://api.openai.com/v1)
# OpenAI: https://api.openai.com/v1
# Ollama (example): http://localhost:11434/v1
EMBEDDING_BASE_URL=https://api.openai.com/v1

# OpenAI API key for embedding generation
# Get this from: https://help.openai.com/en/articles/4936850-where-do-i-find-my-openai-api-key
# This should be the same key used in your agent-api service
EMBEDDING_API_KEY=YOUR_OPENAI_API_KEY

# The embedding model you want to use for RAG.
# Make sure the embeddings column in your database has the same dimensions as this embedding model!
# OpenAI example: text-embedding-3-small (1536 dimensions)
# Ollama example: nomic-embed-text (768 dimensions)
EMBEDDING_MODEL_CHOICE=text-embedding-3-small

# ---------------------------------------------------------------------------
# GOOGLE DRIVE CONFIGURATION (for RAG Pipeline)
# ---------------------------------------------------------------------------

# Google Drive folder ID to watch for document changes
# Get folder ID from Google Drive URL: https://drive.google.com/drive/folders/FOLDER_ID_HERE
# Example: If URL is https://drive.google.com/drive/folders/1A2B3C4D5E6F7G8H9I0J
# Then RAG_WATCH_FOLDER_ID=1A2B3C4D5E6F7G8H9I0J
RAG_WATCH_FOLDER_ID=YOUR_GOOGLE_DRIVE_FOLDER_ID

# Google Drive service account credentials (JSON format as string)
# Required for serverless/cloud deployment without interactive OAuth2
# Steps to get this:
# 1. Go to Google Cloud Console -> Service Accounts
# 2. Create a new service account or select existing one
# 3. Create a key (JSON format) for the service account
# 4. Enable Google Drive API for your project
# 5. Share your Google Drive folder with the service account email
# 6. Copy the entire JSON content as a string (remove line breaks)
# Example: {"type":"service_account","project_id":"your-project",...}
# For Render: Paste the entire JSON content within single quotes ('')
GOOGLE_DRIVE_CREDENTIALS_JSON='YOUR_GOOGLE_SERVICE_ACCOUNT_JSON'