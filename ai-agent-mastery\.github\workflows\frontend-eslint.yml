name: Frontend ESLint

on:
  workflow_call:
    inputs:
      node-version:
        description: 'Node.js version to use'
        required: false
        default: '18'
        type: string
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]
  workflow_dispatch:

jobs:
  eslint:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Set up Node.js ${{ inputs.node-version || '18' }}
      uses: actions/setup-node@v4
      with:
        node-version: ${{ inputs.node-version || '18' }}
        cache: 'npm'
        cache-dependency-path: '6_Agent_Deployment/frontend/package-lock.json'
        
    - name: Install dependencies
      run: |
        cd 6_Agent_Deployment/frontend
        npm ci
        
    - name: Run ESLint
      run: |
        cd 6_Agent_Deployment/frontend
        npm run lint