# DYNAMOUS COMMUNITY - PROPRIETARY LICENSE

Copyright (c) 2025 Cole Medin / Dynamous

## TERMS AND CONDITIONS

1. **GRANT OF LICENSE**: This code (and other resources in the repository) is licensed exclusively to paying members of the Dynamous AI Mastery community.

2. **RESTRICTED ACCESS**: Access to this repository and its contents is restricted to current, active paying members of the Dynamous community. It may not be shared with anyone outside of currently paying Dynamous community members in any way. No forking this repository to share with others or downloading the code and sending it to non-members.

3. **USAGE RIGHTS**: Members are permitted to use the code for personal and commercial projects. Members may modify the code for their own use.

4. **RESTRICTIONS**:
   - Members may not redistribute, share, publish, or otherwise make available any portion of this code outside of the Dynamous community.
   - Members may not sublicense, sell, rent, or lease the code to any third party.
   - Members may not remove or alter any copyright notices or attributions in the code.

5. **TERMINATION**: This license automatically terminates if a member cancels their Dynamous membership or violates any of these terms. Upon termination, the member must cease all use of the code and delete all copies. The exception to this which applies to a membership cancellation can be read in #9.

6. **OWNERSHIP**: All intellectual property rights to the code, including copyright, remain with <PERSON>din / Dynamous. This license does not transfer ownership of the code.

7. **NO WARRANTY**: The code is provided "as is" without warranty of any kind, express or implied. Cole Medin / Dynamous is not liable for any damages arising from the use of this code.

8. **UPDATES**: Cole Medin / Dynamous reserves the right to update or modify this license at any time. Continued use of the code after any modification constitutes acceptance of the new terms.

9. **LEGACY ACCESS**: If a member has maintained active paid membership for at least 3 months, they will retain personal usage rights to any code they accessed during their membership period, even after their membership ends. However, redistribution restrictions remain in effect permanently.

10. **COMMUNITY SUNSET PROVISION**: In the event that the Dynamous community officially closes or becomes inactive (defined as no updates to the repository for 12 consecutive months), all current and former members who maintained membership in good standing will automatically receive a perpetual license to use the code for personal and commercial projects, though redistribution restrictions will remain in effect.

By accessing or using this repository, you acknowledge that you have read this license, understand it, and agree to be bound by its terms.