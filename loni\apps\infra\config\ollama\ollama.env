# Ollama configuration for LONI platform

# Server configuration
OLLAMA_HOST=0.0.0.0:11434
OLLAMA_ORIGINS=*

# Resource limits
OLLAMA_MAX_LOADED_MODELS=3
OLLAMA_NUM_PARALLEL=2
OLLAMA_MAX_QUEUE=512

# Logging
OLLAMA_DEBUG=false
OLLAMA_VERBOSE=false

# Model storage
OLLAMA_MODELS=/root/.ollama/models

# GPU configuration (optional)
OLLAMA_GPU_LAYERS=-1
OLLAMA_CUDA_VISIBLE_DEVICES=all

# Memory settings
OLLAMA_MAX_VRAM=4GB