"""
Validation services for template dependencies and configuration.

These validators implement specific validation logic while following
the Single Responsibility Principle.
"""

from typing import Set, List, Dict, Any, Optional
import re
from pathlib import Path

from .interfaces import IDependencyValidator, IConfigurationValidator
from .models import ValidationResult, TemplateMetadata, ConflictType, ValidationSeverity
from .registry import TemplateRegistry


class DependencyValidator(IDependencyValidator):
    """Validates template dependencies and conflicts."""
    
    def __init__(self, template_registry: 'TemplateRegistry'):
        """Initialize with template registry."""
        self.template_registry = template_registry
        
        # Define known conflicts
        self._mutual_exclusions = {
            # Package managers - only one can be used
            frozenset(['npm', 'pnpm', 'bun', 'yarn']),
            
            # Desktop frameworks - mutually exclusive
            frozenset(['tauri', 'electron']),
            
            # Database types (if using single database)
            frozenset(['postgresql', 'mysql', 'mongodb']) if False else set(),  # Disabled for now
            
            # Frontend frameworks (if not micro-frontend)
            frozenset(['react', 'vue', 'angular', 'svelte']) if False else set(),  # Disabled for now
        }
    
    def validate(self, selected_templates: Set[str]) -> ValidationResult:
        """Validate template selection."""
        return self.validate_dependencies(selected_templates)
    
    def validate_dependencies(self, selected_templates: Set[str]) -> ValidationResult:
        """Validate template dependencies and conflicts."""
        result = ValidationResult(is_valid=True)
        
        # Check for missing dependencies
        missing = self.get_missing_dependencies(selected_templates)
        for missing_dep in missing:
            result.add_error(
                f"Missing required dependency: {missing_dep}",
                suggestion=f"Add '{missing_dep}' to your selection"
            )
        
        # Check for conflicts
        conflicts = self.get_conflicts(selected_templates)
        for template1, template2 in conflicts:
            result.add_error(
                f"Templates '{template1}' and '{template2}' are mutually exclusive",
                suggestion=f"Choose either '{template1}' or '{template2}', but not both"
            )
        
        # Check for mutual exclusions
        self._check_mutual_exclusions(selected_templates, result)
        
        # Check for port conflicts
        self._check_port_conflicts(selected_templates, result)
        
        # Check for volume conflicts
        self._check_volume_conflicts(selected_templates, result)
        
        return result
    
    def get_missing_dependencies(self, selected_templates: Set[str]) -> List[str]:
        """Get list of missing required dependencies."""
        missing = []
        all_dependencies = set()
        
        for template_id in selected_templates:
            template = self.template_registry.get_template(template_id)
            if template:
                metadata = template.get_metadata()
                all_dependencies.update(metadata.dependencies)
        
        # Find dependencies not in selected templates
        for dep in all_dependencies:
            if dep not in selected_templates:
                missing.append(dep)
        
        return missing
    
    def get_conflicts(self, selected_templates: Set[str]) -> List[tuple[str, str]]:
        """Get list of conflicting template pairs."""
        conflicts = []
        
        for template_id in selected_templates:
            template = self.template_registry.get_template(template_id)
            if template:
                metadata = template.get_metadata()
                for conflict in metadata.conflicts:
                    if conflict in selected_templates:
                        # Avoid duplicate pairs
                        conflict_pair = tuple(sorted([template_id, conflict]))
                        if conflict_pair not in conflicts:
                            conflicts.append(conflict_pair)
        
        return conflicts
    
    def _check_mutual_exclusions(self, selected_templates: Set[str], result: ValidationResult):
        """Check for mutually exclusive template groups."""
        for exclusion_group in self._mutual_exclusions:
            if len(exclusion_group) == 0:  # Skip empty groups
                continue
                
            selected_in_group = exclusion_group.intersection(selected_templates)
            if len(selected_in_group) > 1:
                templates_str = "', '".join(selected_in_group)
                result.add_error(
                    f"Templates '{templates_str}' are mutually exclusive",
                    suggestion=f"Choose only one from: {', '.join(exclusion_group)}"
                )
    
    def _check_port_conflicts(self, selected_templates: Set[str], result: ValidationResult):
        """Check for port conflicts between templates."""
        port_usage = {}  # port -> [template_ids]
        
        for template_id in selected_templates:
            template = self.template_registry.get_template(template_id)
            if template:
                metadata = template.get_metadata()
                for port in metadata.required_ports:
                    if port not in port_usage:
                        port_usage[port] = []
                    port_usage[port].append(template_id)
        
        # Check for conflicts
        for port, templates in port_usage.items():
            if len(templates) > 1:
                templates_str = "', '".join(templates)
                result.add_error(
                    f"Port {port} is required by multiple templates: '{templates_str}'",
                    suggestion=f"Configure different ports for conflicting services"
                )
    
    def _check_volume_conflicts(self, selected_templates: Set[str], result: ValidationResult):
        """Check for volume conflicts between templates."""
        volume_usage = {}  # volume -> [template_ids]
        
        for template_id in selected_templates:
            template = self.template_registry.get_template(template_id)
            if template:
                metadata = template.get_metadata()
                for volume in metadata.volumes:
                    if volume not in volume_usage:
                        volume_usage[volume] = []
                    volume_usage[volume].append(template_id)
        
        # Check for conflicts (same volume used by multiple templates might be intentional)
        for volume, templates in volume_usage.items():
            if len(templates) > 1:
                templates_str = "', '".join(templates)
                result.add_warning(
                    f"Volume '{volume}' is used by multiple templates: '{templates_str}'",
                    suggestion="Ensure volume sharing is intentional"
                )


class ConfigurationValidator(IConfigurationValidator):
    """Validates project configuration and environment variables."""
    
    def __init__(self, template_registry: 'TemplateRegistry'):
        """Initialize with template registry."""
        self.template_registry = template_registry
        
        # Security patterns to check for
        self._security_patterns = {
            'password_in_plain': re.compile(r'password\s*[:=]\s*["\']?[^"\'\s]+["\']?', re.IGNORECASE),
            'api_key_in_plain': re.compile(r'api[_-]?key\s*[:=]\s*["\']?[^"\'\s]+["\']?', re.IGNORECASE),
            'secret_in_plain': re.compile(r'secret\s*[:=]\s*["\']?[^"\'\s]+["\']?', re.IGNORECASE),
        }
    
    def validate(self, config: Dict[str, Any]) -> ValidationResult:
        """Validate configuration dictionary."""
        return self.validate_configuration(config)
    
    def validate_configuration(self, config: Dict[str, Any]) -> ValidationResult:
        """Validate project configuration for completeness and correctness."""
        result = ValidationResult(is_valid=True)
        
        # Check required configuration keys
        required_keys = ['project_name', 'selected_templates']
        for key in required_keys:
            if key not in config:
                result.add_error(
                    f"Missing required configuration key: {key}",
                    suggestion=f"Add '{key}' to your configuration"
                )
        
        # Validate project name
        if 'project_name' in config:
            self._validate_project_name(config['project_name'], result)
        
        # Validate selected templates
        if 'selected_templates' in config:
            self._validate_selected_templates(config['selected_templates'], result)
        
        # Validate environment variables if present
        if 'environment_variables' in config:
            env_result = self.validate_environment_variables(config['environment_variables'])
            result.issues.extend(env_result.issues)
            result.warnings.extend(env_result.warnings)
        
        # Check for security issues
        self._check_security_issues(config, result)
        
        return result
    
    def validate_environment_variables(self, env_vars: Dict[str, str]) -> ValidationResult:
        """Validate environment variable configuration."""
        result = ValidationResult(is_valid=True)
        
        # Check for required environment variables
        self._check_required_env_vars(env_vars, result)
        
        # Check for security issues in environment variables
        self._check_env_var_security(env_vars, result)
        
        # Validate environment variable formats
        self._validate_env_var_formats(env_vars, result)
        
        return result
    
    def _validate_project_name(self, project_name: str, result: ValidationResult):
        """Validate project name format and restrictions."""
        if not project_name:
            result.add_error("Project name cannot be empty")
            return
        
        # Check for valid characters
        if not re.match(r'^[a-zA-Z0-9][a-zA-Z0-9_-]*$', project_name):
            result.add_error(
                "Project name must start with alphanumeric character and contain only letters, numbers, hyphens, and underscores",
                suggestion="Use format like 'my-project' or 'my_project'"
            )
        
        # Check length
        if len(project_name) < 2:
            result.add_error("Project name must be at least 2 characters long")
        
        if len(project_name) > 50:
            result.add_warning("Project name is quite long, consider shortening it")
        
        # Check for reserved names
        reserved_names = {'con', 'prn', 'aux', 'nul', 'com1', 'com2', 'lpt1', 'lpt2'}
        if project_name.lower() in reserved_names:
            result.add_error(
                f"'{project_name}' is a reserved name",
                suggestion="Choose a different project name"
            )
    
    def _validate_selected_templates(self, selected_templates, result: ValidationResult):
        """Validate selected templates list."""
        if not selected_templates:
            result.add_error("At least one template must be selected")
            return
        
        if not isinstance(selected_templates, (list, set)):
            result.add_error("Selected templates must be a list or set")
            return
        
        # Check if templates exist
        for template_id in selected_templates:
            if not isinstance(template_id, str):
                result.add_error(f"Template ID must be a string, got {type(template_id)}")
                continue
                
            template = self.template_registry.get_template(template_id)
            if not template:
                result.add_error(
                    f"Unknown template: {template_id}",
                    suggestion="Check available templates with list command"
                )
    
    def _check_required_env_vars(self, env_vars: Dict[str, str], result: ValidationResult):
        """Check for required environment variables."""
        # This would be implemented based on selected templates
        # For now, we'll check for some common required variables
        
        common_required = {
            'POSTGRES_PASSWORD': 'Database password is required for PostgreSQL',
            'JWT_SECRET': 'JWT secret is required for authentication',
        }
        
        for var, description in common_required.items():
            if var not in env_vars or not env_vars[var]:
                result.add_warning(f"Missing environment variable: {var} - {description}")
    
    def _check_env_var_security(self, env_vars: Dict[str, str], result: ValidationResult):
        """Check environment variables for security issues."""
        for var_name, var_value in env_vars.items():
            # Check for weak passwords
            if 'password' in var_name.lower() and var_value:
                if len(var_value) < 8:
                    result.add_warning(
                        f"Password in {var_name} is short",
                        suggestion="Use at least 8 characters for passwords"
                    )
                
                if var_value in ['password', '123456', 'admin', 'root']:
                    result.add_error(
                        f"Weak password detected in {var_name}",
                        suggestion="Use a strong, unique password"
                    )
            
            # Check for hardcoded secrets
            for pattern_name, pattern in self._security_patterns.items():
                if pattern.search(f"{var_name}={var_value}"):
                    result.add_warning(
                        f"Potential security issue: {pattern_name} in {var_name}",
                        suggestion="Ensure secrets are properly managed"
                    )
    
    def _validate_env_var_formats(self, env_vars: Dict[str, str], result: ValidationResult):
        """Validate environment variable formats."""
        for var_name, var_value in env_vars.items():
            # Check variable name format
            if not re.match(r'^[A-Z][A-Z0-9_]*$', var_name):
                result.add_warning(
                    f"Environment variable '{var_name}' doesn't follow naming conventions",
                    suggestion="Use UPPER_CASE_WITH_UNDERSCORES format"
                )
            
            # Check for URL format validation
            if var_name.endswith('_URL') and var_value:
                if not self._is_valid_url(var_value):
                    result.add_warning(
                        f"'{var_name}' doesn't appear to be a valid URL",
                        suggestion="Ensure URL format is correct (e.g., http://localhost:5432)"
                    )
    
    def _check_security_issues(self, config: Dict[str, Any], result: ValidationResult):
        """Check configuration for security issues."""
        # Check for development settings in production
        if config.get('environment') == 'production':
            if config.get('debug', False):
                result.add_error(
                    "Debug mode should not be enabled in production",
                    suggestion="Set debug=false for production environments"
                )
            
            if config.get('allow_custom_templates', True):
                result.add_warning(
                    "Custom templates are allowed in production",
                    suggestion="Consider disabling custom templates for security"
                )
    
    def _is_valid_url(self, url: str) -> bool:
        """Check if string is a valid URL format."""
        url_pattern = re.compile(
            r'^https?://'  # http:// or https://
            r'(?:(?:[A-Z0-9](?:[A-Z0-9-]{0,61}[A-Z0-9])?\.)+[A-Z]{2,6}\.?|'  # domain
            r'localhost|'  # localhost
            r'\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})'  # IP
            r'(?::\d+)?'  # optional port
            r'(?:/?|[/?]\S+)$', re.IGNORECASE)
        
        return url_pattern.match(url) is not None


class SecurityValidator(IConfigurationValidator):
    """Specialized validator for security-related checks."""
    
    def __init__(self):
        """Initialize security validator."""
        self._dangerous_patterns = [
            r'\$\{.*\}',  # Environment variable injection
            r'eval\(',    # Code evaluation
            r'exec\(',    # Code execution
            r'__import__', # Dynamic imports
            r'subprocess\.', # Subprocess calls
        ]
    
    def validate(self, template_content: str) -> ValidationResult:
        """Validate template content for security vulnerabilities."""
        result = ValidationResult(is_valid=True)
        
        for pattern in self._dangerous_patterns:
            if re.search(pattern, template_content):
                result.add_error(
                    f"Potentially dangerous pattern detected: {pattern}",
                    suggestion="Review template content for security vulnerabilities"
                )
        
        return result
    
    def validate_configuration(self, config: Dict[str, Any]) -> ValidationResult:
        """Validate configuration for security issues."""
        result = ValidationResult(is_valid=True)
        
        # Check for insecure defaults
        if config.get('allow_arbitrary_code_execution', False):
            result.add_error(
                "Arbitrary code execution is enabled",
                suggestion="Disable arbitrary code execution for security"
            )
        
        return result
    
    def validate_environment_variables(self, env_vars: Dict[str, str]) -> ValidationResult:
        """Validate environment variables for security issues."""
        result = ValidationResult(is_valid=True)
        
        # Check for exposed secrets
        for var_name, var_value in env_vars.items():
            if any(keyword in var_name.lower() for keyword in ['password', 'secret', 'key', 'token']):
                if not var_value or var_value == 'changeme':
                    result.add_warning(
                        f"Security credential '{var_name}' has default or empty value",
                        suggestion="Set a secure value for this credential"
                    )
        
        return result