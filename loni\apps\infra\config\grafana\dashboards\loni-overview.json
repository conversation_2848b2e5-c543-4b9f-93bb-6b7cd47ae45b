{"dashboard": {"id": null, "title": "LONI Platform Overview", "tags": ["loni", "overview", "ai", "platform"], "timezone": "", "panels": [{"id": 1, "title": "API Request Rate", "type": "stat", "targets": [{"expr": "rate(http_requests_total{job=\"loni-backend\"}[5m])", "refId": "A"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"displayMode": "list", "orientation": "horizontal"}, "mappings": [], "thresholds": {"steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}}, "options": {"reduceOptions": {"values": false, "calcs": ["lastNotNull"], "fields": ""}, "orientation": "auto", "textMode": "auto", "colorMode": "value", "graphMode": "area", "justifyMode": "auto"}, "pluginVersion": "9.0.0", "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0}}, {"id": 2, "title": "AI Model Usage", "type": "piechart", "targets": [{"expr": "sum by (model) (ai_model_requests_total)", "refId": "A"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"hideFrom": {"legend": false, "tooltip": false, "vis": false}}, "mappings": []}}, "options": {"reduceOptions": {"values": false, "calcs": ["lastNotNull"], "fields": ""}, "pieType": "pie", "tooltip": {"mode": "single", "sort": "none"}, "legend": {"displayMode": "list", "placement": "bottom"}}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0}}, {"id": 3, "title": "Database Connections", "type": "timeseries", "targets": [{"expr": "pg_stat_database_numbackends{datname=\"loni\"}", "refId": "A", "legendFormat": "Active Connections"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "vis": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}}, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltip": {"mode": "single", "sort": "none"}}, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 8}}, {"id": 4, "title": "Vector Database Operations", "type": "timeseries", "targets": [{"expr": "rate(qdrant_collections_operations_total[5m])", "refId": "A", "legendFormat": "Operations/sec"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "vis": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "ops"}}, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltip": {"mode": "single", "sort": "none"}}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 16}}, {"id": 5, "title": "Token Usage", "type": "stat", "targets": [{"expr": "sum(ai_tokens_used_total)", "refId": "A"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 1000000}, {"color": "red", "value": 10000000}]}, "unit": "short"}}, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "textMode": "auto"}, "pluginVersion": "9.0.0", "gridPos": {"h": 8, "w": 12, "x": 12, "y": 16}}], "time": {"from": "now-1h", "to": "now"}, "timepicker": {}, "uid": "loni-overview", "version": 1, "weekStart": ""}}