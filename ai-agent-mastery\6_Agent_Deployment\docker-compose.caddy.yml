services:
  # Reverse Proxy Service (for cloud deployment)
  caddy:
    image: caddy:2-alpine
    container_name: dynamous-caddy
    restart: always
    ports:
      - 80:80/tcp
      - 443:443/tcp
    expose:
      - 2019/tcp
      - 443/tcp
      - 443/udp
      - 80/tcp
    environment:
      - AGENT_API_HOSTNAME=${AGENT_API_HOSTNAME-":8001"}
      - FRONTEND_HOSTNAME=${FRONTEND_HOSTNAME-":8082"}
      - LETSENCRYPT_EMAIL=${LETSENCRYPT_EMAIL:-internal}      
    volumes:
      - ./Caddyfile:/etc/caddy/Caddyfile:ro
      - caddy_data:/data:rw
      - caddy_config:/config:rw
    depends_on:
      - agent-api
      - frontend
    cap_drop:
      - ALL
    cap_add:
      - NET_BIND_SERVICE
    logging:
      driver: "json-file"
      options:
        max-size: "1m"
        max-file: "1"

volumes:
  caddy_data:
  caddy_config: