#!/bin/bash
# Quick Docker Build Target Validation Script
# Validates that Dockerfile stages match docker-compose.yml targets

set -euo pipefail

# Colors
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log() { echo -e "${BLUE}[INFO]${NC} $1"; }
success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
error() { echo -e "${RED}[ERROR]${NC} $1"; }
warn() { echo -e "${YELLOW}[WARNING]${NC} $1"; }

# Get script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
INFRA_DIR="$(cd "$SCRIPT_DIR/.." && pwd)"

log "Quick validation of Docker build targets..."
cd "$INFRA_DIR"

# Test 1: Check Docker Compose config
log "Testing Docker Compose configuration..."
if docker-compose config --quiet 2>/dev/null; then
    success "Docker Compose configuration is valid"
else
    error "Docker Compose configuration has errors"
    exit 1
fi

# Test 2: Extract target specifications from docker-compose.yml
log "Checking build targets in docker-compose.yml..."
frontend_target=$(grep -A 10 "frontend:" docker-compose.yml | grep "target:" | awk '{print $2}' || echo "none")
backend_target=$(grep -A 10 "backend:" docker-compose.yml | grep "target:" | awk '{print $2}' || echo "none")

log "Frontend target: $frontend_target"
log "Backend target: $backend_target"

# Test 3: Check Dockerfile stages
log "Checking Dockerfile stages..."

# Frontend stages
frontend_stages=$(grep "^FROM .* AS " ../frontend/Dockerfile | awk '{print $4}' | tr '\n' ' ')
log "Frontend stages: $frontend_stages"

# Backend stages  
backend_stages=$(grep "^FROM .* AS " ../backend/Dockerfile | awk '{print $4}' | tr '\n' ' ')
log "Backend stages: $backend_stages"

# Test 4: Validate target matches
log "Validating target matches..."

# Check frontend
if [[ "$frontend_target" != "none" ]]; then
    if echo "$frontend_stages" | grep -q "$frontend_target"; then
        success "Frontend target '$frontend_target' exists in Dockerfile"
    else
        error "Frontend target '$frontend_target' NOT found in Dockerfile stages: $frontend_stages"
        exit 1
    fi
else
    warn "No frontend target specified in docker-compose.yml"
fi

# Check backend
if [[ "$backend_target" != "none" ]]; then
    if echo "$backend_stages" | grep -q "$backend_target"; then
        success "Backend target '$backend_target' exists in Dockerfile"
    else
        error "Backend target '$backend_target' NOT found in Dockerfile stages: $backend_stages"
        exit 1
    fi
else
    warn "No backend target specified in docker-compose.yml"
fi

# Test 5: Check build contexts
log "Checking build contexts..."

if [[ -f "../frontend/Dockerfile" ]]; then
    success "Frontend Dockerfile exists"
else
    error "Frontend Dockerfile not found"
    exit 1
fi

if [[ -f "../backend/Dockerfile" ]]; then
    success "Backend Dockerfile exists"
else
    error "Backend Dockerfile not found"
    exit 1
fi

# Test 6: Check package manager configurations
log "Checking package manager configurations..."

# Frontend - check for Bun usage
if grep -q "oven/bun" ../frontend/Dockerfile; then
    success "Frontend uses Bun package manager"
else
    warn "Frontend may not be using Bun package manager"
fi

# Backend - check for UV usage
if grep -q "uv" ../backend/Dockerfile; then
    success "Backend uses UV package manager"
else
    warn "Backend may not be using UV package manager"
fi

# Test 7: Check Next.js standalone configuration
log "Checking Next.js configuration..."
if [[ -f "../frontend/next.config.js" ]]; then
    if grep -q "output.*standalone" ../frontend/next.config.js; then
        success "Next.js configured for standalone output"
    else
        warn "Next.js may not be configured for standalone output"
    fi
else
    warn "Next.js config file not found"
fi

success "All validations passed! Docker build targets are correctly configured."
log "You can now run:"
log "  docker-compose build --no-cache frontend backend"
log "  docker-compose up -d --build"
