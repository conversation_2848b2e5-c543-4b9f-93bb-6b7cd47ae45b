"""
Project directory structure generator.

Creates project directory layouts and copies template files
based on selected templates and their requirements.
"""

import shutil
import json
from typing import Dict, Set, Any, List, Optional
from pathlib import Path
from dataclasses import dataclass

from ..core.interfaces import IProjectStructureGenerator, ITemplateRegistry
from ..core.models import ProjectConfig, GenerationResult, TemplateCategory


@dataclass
class DirectoryTemplate:
    """Template for directory structure creation."""
    path: str
    description: str
    files: List[str]
    is_optional: bool = False


class ProjectStructureGenerator(IProjectStructureGenerator):
    """
    Generator for project directory structures and template files.
    
    Creates appropriate directory layouts based on selected templates
    and copies necessary template files.
    """
    
    def __init__(self, template_registry: ITemplateRegistry):
        """Initialize with template registry."""
        self.template_registry = template_registry
        
        # Define standard directory structures
        self.directory_templates = self._define_directory_templates()
    
    def generate(self, project_path: Path, config: ProjectConfig) -> GenerationResult:
        """Generate project directory structure."""
        result = GenerationResult(
            success=True,
            project_path=str(project_path)
        )
        
        try:
            # Create base project directory
            project_path.mkdir(parents=True, exist_ok=config.overwrite_existing)
            
            # Create directory structure based on templates
            created_dirs = self.create_directory_structure(project_path, config.selected_templates)
            
            # Copy template files
            copied_files = []
            for template_id in config.selected_templates:
                template_files = self.copy_template_files(project_path, template_id)
                copied_files.extend(template_files)
            
            # Generate common project files
            self._generate_common_files(project_path, config, result)
            
            # Generate README if requested
            if config.generate_readme:
                self._generate_readme(project_path, config, result)
            
            # Generate .gitignore if requested
            if config.generate_gitignore:
                self._generate_gitignore(project_path, config, result)
            
            # Add created directories to result
            for dir_path in created_dirs:
                result.add_generated_file(
                    path=str(dir_path.relative_to(project_path)),
                    generated_by="ProjectStructureGenerator"
                )
            
            # Add next steps
            result.next_steps.extend([
                f"Navigate to your project: cd {config.project_name}",
                "Review the generated directory structure",
                "Start development with your chosen frameworks"
            ])
            
        except Exception as e:
            result.success = False
            result.errors.append(f"Project structure generation failed: {str(e)}")
        
        return result
    
    def can_generate(self, templates: Set[str]) -> bool:
        """Always can generate project structure."""
        return True
    
    def create_directory_structure(self, project_path: Path, templates: Set[str]) -> List[Path]:
        """Create directory structure based on selected templates."""
        created_dirs = []
        template_categories = self._get_template_categories(templates)
        
        # Create directories based on template categories
        for category in template_categories:
            dirs_for_category = self._get_directories_for_category(category)
            for dir_template in dirs_for_category:
                dir_path = project_path / dir_template.path
                if not dir_path.exists():
                    dir_path.mkdir(parents=True, exist_ok=True)
                    created_dirs.append(dir_path)
                    
                    # Create placeholder files if specified
                    for file_name in dir_template.files:
                        file_path = dir_path / file_name
                        if not file_path.exists():
                            self._create_placeholder_file(file_path, dir_template.description)
        
        # Create template-specific directories
        for template_id in templates:
            template_dirs = self._get_template_specific_directories(template_id)
            for dir_path in template_dirs:
                full_path = project_path / dir_path
                if not full_path.exists():
                    full_path.mkdir(parents=True, exist_ok=True)
                    created_dirs.append(full_path)
        
        return created_dirs
    
    def copy_template_files(self, project_path: Path, template_id: str) -> List[Path]:
        """Copy template-specific files to project directory."""
        copied_files = []
        
        # Get template files from embedded templates
        template_files = self._get_template_files(template_id)
        
        for file_info in template_files:
            source_content = file_info['content']
            target_path = project_path / file_info['path']
            
            # Create parent directories if needed
            target_path.parent.mkdir(parents=True, exist_ok=True)
            
            # Write file content
            with open(target_path, 'w') as f:
                f.write(source_content)
            
            copied_files.append(target_path)
        
        return copied_files
    
    def _define_directory_templates(self) -> Dict[TemplateCategory, List[DirectoryTemplate]]:
        """Define standard directory structures for each template category."""
        return {
            TemplateCategory.FRONTEND: [
                DirectoryTemplate("src", "Source code directory", ["index.ts"]),
                DirectoryTemplate("src/components", "React components", ["index.ts"]),
                DirectoryTemplate("src/pages", "Page components", ["index.ts"]),
                DirectoryTemplate("src/hooks", "Custom React hooks", ["index.ts"]),
                DirectoryTemplate("src/utils", "Utility functions", ["index.ts"]),
                DirectoryTemplate("src/types", "TypeScript type definitions", ["index.ts"]),
                DirectoryTemplate("public", "Static assets", []),
                DirectoryTemplate("tests", "Test files", ["example.test.ts"]),
            ],
            TemplateCategory.BACKEND: [
                DirectoryTemplate("src", "Source code directory", ["__init__.py"]),
                DirectoryTemplate("src/api", "API routes", ["__init__.py"]),
                DirectoryTemplate("src/models", "Data models", ["__init__.py"]),
                DirectoryTemplate("src/services", "Business logic", ["__init__.py"]),
                DirectoryTemplate("src/utils", "Utility functions", ["__init__.py"]),
                DirectoryTemplate("tests", "Test files", ["__init__.py"]),
                DirectoryTemplate("docs", "API documentation", ["README.md"]),
            ],
            TemplateCategory.DATABASE: [
                DirectoryTemplate("migrations", "Database migrations", []),
                DirectoryTemplate("seeds", "Database seed files", []),
                DirectoryTemplate("sql", "SQL scripts", []),
            ],
            TemplateCategory.INFRASTRUCTURE: [
                DirectoryTemplate("docker", "Docker configurations", []),
                DirectoryTemplate("scripts", "Utility scripts", []),
                DirectoryTemplate("docs", "Documentation", ["README.md"]),
            ],
            TemplateCategory.AI_ML: [
                DirectoryTemplate("models", "ML model files", []),
                DirectoryTemplate("data", "Training and test data", []),
                DirectoryTemplate("notebooks", "Jupyter notebooks", []),
                DirectoryTemplate("experiments", "ML experiments", []),
            ]
        }
    
    def _get_template_categories(self, templates: Set[str]) -> Set[TemplateCategory]:
        """Get categories of selected templates."""
        categories = set()
        
        for template_id in templates:
            template = self.template_registry.get_template(template_id)
            if template:
                metadata = template.get_metadata()
                categories.add(metadata.category)
        
        return categories
    
    def _get_directories_for_category(self, category: TemplateCategory) -> List[DirectoryTemplate]:
        """Get directory templates for a specific category."""
        return self.directory_templates.get(category, [])
    
    def _get_template_specific_directories(self, template_id: str) -> List[str]:
        """Get directories specific to a template."""
        template = self.template_registry.get_template(template_id)
        if not template:
            return []
        
        metadata = template.get_metadata()
        directories = []
        
        # Extract directory paths from generated files
        for file_path in metadata.generated_files:
            if '/' in file_path:
                dir_path = '/'.join(file_path.split('/')[:-1])
                if dir_path not in directories:
                    directories.append(dir_path)
        
        return directories
    
    def _create_placeholder_file(self, file_path: Path, description: str):
        """Create a placeholder file with basic content."""
        if file_path.suffix == '.ts':
            content = f"// {description}\nexport {{};};\n"
        elif file_path.suffix == '.py':
            content = f'"""{description}"""\n'
        elif file_path.suffix == '.md':
            content = f"# {description}\n\nThis file was auto-generated.\n"
        else:
            content = f"// {description}\n"
        
        with open(file_path, 'w') as f:
            f.write(content)
    
    def _get_template_files(self, template_id: str) -> List[Dict[str, str]]:
        """Get embedded template files for a specific template."""
        template_files = {
            'react': [
                {
                    'path': 'package.json',
                    'content': self._generate_react_package_json()
                },
                {
                    'path': 'tsconfig.json',
                    'content': self._generate_react_tsconfig()
                },
                {
                    'path': 'src/App.tsx',
                    'content': self._generate_react_app_component()
                }
            ],
            'nextjs': [
                {
                    'path': 'next.config.js',
                    'content': self._generate_nextjs_config()
                },
                {
                    'path': 'pages/_app.tsx',
                    'content': self._generate_nextjs_app()
                }
            ],
            'tailwindcss': [
                {
                    'path': 'tailwind.config.ts',
                    'content': self._generate_tailwind_config()
                },
                {
                    'path': 'postcss.config.js',
                    'content': self._generate_postcss_config()
                }
            ],
            'caddy': [
                {
                    'path': 'Caddyfile',
                    'content': self._generate_caddyfile()
                }
            ]
        }
        
        return template_files.get(template_id, [])
    
    def _generate_common_files(self, project_path: Path, config: ProjectConfig, result: GenerationResult):
        """Generate common project files."""
        # Generate package.json for Node.js projects
        if self._has_nodejs_templates(config.selected_templates):
            package_json = self._generate_package_json(config)
            package_file = project_path / "package.json"
            with open(package_file, 'w') as f:
                f.write(package_json)
            
            result.add_generated_file(
                path="package.json",
                generated_by="ProjectStructureGenerator"
            )
    
    def _generate_readme(self, project_path: Path, config: ProjectConfig, result: GenerationResult):
        """Generate README.md file."""
        readme_content = f"""# {config.project_name}

This project was generated using the Project Template Orchestrator.

## Technologies Used

{self._get_technologies_list(config.selected_templates)}

## Getting Started

1. Install dependencies:
   ```bash
   # If using npm
   npm install
   
   # If using pnpm
   pnpm install
   
   # If using bun
   bun install
   ```

2. Configure environment variables:
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

3. Start services:
   ```bash
   docker-compose up -d
   ```

4. Start development:
   ```bash
   npm run dev
   ```

## Project Structure

```
{self._generate_project_structure_tree(config.selected_templates)}
```

## Environment Variables

See `.env.example` for required environment variables.

## Docker Services

This project includes the following Docker services:
{self._get_docker_services_list(config.selected_templates)}

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Submit a pull request

## License

This project is licensed under the MIT License.
"""
        
        readme_file = project_path / "README.md"
        with open(readme_file, 'w') as f:
            f.write(readme_content)
        
        result.add_generated_file(
            path="README.md",
            generated_by="ProjectStructureGenerator"
        )
    
    def _generate_gitignore(self, project_path: Path, config: ProjectConfig, result: GenerationResult):
        """Generate .gitignore file."""
        gitignore_entries = [
            "# Dependencies",
            "node_modules/",
            "__pycache__/",
            "*.pyc",
            "",
            "# Environment files",
            ".env",
            ".env.local",
            ".env.*.local",
            "",
            "# Build outputs",
            "dist/",
            "build/",
            ".next/",
            "*.tsbuildinfo",
            "",
            "# IDE files",
            ".vscode/",
            ".idea/",
            "*.swp",
            "*.swo",
            "",
            "# OS files",
            ".DS_Store",
            "Thumbs.db",
            "",
            "# Docker",
            "docker-compose.override.yml",
            "",
            "# Logs",
            "logs/",
            "*.log",
            "npm-debug.log*",
            "yarn-debug.log*",
            "yarn-error.log*"
        ]
        
        gitignore_file = project_path / ".gitignore"
        with open(gitignore_file, 'w') as f:
            f.write("\n".join(gitignore_entries))
        
        result.add_generated_file(
            path=".gitignore",
            generated_by="ProjectStructureGenerator"
        )
    
    def _has_nodejs_templates(self, templates: Set[str]) -> bool:
        """Check if project includes Node.js templates."""
        nodejs_templates = {'react', 'nextjs', 'nodejs', 'npm', 'pnpm', 'bun'}
        return bool(nodejs_templates.intersection(templates))
    
    def _generate_package_json(self, config: ProjectConfig) -> str:
        """Generate package.json content."""
        package_data = {
            "name": config.project_name,
            "version": "1.0.0",
            "description": f"Generated project using template orchestrator",
            "main": "index.js",
            "scripts": {
                "dev": "next dev" if 'nextjs' in config.selected_templates else "vite",
                "build": "next build" if 'nextjs' in config.selected_templates else "vite build",
                "start": "next start" if 'nextjs' in config.selected_templates else "vite preview",
                "lint": "eslint . --ext .ts,.tsx",
                "type-check": "tsc --noEmit"
            },
            "dependencies": {},
            "devDependencies": {},
            "keywords": [],
            "author": "",
            "license": "MIT"
        }
        
        return json.dumps(package_data, indent=2)
    
    def _get_technologies_list(self, templates: Set[str]) -> str:
        """Generate markdown list of technologies."""
        tech_descriptions = {
            'react': '- React 18 - Modern frontend framework',
            'nextjs': '- Next.js - React framework with SSR',
            'tailwindcss': '- Tailwind CSS - Utility-first CSS framework',
            'shadcn': '- Shadcn UI - Modern component library',
            'postgresql': '- PostgreSQL - Relational database',
            'supabase': '- Supabase - Backend-as-a-service',
            'qdrant': '- Qdrant - Vector database for AI',
            'ollama': '- Ollama - Local LLM serving',
            'docker': '- Docker - Containerization platform',
            'caddy': '- Caddy - Modern web server with automatic HTTPS'
        }
        
        lines = []
        for template_id in sorted(templates):
            if template_id in tech_descriptions:
                lines.append(tech_descriptions[template_id])
        
        return '\n'.join(lines) if lines else '- Custom technology stack'
    
    def _generate_project_structure_tree(self, templates: Set[str]) -> str:
        """Generate project structure tree."""
        structure_lines = [
            f"├── docker-compose.yml",
            f"├── .env.example",
            f"├── .gitignore",
            f"├── README.md"
        ]
        
        if self._has_nodejs_templates(templates):
            structure_lines.append("├── package.json")
        
        if 'react' in templates or 'nextjs' in templates:
            structure_lines.extend([
                "├── src/",
                "│   ├── components/",
                "│   ├── pages/",
                "│   └── utils/"
            ])
        
        return '\n'.join(structure_lines)
    
    def _get_docker_services_list(self, templates: Set[str]) -> str:
        """Generate list of Docker services."""
        services = []
        
        service_descriptions = {
            'postgresql': '- PostgreSQL database (port 5432)',
            'supabase': '- Supabase backend (port 8000)',
            'qdrant': '- Qdrant vector database (port 6333)',
            'neo4j': '- Neo4j graph database (ports 7474, 7687)',
            'ollama': '- Ollama LLM server (port 11434)',
            'n8n': '- n8n workflow automation (port 5678)',
            'caddy': '- Caddy reverse proxy (ports 80, 443)'
        }
        
        for template_id in sorted(templates):
            if template_id in service_descriptions:
                services.append(service_descriptions[template_id])
        
        return '\n'.join(services) if services else '- No Docker services configured'
    
    # Template file generators
    def _generate_react_package_json(self) -> str:
        return """{
  "name": "react-app",
  "version": "0.1.0",
  "private": true,
  "dependencies": {
    "react": "^18.2.0",
    "react-dom": "^18.2.0"
  },
  "devDependencies": {
    "@types/react": "^18.2.0",
    "@types/react-dom": "^18.2.0",
    "typescript": "^5.0.0",
    "vite": "^4.0.0"
  }
}"""
    
    def _generate_react_tsconfig(self) -> str:
        return """{
  "compilerOptions": {
    "target": "ES2020",
    "useDefineForClassFields": true,
    "lib": ["ES2020", "DOM", "DOM.Iterable"],
    "module": "ESNext",
    "skipLibCheck": true,
    "moduleResolution": "bundler",
    "allowImportingTsExtensions": true,
    "resolveJsonModule": true,
    "isolatedModules": true,
    "noEmit": true,
    "jsx": "react-jsx",
    "strict": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "noFallthroughCasesInSwitch": true
  },
  "include": ["src"],
  "references": [{ "path": "./tsconfig.node.json" }]
}"""
    
    def _generate_react_app_component(self) -> str:
        return """import React from 'react';

function App() {
  return (
    <div className="App">
      <header className="App-header">
        <h1>Welcome to Your New Project</h1>
        <p>Generated by Project Template Orchestrator</p>
      </header>
    </div>
  );
}

export default App;
"""
    
    def _generate_nextjs_config(self) -> str:
        return """/** @type {import('next').NextConfig} */
const nextConfig = {
  experimental: {
    appDir: true,
  },
  images: {
    domains: ['localhost'],
  },
}

module.exports = nextConfig
"""
    
    def _generate_nextjs_app(self) -> str:
        return """import type { AppProps } from 'next/app'

export default function App({ Component, pageProps }: AppProps) {
  return <Component {...pageProps} />
}
"""
    
    def _generate_tailwind_config(self) -> str:
        return """import type { Config } from 'tailwindcss'

const config: Config = {
  content: [
    './pages/**/*.{js,ts,jsx,tsx,mdx}',
    './components/**/*.{js,ts,jsx,tsx,mdx}',
    './app/**/*.{js,ts,jsx,tsx,mdx}',
    './src/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  theme: {
    extend: {},
  },
  plugins: [],
}
export default config
"""
    
    def _generate_postcss_config(self) -> str:
        return """module.exports = {
  plugins: {
    tailwindcss: {},
    autoprefixer: {},
  },
}
"""
    
    def _generate_caddyfile(self) -> str:
        return """# Caddyfile for reverse proxy
localhost {
    reverse_proxy app:3000
}

# API routes
localhost/api/* {
    reverse_proxy api:8000
}

# Database admin (optional)
localhost:8080 {
    reverse_proxy postgres-admin:80
}
"""