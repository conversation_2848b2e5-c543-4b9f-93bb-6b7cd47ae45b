# LONI Docker Infrastructure - Quick Start Guide

## 🚀 **Prerequisites**

1. **Enable Docker Desktop WSL2 Integration**
   ```bash
   # Check if Docker is available
   docker --version
   
   # If not available:
   # 1. Open Docker Desktop
   # 2. Settings > Resources > WSL Integration
   # 3. Enable integration with your WSL distro
   # 4. Restart WSL terminal
   ```

2. **Environment Setup**
   ```bash
   cd /mnt/e/Projects/lonors/loni/apps/infra
   
   # Environment file is already created with secure defaults
   # Edit .env if you need to customize settings
   ```

## ⚡ **Quick Start Commands**

### **Start All Services**
```bash
# Automated startup with health checks
bash scripts/start-services.sh

# Manual startup (alternative)
docker-compose -f docker-compose.fixed.yml up -d
```

### **Check Status**
```bash
# Service status
bash scripts/start-services.sh status

# Health check
docker-compose -f docker-compose.fixed.yml ps
```

### **View Logs**
```bash
# All services
bash scripts/start-services.sh logs

# Specific service
bash scripts/start-services.sh logs nginx
bash scripts/start-services.sh logs prometheus
```

### **Stop Services**
```bash
# Graceful shutdown
bash scripts/start-services.sh stop

# Force stop and remove
docker-compose -f docker-compose.fixed.yml down -v
```

## 🌐 **Access URLs**

### **Direct Service Access**
| Service | URL | Credentials |
|---------|-----|-------------|
| Grafana | http://localhost:3001 | admin / [check .env] |
| Prometheus | http://localhost:9090 | No auth |
| Jaeger | http://localhost:16686 | No auth |
| n8n | http://localhost:5678 | admin / [check .env] |
| Qdrant | http://localhost:6333 | No auth |

### **Reverse Proxy Access (when nginx running)**
| Service | URL | Credentials |
|---------|-----|-------------|
| Monitoring | http://localhost/monitoring | admin / loni_monitoring_admin |
| Metrics | http://localhost/prometheus | admin / loni_monitoring_admin |
| Automation | http://localhost/automation | admin / loni_monitoring_admin |
| Tracing | http://localhost/tracing | admin / loni_monitoring_admin |

## 🔧 **Troubleshooting**

### **Common Issues**

#### **Docker Not Available**
```bash
# Error: command 'docker' could not be found
# Solution: Enable Docker Desktop WSL2 integration

# Check Docker status
docker info
```

#### **Services Not Starting**
```bash
# Check individual service logs
docker-compose -f docker-compose.fixed.yml logs [service-name]

# Common services to check:
docker-compose -f docker-compose.fixed.yml logs postgres
docker-compose -f docker-compose.fixed.yml logs redis
docker-compose -f docker-compose.fixed.yml logs nginx
```

#### **Permission Errors**
```bash
# Fix script permissions
chmod +x scripts/start-services.sh

# Fix config file permissions
sudo chown -R $USER:$USER config/
```

#### **Port Conflicts**
```bash
# Check what's using the ports
netstat -tulpn | grep :3001  # Grafana
netstat -tulpn | grep :9090  # Prometheus
netstat -tulpn | grep :5432  # PostgreSQL

# Edit .env to change ports if needed
```

### **Service-Specific Issues**

#### **Ollama (AI Models)**
```bash
# Start CPU version if GPU fails
docker-compose -f docker-compose.fixed.yml --profile cpu up -d ollama-cpu

# Check Ollama status
curl http://localhost:11434/api/tags
```

#### **PostgreSQL Connection Issues**
```bash
# Test database connection
docker exec -it loni-postgres psql -U loni -d loni -c "SELECT version();"

# Check n8n database
docker exec -it loni-postgres psql -U loni -d n8n -c "\dt"
```

#### **Nginx Configuration Issues**
```bash
# Test nginx configuration
docker exec -it loni-nginx nginx -t

# Reload nginx
docker exec -it loni-nginx nginx -s reload
```

## 📊 **Monitoring & Health Checks**

### **Service Health**
```bash
# Check all service health
docker-compose -f docker-compose.fixed.yml ps

# Services should show "healthy" status:
# - postgres
# - redis
# - qdrant
# - prometheus
# - grafana
# - nginx
```

### **Grafana Dashboards**
1. Open http://localhost:3001
2. Login with admin/[password from .env]
3. Navigate to Dashboards > LONI Platform Overview

### **Prometheus Metrics**
1. Open http://localhost:9090
2. Check Targets: Status > Targets
3. All targets should be "UP"

## 🔐 **Security Notes**

### **Default Credentials**
- **Grafana**: admin / [generated password in .env]
- **n8n**: admin / [generated password in .env]
- **Nginx Auth**: admin / loni_monitoring_admin
- **PostgreSQL**: loni / [generated password in .env]
- **Redis**: [generated password in .env]

### **Change Default Passwords**
```bash
# Edit .env file to change passwords
nano .env

# Restart services after changing passwords
bash scripts/start-services.sh restart
```

## 🚀 **Next Steps**

### **Start Application Services**
```bash
# Terminal 1: Start Backend API
cd ../backend
uv venv && source .venv/bin/activate
uv pip install -r requirements.txt
uvicorn main:app --reload --port 8000

# Terminal 2: Start Frontend
cd ../frontend
bun install
bun dev

# Access application at http://localhost:3000
```

### **Load AI Models**
```bash
# Connect to Ollama and pull models
docker exec -it loni-ollama ollama pull llama2
docker exec -it loni-ollama ollama pull codellama
docker exec -it loni-ollama ollama list
```

### **Configure Workflows**
1. Access n8n at http://localhost:5678
2. Login with credentials from .env
3. Create AI-powered workflows

## 📞 **Support**

- **Infrastructure Issues**: Check logs and this troubleshooting guide
- **Application Issues**: Refer to backend/frontend documentation
- **Docker Issues**: Ensure Docker Desktop WSL2 integration is enabled
- **Performance Issues**: Monitor resource usage in Grafana dashboards