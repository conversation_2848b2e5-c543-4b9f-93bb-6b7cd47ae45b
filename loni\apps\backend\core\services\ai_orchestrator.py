"""
AI Orchestrator Service.

This service orchestrates all AI-related services following the
Single Responsibility Principle and Dependency Inversion Principle.
"""

from typing import Dict, List, Any, Optional, AsyncGenerator
from uuid import UUID

from loguru import logger

from .ai.model_service import ModelService
from .ai.chat_service import ChatService
from .ai.streaming_service import StreamingService
from .ai.rag_service import RAGService
from .ai.analysis_service import AnalysisService
from ..repositories.conversation import ConversationRepository, MessageRepository


class AIOrchestrator:
    """
    AI Orchestrator service.
    
    Coordinates AI services and provides a unified interface
    for AI operations while maintaining separation of concerns.
    """
    
    def __init__(
        self,
        conversation_repository: ConversationRepository,
        message_repository: MessageRepository
    ):
        """
        Initialize the AI orchestrator.
        
        Args:
            conversation_repository: Repository for conversation operations
            message_repository: Repository for message operations
        """
        # Initialize specialized services
        self.model_service = ModelService()
        self.rag_service = RAGService()
        self.chat_service = ChatService(self.model_service, self.rag_service)
        self.streaming_service = StreamingService(self.model_service, self.rag_service)
        self.analysis_service = AnalysisService(conversation_repository, message_repository)
        
        # Repository references
        self.conversation_repo = conversation_repository
        self.message_repo = message_repository
    
    async def get_available_models(self) -> List[Dict[str, Any]]:
        """
        Get list of available AI models.
        
        Returns:
            List of model configurations
        """
        return await self.model_service.get_available_models()
    
    async def chat_completion(
        self,
        user_id: UUID,
        conversation_id: UUID,
        message: str,
        model_id: str = "gpt-4",
        rag_enabled: bool = True,
        temperature: float = 0.7
    ) -> Dict[str, Any]:
        """
        Generate AI response for a chat message.
        
        Args:
            user_id: ID of the user
            conversation_id: ID of the conversation
            message: User message content
            model_id: ID of the model to use
            rag_enabled: Whether to use RAG context
            temperature: Response randomness
            
        Returns:
            Dictionary containing response and metadata
        """
        try:
            # Get conversation context
            context_messages = await self._get_conversation_context(
                conversation_id, message_count=10
            )
            
            # Add current message to context
            messages = context_messages + [{'role': 'user', 'content': message}]
            
            # Estimate tokens
            token_estimate = self.chat_service.estimate_tokens(messages)
            
            # Generate response
            response = await self.chat_service.generate_response(
                messages=messages,
                model_id=model_id,
                rag_enabled=rag_enabled,
                conversation_id=conversation_id,
                temperature=temperature
            )
            
            # Save message pair
            user_msg, assistant_msg = await self.message_repo.create_message_pair(
                conversation_id=conversation_id,
                user_content=message,
                assistant_content=response,
                model_name=model_id,
                tokens_used=token_estimate['input_tokens'] + len(response.split())
            )
            
            return {
                'response': response,
                'user_message_id': str(user_msg.id),
                'assistant_message_id': str(assistant_msg.id),
                'model_used': model_id,
                'tokens_used': token_estimate['input_tokens'] + len(response.split()),
                'rag_enabled': rag_enabled
            }
            
        except Exception as e:
            logger.error(f"Chat completion failed: {e}")
            raise
    
    async def stream_chat_completion(
        self,
        user_id: UUID,
        conversation_id: UUID,
        message: str,
        model_id: str = "gpt-4",
        rag_enabled: bool = True,
        temperature: float = 0.7
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """
        Stream AI response for a chat message.
        
        Args:
            user_id: ID of the user
            conversation_id: ID of the conversation
            message: User message content
            model_id: ID of the model to use
            rag_enabled: Whether to use RAG context
            temperature: Response randomness
            
        Yields:
            Stream chunks with response data
        """
        try:
            # Validate streaming support
            if not self.streaming_service.validate_streaming_model(model_id):
                raise ValueError(f"Model {model_id} does not support streaming")
            
            # Get conversation context
            context_messages = await self._get_conversation_context(
                conversation_id, message_count=10
            )
            
            # Add current message to context
            messages = context_messages + [{'role': 'user', 'content': message}]
            
            # Stream response
            full_response = ""
            user_message_id = None
            assistant_message_id = None
            
            async for chunk in self.streaming_service.stream_response(
                messages=messages,
                model_id=model_id,
                rag_enabled=rag_enabled,
                conversation_id=conversation_id,
                temperature=temperature
            ):
                # Save messages on first chunk
                if chunk.get('chunk_id') == 1 and chunk.get('type') == 'content':
                    # Create user message
                    user_msg = await self.message_repo.create(
                        conversation_id=conversation_id,
                        role='user',
                        content=message
                    )
                    user_message_id = str(user_msg.id)
                
                # Accumulate response for final save
                if chunk.get('type') == 'content':
                    full_response += chunk.get('content', '')
                
                # Add message IDs to chunk
                chunk['user_message_id'] = user_message_id
                chunk['assistant_message_id'] = assistant_message_id
                
                yield chunk
                
                # Save assistant message when complete
                if chunk.get('finished') and chunk.get('type') == 'completion':
                    assistant_msg = await self.message_repo.create(
                        conversation_id=conversation_id,
                        role='assistant',
                        content=full_response,
                        model_name=model_id,
                        tokens_used=len(full_response.split()) + len(message.split())
                    )
                    assistant_message_id = str(assistant_msg.id)
                    
                    # Send final chunk with message ID
                    yield {
                        **chunk,
                        'assistant_message_id': assistant_message_id,
                        'full_response': full_response
                    }
            
        except Exception as e:
            logger.error(f"Stream chat completion failed: {e}")
            yield {
                'type': 'error',
                'error': str(e),
                'finished': True
            }
    
    async def generate_conversation_title(self, conversation_id: UUID) -> str:
        """
        Generate a title for a conversation.
        
        Args:
            conversation_id: ID of the conversation
            
        Returns:
            Generated title
        """
        try:
            messages = await self._get_conversation_context(conversation_id, message_count=5)
            title = await self.chat_service.generate_title(messages)
            
            # Update conversation with new title
            await self.conversation_repo.update_title(conversation_id, title)
            
            return title
            
        except Exception as e:
            logger.error(f"Title generation failed: {e}")
            return "New Conversation"
    
    async def analyze_conversation(self, conversation_id: UUID) -> Dict[str, Any]:
        """
        Analyze a conversation for insights.
        
        Args:
            conversation_id: ID of the conversation to analyze
            
        Returns:
            Analysis results
        """
        return await self.analysis_service.analyze_conversation(conversation_id)
    
    async def search_documents(
        self,
        query: str,
        limit: int = 5,
        score_threshold: float = 0.7
    ) -> List[Dict[str, Any]]:
        """
        Search documents using RAG.
        
        Args:
            query: Search query
            limit: Maximum number of results
            score_threshold: Minimum similarity score
            
        Returns:
            Search results
        """
        return await self.rag_service.search_documents(
            query=query,
            limit=limit,
            score_threshold=score_threshold
        )
    
    async def add_document_to_rag(
        self,
        content: str,
        title: str,
        metadata: Optional[Dict[str, Any]] = None
    ) -> str:
        """
        Add a document to the RAG system.
        
        Args:
            content: Document content
            title: Document title
            metadata: Optional metadata
            
        Returns:
            Document ID
        """
        return await self.rag_service.add_document(
            content=content,
            title=title,
            metadata=metadata
        )
    
    async def get_model_info(self, model_id: str) -> Dict[str, Any] | None:
        """
        Get information about a specific model.
        
        Args:
            model_id: ID of the model
            
        Returns:
            Model information or None if not found
        """
        return await self.model_service.get_model_by_id(model_id)
    
    async def estimate_cost(
        self,
        model_id: str,
        input_text: str,
        estimated_output_length: int = 100
    ) -> float:
        """
        Estimate the cost for a request.
        
        Args:
            model_id: ID of the model
            input_text: Input text
            estimated_output_length: Estimated output length in words
            
        Returns:
            Estimated cost in USD
        """
        input_tokens = len(input_text.split()) * 1.3  # Rough token estimation
        output_tokens = estimated_output_length * 1.3
        
        return self.model_service.estimate_cost(
            model_id=model_id,
            input_tokens=int(input_tokens),
            output_tokens=int(output_tokens)
        )
    
    async def _get_conversation_context(
        self,
        conversation_id: UUID,
        message_count: int = 10
    ) -> List[Dict[str, str]]:
        """
        Get conversation context as formatted messages.
        
        Args:
            conversation_id: ID of the conversation
            message_count: Number of recent messages to include
            
        Returns:
            List of formatted messages
        """
        messages = await self.message_repo.get_latest_messages(
            conversation_id=conversation_id,
            count=message_count
        )
        
        return [
            {
                'role': msg.role,
                'content': msg.content
            }
            for msg in messages
        ]