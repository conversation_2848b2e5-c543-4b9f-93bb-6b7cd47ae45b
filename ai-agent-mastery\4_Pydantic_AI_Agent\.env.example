# The provider for your LLM
# Set this to either openai, openrouter, or ollama
# This is needed on top of the base URL for Mem0 (long term memory)
LLM_PROVIDER=

# Base URL for the OpenAI compatible instance (default is https://api.openai.com/v1)
# OpenAI: https://api.openai.com/v1
# Ollama (example): http://localhost:11434/v1
# OpenRouter: https://openrouter.ai/api/v1
LLM_BASE_URL=

# OpenAI: https://help.openai.com/en/articles/4936850-where-do-i-find-my-openai-api-key
# Open Router: Get your API Key here after registering: https://openrouter.ai/keys
# Ollama: Just set this to a placeholder like ollama unless you specifically configured an API key
LLM_API_KEY=

# The LLM you want to use for the agents. Make sure this LLM supports tools (especially important if using Ollama)!
# OpenAI example: gpt-4o-mini
# OpenRouter example: anthropic/claude-3.7-sonnet
# Ollama example: qwen2.5:14b-instruct-8k
LLM_CHOICE=

# The LLM you want to use for image analysis.
# Make sure this LLM supports vision (especially important if using Ollama or OpenRouter)!
# OpenAI example: gpt-4o-mini
# OpenRouter example: anthropic/claude-3.7-sonnet
# Ollama example: llava:7b
VISION_LLM_CHOICE=

# The provider for your embedding model
# Set this to either openai, or ollama (openrouter doesn't have embedding models)
# This is needed on top of the base URL for Mem0 (long term memory)
EMBEDDING_PROVIDER=

# Base URL for the OpenAI compatible instance that has embedding models (default is https://api.openai.com/v1)
# OpenAI: https://api.openai.com/v1
# Ollama (example): http://localhost:11434/v1
EMBEDDING_BASE_URL=

# OpenAI: https://help.openai.com/en/articles/4936850-where-do-i-find-my-openai-api-key
# Ollama: Just set this to a placeholder like ollama unless you specifically configured an API key
EMBEDDING_API_KEY=

# The embedding model you want to use for RAG.
# Make sure the embeddings column in your database has the same dimensions as this embedding model!
# OpenAI example: text-embedding-3-small
# Ollama example: nomic-embed-text
EMBEDDING_MODEL_CHOICE=

# Postgres DB URL used for Mem0
# Format: postgresql://[user]:[password]@[host]:[port]/[database_name]
# Example: postgresql://postgres:mypassword@localhost:5432/mydb
# For Supabase Postgres connection, you can find this in "Connect" (top middle of Supabase dashboard) -> Transaction pooler
# For local Supabase, be sure to include the pooler tenant ID you set in your .env file for the local AI package, example:
# postgresql://postgres.your-pooler-tenant-id:mypassword@localhost:5432/mydb
DATABASE_URL=

# Supabase configuration
# Get these from your Supabase project settings -> API
# https://supabase.com/dashboard/project/<your project ID>/settings/api
# For the Local AI Package:
#   The Supabase URL is http://kong:8000 (when running in Docker) or http://localhost:8000 (agent is outside of Docker)
#   The Service Key you get from your package .env
SUPABASE_URL=
SUPABASE_SERVICE_KEY=

# Set your Brave API key if using Brave for agent web search (leave empty if using SearXNG)
# Get your key by going to the following link after signing up for Brave:
# https://api.search.brave.com/app/keys
BRAVE_API_KEY=

# Set the SearXNG endpoint if using SearXNG for agent web search (leave empty if using Brave)
# For the local AI package - this will be:
#    http://localhost:8081 if your agent is running outside of Docker
#    http://searxng:8080 if your agent is running in a container in the local-ai network
SEARXNG_BASE_URL=
