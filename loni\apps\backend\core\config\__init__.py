"""
Configuration module with domain-specific settings.

This module provides centralized configuration management
following the Single Responsibility Principle.
"""

from .application import AppSettings
from .database import DatabaseSettings
from .security import SecuritySettings
from .ai import AnthropicSettings, OllamaSettings, OpenAISettings
from .redis import RedisSettings
from .logging import LoggingSettings
from .monitoring import MonitoringSettings

# Main settings function
from .application import get_settings

__all__ = [
    "AppSettings",
    "DatabaseSettings", 
    "SecuritySettings",
    "AnthropicSettings",
    "OllamaSettings", 
    "OpenAISettings",
    "RedisSettings",
    "LoggingSettings",
    "MonitoringSettings",
    "get_settings",
]