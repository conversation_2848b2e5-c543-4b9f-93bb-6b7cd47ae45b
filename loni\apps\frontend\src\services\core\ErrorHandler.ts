/**
 * Error handling service for frontend applications.
 * 
 * This service provides centralized error handling and user notification
 * following the Single Responsibility Principle.
 */

export interface ErrorDetails {
  message: string;
  code?: string;
  details?: any;
  timestamp: string;
  correlationId?: string;
}

export interface ErrorNotification {
  id: string;
  type: 'error' | 'warning' | 'info';
  title: string;
  message: string;
  duration?: number;
  actions?: Array<{
    label: string;
    onClick: () => void;
  }>;
}

export class ErrorHandler {
  private errorListeners: Array<(notification: ErrorNotification) => void> = [];
  private errorHistory: ErrorDetails[] = [];

  /**
   * Register an error listener for notifications.
   */
  addErrorListener(listener: (notification: ErrorNotification) => void): void {
    this.errorListeners.push(listener);
  }

  /**
   * Remove an error listener.
   */
  removeErrorListener(listener: (notification: ErrorNotification) => void): void {
    this.errorListeners = this.errorListeners.filter(l => l !== listener);
  }

  /**
   * Handle an error and notify listeners.
   */
  handleError(error: Error | unknown, context?: string): void {
    const errorDetails = this.parseError(error, context);
    
    // Store in history
    this.errorHistory.push(errorDetails);
    if (this.errorHistory.length > 100) {
      this.errorHistory.shift(); // Keep only last 100 errors
    }

    // Create notification
    const notification = this.createNotification(errorDetails);
    
    // Notify listeners
    this.errorListeners.forEach(listener => {
      try {
        listener(notification);
      } catch (e) {
        console.error('Error in error listener:', e);
      }
    });

    // Log to console in development
    if (process.env.NODE_ENV === 'development') {
      console.error('Error handled:', errorDetails);
    }
  }

  /**
   * Handle HTTP errors specifically.
   */
  handleHttpError(error: any, context?: string): void {
    if (error?.status) {
      switch (error.status) {
        case 401:
          this.handleAuthError(error, context);
          break;
        case 403:
          this.handleForbiddenError(error, context);
          break;
        case 404:
          this.handleNotFoundError(error, context);
          break;
        case 429:
          this.handleRateLimitError(error, context);
          break;
        case 500:
        case 502:
        case 503:
        case 504:
          this.handleServerError(error, context);
          break;
        default:
          this.handleError(error, context);
      }
    } else {
      this.handleError(error, context);
    }
  }

  /**
   * Handle authentication errors.
   */
  private handleAuthError(error: any, context?: string): void {
    const notification: ErrorNotification = {
      id: this.generateId(),
      type: 'error',
      title: 'Authentication Required',
      message: 'Please log in to continue.',
      actions: [
        {
          label: 'Login',
          onClick: () => {
            // Redirect to login
            window.location.href = '/login';
          }
        }
      ]
    };

    this.notifyListeners(notification);
  }

  /**
   * Handle forbidden errors.
   */
  private handleForbiddenError(error: any, context?: string): void {
    const notification: ErrorNotification = {
      id: this.generateId(),
      type: 'error',
      title: 'Access Denied',
      message: 'You do not have permission to perform this action.',
      duration: 5000
    };

    this.notifyListeners(notification);
  }

  /**
   * Handle not found errors.
   */
  private handleNotFoundError(error: any, context?: string): void {
    const notification: ErrorNotification = {
      id: this.generateId(),
      type: 'warning',
      title: 'Not Found',
      message: 'The requested resource was not found.',
      duration: 3000
    };

    this.notifyListeners(notification);
  }

  /**
   * Handle rate limit errors.
   */
  private handleRateLimitError(error: any, context?: string): void {
    const notification: ErrorNotification = {
      id: this.generateId(),
      type: 'warning',
      title: 'Rate Limited',
      message: 'Too many requests. Please try again later.',
      duration: 5000
    };

    this.notifyListeners(notification);
  }

  /**
   * Handle server errors.
   */
  private handleServerError(error: any, context?: string): void {
    const notification: ErrorNotification = {
      id: this.generateId(),
      type: 'error',
      title: 'Server Error',
      message: 'A server error occurred. Please try again.',
      actions: [
        {
          label: 'Retry',
          onClick: () => {
            // Retry logic would be implemented by the caller
            window.location.reload();
          }
        }
      ]
    };

    this.notifyListeners(notification);
  }

  /**
   * Parse error into standardized format.
   */
  private parseError(error: Error | unknown, context?: string): ErrorDetails {
    let message = 'An unexpected error occurred';
    let code: string | undefined;
    let details: any;

    if (error instanceof Error) {
      message = error.message;
      code = (error as any).code;
      details = (error as any).details || (error as any).data;
    } else if (typeof error === 'string') {
      message = error;
    } else if (error && typeof error === 'object') {
      const errorObj = error as any;
      message = errorObj.message || errorObj.error || message;
      code = errorObj.code || errorObj.status;
      details = errorObj.details || errorObj.data;
    }

    return {
      message,
      code,
      details,
      timestamp: new Date().toISOString(),
      correlationId: this.generateId()
    };
  }

  /**
   * Create notification from error details.
   */
  private createNotification(errorDetails: ErrorDetails): ErrorNotification {
    return {
      id: this.generateId(),
      type: 'error',
      title: 'Error',
      message: errorDetails.message,
      duration: 5000
    };
  }

  /**
   * Notify all listeners.
   */
  private notifyListeners(notification: ErrorNotification): void {
    this.errorListeners.forEach(listener => {
      try {
        listener(notification);
      } catch (e) {
        console.error('Error in error listener:', e);
      }
    });
  }

  /**
   * Generate unique ID.
   */
  private generateId(): string {
    return Math.random().toString(36).substr(2, 9);
  }

  /**
   * Get error history.
   */
  getErrorHistory(): ErrorDetails[] {
    return [...this.errorHistory];
  }

  /**
   * Clear error history.
   */
  clearErrorHistory(): void {
    this.errorHistory = [];
  }

  /**
   * Show success notification.
   */
  showSuccess(message: string, title: string = 'Success'): void {
    const notification: ErrorNotification = {
      id: this.generateId(),
      type: 'info',
      title,
      message,
      duration: 3000
    };

    this.notifyListeners(notification);
  }

  /**
   * Show warning notification.
   */
  showWarning(message: string, title: string = 'Warning'): void {
    const notification: ErrorNotification = {
      id: this.generateId(),
      type: 'warning',
      title,
      message,
      duration: 4000
    };

    this.notifyListeners(notification);
  }
}

// Export singleton instance
export const errorHandler = new ErrorHandler();