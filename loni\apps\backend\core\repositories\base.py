"""
Base repository class for database operations.

This module provides a generic base repository following the Repository pattern
and Single Responsibility Principle.
"""

from abc import ABC, abstractmethod
from typing import Any, Dict, Generic, List, Optional, Sequence, TypeVar, Union
from uuid import UUID

from sqlalchemy import and_, delete, func, or_, select, update
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload
from sqlalchemy.sql import Select

from ..models.base import BaseModel

ModelType = TypeVar("ModelType", bound=BaseModel)


class BaseRepository(Generic[ModelType], ABC):
    """
    Base repository class for database operations.
    
    Provides common CRUD operations following the Repository pattern.
    """
    
    def __init__(self, model: type[ModelType], session: AsyncSession):
        """
        Initialize repository.
        
        Args:
            model: SQLAlchemy model class
            session: Database session
        """
        self.model = model
        self.session = session
    
    async def create(self, **kwargs) -> ModelType:
        """
        Create a new record.
        
        Args:
            **kwargs: Model field values
            
        Returns:
            Created model instance
        """
        instance = self.model(**kwargs)
        self.session.add(instance)
        await self.session.commit()
        await self.session.refresh(instance)
        return instance
    
    async def get_by_id(self, id: UUID, options: Optional[List] = None) -> Optional[ModelType]:
        """
        Get record by ID.
        
        Args:
            id: Record ID
            options: Query options (joins, etc.)
            
        Returns:
            Model instance or None
        """
        query = select(self.model).where(self.model.id == id)
        
        if options:
            query = query.options(*options)
        
        result = await self.session.execute(query)
        return result.scalar_one_or_none()
    
    async def get_by_field(
        self, 
        field_name: str, 
        value: Any, 
        options: Optional[List] = None
    ) -> Optional[ModelType]:
        """
        Get record by field value.
        
        Args:
            field_name: Field name to filter by
            value: Field value
            options: Query options
            
        Returns:
            Model instance or None
        """
        field = getattr(self.model, field_name)
        query = select(self.model).where(field == value)
        
        if options:
            query = query.options(*options)
        
        result = await self.session.execute(query)
        return result.scalar_one_or_none()
    
    async def get_all(
        self,
        limit: Optional[int] = None,
        offset: Optional[int] = None,
        options: Optional[List] = None,
        filters: Optional[Dict[str, Any]] = None,
        order_by: Optional[str] = None,
    ) -> List[ModelType]:
        """
        Get all records with optional filtering and pagination.
        
        Args:
            limit: Maximum number of records
            offset: Number of records to skip
            options: Query options
            filters: Filter conditions
            order_by: Order by field name
            
        Returns:
            List of model instances
        """
        query = select(self.model)
        
        # Apply filters
        if filters:
            conditions = []
            for field_name, value in filters.items():
                field = getattr(self.model, field_name)
                if isinstance(value, list):
                    conditions.append(field.in_(value))
                else:
                    conditions.append(field == value)
            query = query.where(and_(*conditions))
        
        # Apply ordering
        if order_by:
            if order_by.startswith('-'):
                field_name = order_by[1:]
                field = getattr(self.model, field_name)
                query = query.order_by(field.desc())
            else:
                field = getattr(self.model, order_by)
                query = query.order_by(field)
        
        # Apply options
        if options:
            query = query.options(*options)
        
        # Apply pagination
        if offset:
            query = query.offset(offset)
        if limit:
            query = query.limit(limit)
        
        result = await self.session.execute(query)
        return list(result.scalars().all())
    
    async def update(self, id: UUID, **kwargs) -> Optional[ModelType]:
        """
        Update record by ID.
        
        Args:
            id: Record ID
            **kwargs: Fields to update
            
        Returns:
            Updated model instance or None
        """
        instance = await self.get_by_id(id)
        if not instance:
            return None
        
        for field, value in kwargs.items():
            if hasattr(instance, field):
                setattr(instance, field, value)
        
        await self.session.commit()
        await self.session.refresh(instance)
        return instance
    
    async def delete(self, id: UUID) -> bool:
        """
        Delete record by ID.
        
        Args:
            id: Record ID
            
        Returns:
            True if deleted, False if not found
        """
        instance = await self.get_by_id(id)
        if not instance:
            return False
        
        await self.session.delete(instance)
        await self.session.commit()
        return True
    
    async def soft_delete(self, id: UUID) -> bool:
        """
        Soft delete record by ID (if model supports it).
        
        Args:
            id: Record ID
            
        Returns:
            True if soft deleted, False if not found
        """
        instance = await self.get_by_id(id)
        if not instance:
            return False
        
        if hasattr(instance, 'soft_delete'):
            instance.soft_delete()
            await self.session.commit()
            return True
        
        return False
    
    async def count(self, filters: Optional[Dict[str, Any]] = None) -> int:
        """
        Count records with optional filtering.
        
        Args:
            filters: Filter conditions
            
        Returns:
            Number of records
        """
        query = select(func.count(self.model.id))
        
        if filters:
            conditions = []
            for field_name, value in filters.items():
                field = getattr(self.model, field_name)
                if isinstance(value, list):
                    conditions.append(field.in_(value))
                else:
                    conditions.append(field == value)
            query = query.where(and_(*conditions))
        
        result = await self.session.execute(query)
        return result.scalar() or 0
    
    async def exists(self, filters: Dict[str, Any]) -> bool:
        """
        Check if record exists with given filters.
        
        Args:
            filters: Filter conditions
            
        Returns:
            True if exists, False otherwise
        """
        conditions = []
        for field_name, value in filters.items():
            field = getattr(self.model, field_name)
            conditions.append(field == value)
        
        query = select(func.count(self.model.id)).where(and_(*conditions))
        result = await self.session.execute(query)
        count = result.scalar() or 0
        return count > 0
    
    async def bulk_create(self, records: List[Dict[str, Any]]) -> List[ModelType]:
        """
        Create multiple records in bulk.
        
        Args:
            records: List of record data
            
        Returns:
            List of created instances
        """
        instances = [self.model(**record) for record in records]
        self.session.add_all(instances)
        await self.session.commit()
        
        # Refresh all instances
        for instance in instances:
            await self.session.refresh(instance)
        
        return instances
    
    async def bulk_update(self, updates: List[Dict[str, Any]]) -> int:
        """
        Update multiple records in bulk.
        
        Args:
            updates: List of update data with 'id' field
            
        Returns:
            Number of updated records
        """
        if not updates:
            return 0
        
        # Group updates by fields to optimize queries
        field_groups = {}
        for update_data in updates:
            id_val = update_data.pop('id')
            fields_key = tuple(sorted(update_data.keys()))
            
            if fields_key not in field_groups:
                field_groups[fields_key] = []
            
            field_groups[fields_key].append({'id': id_val, **update_data})
        
        total_updated = 0
        
        for fields, group_updates in field_groups.items():
            # Prepare bulk update statement
            stmt = update(self.model)
            
            # Execute bulk update for this group
            for update_data in group_updates:
                id_val = update_data.pop('id')
                await self.session.execute(
                    stmt.where(self.model.id == id_val).values(**update_data)
                )
                total_updated += 1
        
        await self.session.commit()
        return total_updated
    
    async def search(
        self,
        query: str,
        search_fields: List[str],
        limit: Optional[int] = None,
        offset: Optional[int] = None,
    ) -> List[ModelType]:
        """
        Search records by text query across multiple fields.
        
        Args:
            query: Search query
            search_fields: Fields to search in
            limit: Maximum number of results
            offset: Number of results to skip
            
        Returns:
            List of matching records
        """
        if not query.strip():
            return []
        
        # Build search conditions
        search_conditions = []
        for field_name in search_fields:
            field = getattr(self.model, field_name)
            search_conditions.append(field.ilike(f"%{query}%"))
        
        # Build query
        db_query = select(self.model).where(or_(*search_conditions))
        
        if offset:
            db_query = db_query.offset(offset)
        if limit:
            db_query = db_query.limit(limit)
        
        result = await self.session.execute(db_query)
        return list(result.scalars().all())
    
    def get_query(self) -> Select:
        """
        Get base query for the model.
        
        Returns:
            SQLAlchemy Select query
        """
        return select(self.model)