"""
Dependency resolution engine for template dependencies.

Implements graph algorithms to resolve dependencies, detect conflicts,
and provide optimal ordering for template processing.
"""

import networkx as nx
from typing import Set, List, Dict, Any, Optional, Tuple
from collections import defaultdict, deque

from .interfaces import IDependency<PERSON>esolver, ITemplateRegistry
from .models import <PERSON>pen<PERSON>cyResult, DependencyGraph, TemplateConflict, ConflictType


class DependencyResolver(IDependencyResolver):
    """
    Advanced dependency resolver using graph algorithms.
    
    Implements topological sorting, cycle detection, and conflict resolution
    to provide optimal template ordering and dependency management.
    """
    
    def __init__(self, template_registry: ITemplateRegistry):
        """Initialize dependency resolver with template registry."""
        self.template_registry = template_registry
        self._dependency_cache: Dict[frozenset, DependencyResult] = {}
        self._graph_cache: Optional[nx.DiGraph] = None
    
    def resolve_dependencies(self, selected_templates: Set[str]) -> DependencyResult:
        """
        Resolve all dependencies for selected templates.
        
        Args:
            selected_templates: Set of initially selected template IDs
            
        Returns:
            DependencyResult with resolved templates in order and any issues
        """
        # Check cache first
        cache_key = frozenset(selected_templates)
        if cache_key in self._dependency_cache:
            return self._dependency_cache[cache_key]
        
        # Build dependency graph
        dependency_graph = self._build_dependency_graph(selected_templates)
        
        # Detect circular dependencies
        circular_deps = self.detect_circular_dependencies(selected_templates)
        if circular_deps:
            result = DependencyResult(
                resolved_templates=[],
                circular_dependencies=circular_deps
            )
            self._dependency_cache[cache_key] = result
            return result
        
        # Find missing dependencies
        missing_deps = self._find_missing_dependencies(selected_templates, dependency_graph)
        
        # Add missing dependencies to selection
        all_templates = selected_templates.union(set(missing_deps))
        
        # Get conflicts
        conflicts = self._detect_conflicts(all_templates)
        
        if conflicts or missing_deps:
            # Return partial result with issues
            result = DependencyResult(
                resolved_templates=list(all_templates),
                missing_dependencies=missing_deps,
                conflicts=[(c.template1, c.template2) for c in conflicts]
            )
        else:
            # Get topological order
            ordered_templates = self.get_dependency_order(all_templates)
            
            # Get optional suggestions
            optional_suggestions = self._get_optional_suggestions(all_templates)
            
            result = DependencyResult(
                resolved_templates=ordered_templates,
                optional_suggestions=optional_suggestions
            )
        
        # Cache result
        self._dependency_cache[cache_key] = result
        return result
    
    def get_dependency_order(self, templates: Set[str]) -> List[str]:
        """
        Get templates in dependency order using topological sort.
        
        Args:
            templates: Set of template IDs to order
            
        Returns:
            List of template IDs in dependency order
        """
        # Build graph for the selected templates
        graph = nx.DiGraph()
        
        # Add all templates as nodes
        for template_id in templates:
            graph.add_node(template_id)
        
        # Add dependency edges
        for template_id in templates:
            template = self.template_registry.get_template(template_id)
            if template:
                metadata = template.get_metadata()
                for dep in metadata.dependencies:
                    if dep in templates:  # Only include dependencies that are in our set
                        graph.add_edge(dep, template_id)  # dep -> template_id
        
        try:
            # Topological sort returns dependencies first
            return list(nx.topological_sort(graph))
        except nx.NetworkXError:
            # Fallback to simple ordering if cycles exist
            return list(templates)
    
    def detect_circular_dependencies(self, templates: Set[str]) -> List[List[str]]:
        """
        Detect circular dependency chains.
        
        Args:
            templates: Set of template IDs to check
            
        Returns:
            List of circular dependency chains
        """
        graph = nx.DiGraph()
        
        # Build dependency graph
        for template_id in templates:
            template = self.template_registry.get_template(template_id)
            if template:
                metadata = template.get_metadata()
                for dep in metadata.dependencies:
                    graph.add_edge(template_id, dep)  # template_id depends on dep
        
        # Find strongly connected components with more than one node
        cycles = []
        try:
            for component in nx.strongly_connected_components(graph):
                if len(component) > 1:
                    cycles.append(list(component))
                elif len(component) == 1:
                    # Check for self-loops
                    node = list(component)[0]
                    if graph.has_edge(node, node):
                        cycles.append([node])
        except nx.NetworkXError:
            pass
        
        return cycles
    
    def get_dependents(self, template_id: str) -> Set[str]:
        """Get templates that depend on the given template."""
        dependents = set()
        
        for template in self.template_registry.get_all_templates():
            metadata = template.get_metadata()
            if template_id in metadata.dependencies:
                dependents.add(metadata.id)
        
        return dependents
    
    def get_dependency_tree(self, template_id: str) -> Dict[str, Any]:
        """Get complete dependency tree for a template."""
        template = self.template_registry.get_template(template_id)
        if not template:
            return {}
        
        metadata = template.get_metadata()
        tree = {
            'id': template_id,
            'name': metadata.name,
            'dependencies': []
        }
        
        for dep in metadata.dependencies:
            tree['dependencies'].append(self.get_dependency_tree(dep))
        
        return tree
    
    def _build_dependency_graph(self, selected_templates: Set[str]) -> DependencyGraph:
        """Build dependency graph for the selected templates."""
        graph = DependencyGraph()
        visited = set()
        
        def add_template_and_deps(template_id: str):
            if template_id in visited:
                return
            visited.add(template_id)
            
            template = self.template_registry.get_template(template_id)
            if not template:
                return
            
            metadata = template.get_metadata()
            graph.nodes.add(template_id)
            
            for dep in metadata.dependencies:
                graph.add_dependency(template_id, dep)
                add_template_and_deps(dep)  # Recursively add dependencies
        
        # Start with selected templates and recursively add dependencies
        for template_id in selected_templates:
            add_template_and_deps(template_id)
        
        return graph
    
    def _find_missing_dependencies(self, selected_templates: Set[str], graph: DependencyGraph) -> List[str]:
        """Find dependencies that are required but not selected."""
        missing = []
        
        for template_id in selected_templates:
            template = self.template_registry.get_template(template_id)
            if template:
                metadata = template.get_metadata()
                for dep in metadata.dependencies:
                    if dep not in selected_templates and dep not in missing:
                        missing.append(dep)
        
        return missing
    
    def _detect_conflicts(self, templates: Set[str]) -> List[TemplateConflict]:
        """Detect conflicts between templates."""
        conflicts = []
        
        # Check explicit conflicts
        for template_id in templates:
            template = self.template_registry.get_template(template_id)
            if template:
                metadata = template.get_metadata()
                for conflict_id in metadata.conflicts:
                    if conflict_id in templates:
                        # Get conflict type
                        conflict_type = metadata.conflict_types.get(conflict_id, ConflictType.MUTUALLY_EXCLUSIVE)
                        
                        # Avoid duplicate conflicts
                        conflict_pair = tuple(sorted([template_id, conflict_id]))
                        if not any(c.template1 == conflict_pair[0] and c.template2 == conflict_pair[1] for c in conflicts):
                            conflicts.append(TemplateConflict(
                                template1=conflict_pair[0],
                                template2=conflict_pair[1],
                                conflict_type=conflict_type,
                                description=f"Templates '{conflict_pair[0]}' and '{conflict_pair[1]}' are mutually exclusive"
                            ))
        
        # Check for resource conflicts (ports, volumes)
        conflicts.extend(self._detect_port_conflicts(templates))
        conflicts.extend(self._detect_volume_conflicts(templates))
        
        return conflicts
    
    def _detect_port_conflicts(self, templates: Set[str]) -> List[TemplateConflict]:
        """Detect port conflicts between templates."""
        port_usage = defaultdict(list)
        conflicts = []
        
        for template_id in templates:
            template = self.template_registry.get_template(template_id)
            if template:
                metadata = template.get_metadata()
                for port in metadata.required_ports:
                    port_usage[port].append(template_id)
        
        for port, template_list in port_usage.items():
            if len(template_list) > 1:
                for i in range(len(template_list)):
                    for j in range(i + 1, len(template_list)):
                        conflicts.append(TemplateConflict(
                            template1=template_list[i],
                            template2=template_list[j],
                            conflict_type=ConflictType.RESOURCE_CONFLICT,
                            description=f"Both templates require port {port}",
                            resolution_suggestion=f"Configure different ports for one of the services"
                        ))
        
        return conflicts
    
    def _detect_volume_conflicts(self, templates: Set[str]) -> List[TemplateConflict]:
        """Detect volume conflicts between templates."""
        volume_usage = defaultdict(list)
        conflicts = []
        
        for template_id in templates:
            template = self.template_registry.get_template(template_id)
            if template:
                metadata = template.get_metadata()
                for volume in metadata.volumes:
                    volume_usage[volume].append(template_id)
        
        # Volume sharing might be intentional, so these are warnings rather than errors
        for volume, template_list in volume_usage.items():
            if len(template_list) > 1:
                for i in range(len(template_list)):
                    for j in range(i + 1, len(template_list)):
                        conflicts.append(TemplateConflict(
                            template1=template_list[i],
                            template2=template_list[j],
                            conflict_type=ConflictType.RESOURCE_CONFLICT,
                            description=f"Both templates use volume '{volume}'",
                            resolution_suggestion="Ensure volume sharing is intentional"
                        ))
        
        return conflicts
    
    def _get_optional_suggestions(self, templates: Set[str]) -> List[str]:
        """Get optional dependencies that would enhance the project."""
        suggestions = []
        
        for template_id in templates:
            template = self.template_registry.get_template(template_id)
            if template:
                metadata = template.get_metadata()
                for optional_dep in metadata.optional_dependencies:
                    if optional_dep not in templates and optional_dep not in suggestions:
                        # Check if the optional dependency exists
                        if self.template_registry.get_template(optional_dep):
                            suggestions.append(optional_dep)
        
        return suggestions
    
    def clear_cache(self):
        """Clear dependency resolution cache."""
        self._dependency_cache.clear()
        self._graph_cache = None
    
    def get_cache_stats(self) -> Dict[str, int]:
        """Get cache statistics."""
        return {
            'cache_size': len(self._dependency_cache),
            'cache_hits': getattr(self, '_cache_hits', 0),
            'cache_misses': getattr(self, '_cache_misses', 0)
        }


class OptimizedDependencyResolver(DependencyResolver):
    """
    Optimized version of dependency resolver with caching and performance improvements.
    
    Uses NetworkX for graph operations and implements advanced caching strategies.
    """
    
    def __init__(self, template_registry: ITemplateRegistry, cache_size: int = 1000):
        """Initialize optimized resolver with cache size limit."""
        super().__init__(template_registry)
        self.cache_size = cache_size
        self._cache_hits = 0
        self._cache_misses = 0
    
    def resolve_dependencies(self, selected_templates: Set[str]) -> DependencyResult:
        """Resolve dependencies with optimized caching."""
        cache_key = frozenset(selected_templates)
        
        if cache_key in self._dependency_cache:
            self._cache_hits += 1
            return self._dependency_cache[cache_key]
        
        self._cache_misses += 1
        
        # Use parent implementation
        result = super().resolve_dependencies(selected_templates)
        
        # Manage cache size
        if len(self._dependency_cache) >= self.cache_size:
            # Remove oldest entries (simple FIFO for now)
            oldest_keys = list(self._dependency_cache.keys())[:self.cache_size // 2]
            for key in oldest_keys:
                del self._dependency_cache[key]
        
        self._dependency_cache[cache_key] = result
        return result
    
    def get_dependency_order(self, templates: Set[str]) -> List[str]:
        """Optimized dependency ordering using NetworkX."""
        if not templates:
            return []
        
        # Use NetworkX for better performance on large graphs
        graph = nx.DiGraph()
        
        # Add nodes and edges efficiently
        graph.add_nodes_from(templates)
        
        edges = []
        for template_id in templates:
            template = self.template_registry.get_template(template_id)
            if template:
                metadata = template.get_metadata()
                for dep in metadata.dependencies:
                    if dep in templates:
                        edges.append((dep, template_id))
        
        graph.add_edges_from(edges)
        
        # Use NetworkX topological sort
        try:
            return list(nx.topological_sort(graph))
        except nx.NetworkXError:
            # Handle cycles by using a partial order
            return self._partial_topological_sort(graph)
    
    def _partial_topological_sort(self, graph: nx.DiGraph) -> List[str]:
        """
        Perform partial topological sort when cycles exist.
        
        Uses Kahn's algorithm with cycle breaking.
        """
        result = []
        in_degree = dict(graph.in_degree())
        queue = deque([node for node, degree in in_degree.items() if degree == 0])
        
        while queue:
            node = queue.popleft()
            result.append(node)
            
            for successor in graph.successors(node):
                in_degree[successor] -= 1
                if in_degree[successor] == 0:
                    queue.append(successor)
        
        # Add remaining nodes (those in cycles)
        remaining = set(graph.nodes()) - set(result)
        result.extend(remaining)
        
        return result