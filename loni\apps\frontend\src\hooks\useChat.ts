/**
 * Chat functionality React hooks
 */
import { useState, useCallback, useRef } from 'react'
import { apiClient } from '@/lib/api'
import type { 
  Conversation, 
  Message, 
  ChatRequest, 
  ChatResponse, 
  ModelInfo,
  ConversationWithMessages 
} from '@/types'

interface UseChatReturn {
  conversations: Conversation[]
  currentConversation: Conversation | null
  messages: Message[]
  isGenerating: boolean
  streamingMessage: string
  error: string | null
  models: ModelInfo[]
  sendMessage: (message: string, options?: Partial<ChatRequest>) => Promise<void>
  sendMessageStream: (message: string, options?: Partial<ChatRequest>) => Promise<void>
  createConversation: (title?: string) => Promise<Conversation>
  selectConversation: (conversationId: string) => Promise<void>
  deleteConversation: (conversationId: string) => Promise<void>
  updateConversation: (conversationId: string, updates: Partial<Conversation>) => Promise<void>
  loadConversations: () => Promise<void>
  loadModels: () => Promise<void>
  clearError: () => void
}

export function useChat(): UseChatReturn {
  const [conversations, setConversations] = useState<Conversation[]>([])
  const [currentConversation, setCurrentConversation] = useState<Conversation | null>(null)
  const [messages, setMessages] = useState<Message[]>([])
  const [isGenerating, setIsGenerating] = useState(false)
  const [streamingMessage, setStreamingMessage] = useState('')
  const [error, setError] = useState<string | null>(null)
  const [models, setModels] = useState<ModelInfo[]>([])
  
  const abortControllerRef = useRef<AbortController | null>(null)

  const clearError = useCallback(() => {
    setError(null)
  }, [])

  const loadModels = useCallback(async () => {
    try {
      const modelsData = await apiClient.get<ModelInfo[]>('/chat/models')
      setModels(modelsData)
    } catch (err: any) {
      setError(err.detail || 'Failed to load models')
    }
  }, [])

  const loadConversations = useCallback(async () => {
    try {
      const conversationsData = await apiClient.get<Conversation[]>('/chat/conversations')
      setConversations(conversationsData)
    } catch (err: any) {
      setError(err.detail || 'Failed to load conversations')
    }
  }, [])

  const createConversation = useCallback(async (title?: string): Promise<Conversation> => {
    try {
      const conversation = await apiClient.post<Conversation>('/chat/conversations', {
        title: title || 'New Conversation'
      })
      setConversations(prev => [conversation, ...prev])
      return conversation
    } catch (err: any) {
      setError(err.detail || 'Failed to create conversation')
      throw err
    }
  }, [])

  const selectConversation = useCallback(async (conversationId: string) => {
    try {
      const conversationData = await apiClient.get<ConversationWithMessages>(
        `/chat/conversations/${conversationId}`
      )
      setCurrentConversation(conversationData)
      setMessages(conversationData.messages || [])
      setError(null)
    } catch (err: any) {
      setError(err.detail || 'Failed to load conversation')
    }
  }, [])

  const deleteConversation = useCallback(async (conversationId: string) => {
    try {
      await apiClient.delete(`/chat/conversations/${conversationId}`)
      setConversations(prev => prev.filter(c => c.id !== conversationId))
      
      // If deleting current conversation, clear it
      if (currentConversation?.id === conversationId) {
        setCurrentConversation(null)
        setMessages([])
      }
    } catch (err: any) {
      setError(err.detail || 'Failed to delete conversation')
    }
  }, [currentConversation])

  const updateConversation = useCallback(async (
    conversationId: string, 
    updates: Partial<Conversation>
  ) => {
    try {
      const updatedConversation = await apiClient.put<Conversation>(
        `/chat/conversations/${conversationId}`, 
        updates
      )
      
      setConversations(prev => 
        prev.map(c => c.id === conversationId ? updatedConversation : c)
      )
      
      if (currentConversation?.id === conversationId) {
        setCurrentConversation(updatedConversation)
      }
    } catch (err: any) {
      setError(err.detail || 'Failed to update conversation')
    }
  }, [currentConversation])

  const sendMessage = useCallback(async (
    message: string, 
    options: Partial<ChatRequest> = {}
  ) => {
    if (!message.trim() || isGenerating) return

    setIsGenerating(true)
    setError(null)

    try {
      // Create conversation if none selected
      let conversationId = currentConversation?.id || options.conversation_id
      if (!conversationId) {
        const newConversation = await createConversation()
        conversationId = newConversation.id
        setCurrentConversation(newConversation)
      }

      const chatRequest: ChatRequest = {
        message,
        conversation_id: conversationId,
        model_name: options.model_name || 'gpt-4',
        rag_enabled: options.rag_enabled ?? true,
        ...options
      }

      const response = await apiClient.post<ChatResponse>('/chat/completions', chatRequest)
      
      // Add both messages to the conversation
      setMessages(prev => [...prev, response.user_message, response.assistant_message])
      
    } catch (err: any) {
      setError(err.detail || 'Failed to send message')
    } finally {
      setIsGenerating(false)
    }
  }, [currentConversation, isGenerating, createConversation])

  const sendMessageStream = useCallback(async (
    message: string, 
    options: Partial<ChatRequest> = {}
  ) => {
    if (!message.trim() || isGenerating) return

    setIsGenerating(true)
    setStreamingMessage('')
    setError(null)

    // Cancel any existing stream
    if (abortControllerRef.current) {
      abortControllerRef.current.abort()
    }

    abortControllerRef.current = new AbortController()

    try {
      // Create conversation if none selected
      let conversationId = currentConversation?.id || options.conversation_id
      if (!conversationId) {
        const newConversation = await createConversation()
        conversationId = newConversation.id
        setCurrentConversation(newConversation)
      }

      const chatRequest: ChatRequest = {
        message,
        conversation_id: conversationId,
        model_name: options.model_name || 'gpt-4',
        rag_enabled: options.rag_enabled ?? true,
        stream: true,
        ...options
      }

      // Add user message immediately
      const userMessage: Message = {
        id: `temp-${Date.now()}`,
        conversation_id: conversationId,
        role: 'user',
        content: message,
        created_at: new Date().toISOString()
      }
      setMessages(prev => [...prev, userMessage])

      const response = await fetch(`${apiClient['baseUrl']}/chat/stream`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('auth_token')}`
        },
        body: JSON.stringify(chatRequest),
        signal: abortControllerRef.current.signal
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const reader = response.body?.getReader()
      const decoder = new TextDecoder()

      let fullResponse = ''
      
      while (true) {
        const { done, value } = await reader!.read()
        if (done) break

        const chunk = decoder.decode(value)
        const lines = chunk.split('\n')

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            const data = line.slice(6)
            if (data.trim()) {
              try {
                const parsed = JSON.parse(data)
                if (parsed.content) {
                  fullResponse += parsed.content
                  setStreamingMessage(fullResponse)
                } else if (parsed.done) {
                  // Stream complete, add assistant message
                  const assistantMessage: Message = {
                    id: `assistant-${Date.now()}`,
                    conversation_id: conversationId,
                    role: 'assistant',
                    content: fullResponse,
                    created_at: new Date().toISOString()
                  }
                  setMessages(prev => [...prev, assistantMessage])
                  setStreamingMessage('')
                }
              } catch (parseError) {
                console.warn('Failed to parse stream data:', data)
              }
            }
          }
        }
      }

    } catch (err: any) {
      if (err.name !== 'AbortError') {
        setError(err.message || 'Failed to send message')
      }
    } finally {
      setIsGenerating(false)
      setStreamingMessage('')
      abortControllerRef.current = null
    }
  }, [currentConversation, isGenerating, createConversation])

  return {
    conversations,
    currentConversation,
    messages,
    isGenerating,
    streamingMessage,
    error,
    models,
    sendMessage,
    sendMessageStream,
    createConversation,
    selectConversation,
    deleteConversation,
    updateConversation,
    loadConversations,
    loadModels,
    clearError,
  }
}