"""
User-related API schemas.
"""
import uuid
from typing import Optional

from fastapi_users import schemas
from pydantic import BaseModel, EmailStr


class UserRead(schemas.BaseUser[uuid.UUID]):
    """Schema for reading user data."""
    name: str
    api_quota_limit: int
    api_quota_used: int


class UserCreate(schemas.BaseUserCreate):
    """Schema for creating users."""
    name: str
    api_quota_limit: Optional[int] = 100000


class UserUpdate(schemas.BaseUserUpdate):
    """Schema for updating users."""
    name: Optional[str] = None
    api_quota_limit: Optional[int] = None


class UserQuotaStatus(BaseModel):
    """Schema for user quota status."""
    used: int
    limit: int
    remaining: int
    percentage_used: float


class UserProfile(BaseModel):
    """Schema for user profile data."""
    id: uuid.UUID
    email: EmailStr
    name: str
    is_active: bool
    is_verified: bool
    is_superuser: bool
    quota_status: UserQuotaStatus
    
    class Config:
        from_attributes = True