"""
Logging configuration settings.

This module contains all logging-related configuration
following the Single Responsibility Principle.
"""

from typing import Optional
from pydantic_settings import BaseSettings


class LoggingSettings(BaseSettings):
    """Logging configuration settings."""
    
    level: str = "INFO"
    format: str = "json"
    
    # File logging
    log_file: Optional[str] = None
    max_file_size: int = 10 * 1024 * 1024  # 10MB
    backup_count: int = 5
    
    # Structured logging
    include_trace_id: bool = True
    include_user_id: bool = True
    
    class Config:
        env_prefix = "LOGGING_"