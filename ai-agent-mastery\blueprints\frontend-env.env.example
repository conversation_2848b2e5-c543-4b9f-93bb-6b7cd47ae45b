# ---------------------------------------------------------------------------
# FRONTEND SERVICE ENVIRONMENT VARIABLES
# ---------------------------------------------------------------------------
# These variables are used by the React frontend application.
# Copy these values to your Render frontend service environment settings.

# ---------------------------------------------------------------------------
# AGENT API CONFIGURATION
# ---------------------------------------------------------------------------

# The endpoint URL for the agent API service
# This should point to your deployed agent API service
# Format: https://api.YOUR_CUSTOM_DOMAIN/api/pydantic-agent
# Example: https://api.myapp.com/api/pydantic-agent
VITE_AGENT_ENDPOINT=https://YOUR_API_DOMAIN/api/pydantic-agent

# ---------------------------------------------------------------------------
# APPLICATION URLs
# ---------------------------------------------------------------------------

# The main URL where your frontend application will be hosted
# This is used for OAuth redirects and internal links
# Format: https://YOUR_CUSTOM_DOMAIN
# Example: https://myapp.com
VITE_APP_URL=https://YOUR_CUSTOM_DOMAIN

# OAuth callback URL for Supabase authentication
# Must match the redirect URL configured in your Supabase project
# Format: https://YOUR_CUSTOM_DOMAIN/auth/callback
# Example: https://myapp.com/auth/callback
VITE_REDIRECT_URL=https://YOUR_CUSTOM_DOMAIN/auth/callback

# ---------------------------------------------------------------------------
# STREAMING CONFIGURATION
# ---------------------------------------------------------------------------

# Enable streaming responses from the agent API (recommended: true)
# Set to "false" if you experience issues with streaming
VITE_ENABLE_STREAMING=true

# ---------------------------------------------------------------------------
# SUPABASE CONFIGURATION
# ---------------------------------------------------------------------------

# Your Supabase project URL
# Get this from: Supabase Dashboard -> Project Settings -> API
# https://supabase.com/dashboard/project/<project-id>/settings/api
# Format: https://YOUR_SUPABASE_PROJECT_ID.supabase.co
# Example: https://abcd1234xyz.supabase.co
VITE_SUPABASE_URL=https://YOUR_SUPABASE_PROJECT_ID.supabase.co

# Your Supabase anonymous (public) key
# Get this from: Supabase Dashboard -> Project Settings -> API
# https://supabase.com/dashboard/project/<project-id>/settings/api
# This key is safe to expose in frontend code
VITE_SUPABASE_ANON_KEY=YOUR_SUPABASE_ANON_KEY

# ---------------------------------------------------------------------------
# OPTIONAL LANGFUSE CONFIGURATION
# ---------------------------------------------------------------------------

# Optional: LangFuse integration for admin dashboard
# Enables clickable links to view conversations in LangFuse
# Format: http://your-langfuse-host/project/your-project-id
# Example: http://localhost:3000/project/cm9n7mcx60006ph071x425gar
VITE_LANGFUSE_HOST_WITH_PROJECT=