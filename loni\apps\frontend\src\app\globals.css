@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 47.4% 11.2%;
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96%;
    --accent-foreground: 222.2 47.4% 11.2%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;
    --radius: 0.5rem;
    
    /* LONI Brand Colors */
    --loni-primary: 258 100% 64%;
    --loni-secondary: 280 100% 70%;
    --loni-accent: 240 100% 60%;
    
    /* AI-specific Colors */
    --ai-primary: 200 100% 50%;
    --ai-secondary: 220 100% 95%;
    --ai-secondary-foreground: 200 100% 30%;
    --ai-accent: 180 100% 40%;
    
    /* Status Colors */
    --success: 142 76% 36%;
    --warning: 38 92% 50%;
    --info: 200 100% 50%;
    --error: 0 84% 60%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    
    /* LONI Brand Colors - Dark Mode */
    --loni-primary: 258 100% 70%;
    --loni-secondary: 280 100% 75%;
    --loni-accent: 240 100% 65%;
    
    /* AI-specific Colors - Dark Mode */
    --ai-primary: 200 100% 60%;
    --ai-secondary: 220 50% 20%;
    --ai-secondary-foreground: 200 100% 80%;
    --ai-accent: 180 100% 50%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  
  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
  
  /* Custom scrollbar */
  ::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }
  
  ::-webkit-scrollbar-track {
    @apply bg-transparent;
  }
  
  ::-webkit-scrollbar-thumb {
    @apply bg-border rounded-full;
  }
  
  ::-webkit-scrollbar-thumb:hover {
    @apply bg-muted-foreground/50;
  }
  
  /* Selection */
  ::selection {
    @apply bg-loni-primary/20;
  }
  
  /* Focus styles */
  .focus-ring {
    @apply focus:outline-none focus:ring-2 focus:ring-loni-primary focus:ring-offset-2;
  }
}

@layer components {
  /* Custom animations */
  .animate-gradient {
    background-size: 200% 200%;
    animation: gradient 3s ease infinite;
  }
  
  @keyframes gradient {
    0% {
      background-position: 0% 50%;
    }
    50% {
      background-position: 100% 50%;
    }
    100% {
      background-position: 0% 50%;
    }
  }
  
  .animate-float {
    animation: float 3s ease-in-out infinite;
  }
  
  @keyframes float {
    0%, 100% {
      transform: translateY(0px);
    }
    50% {
      transform: translateY(-10px);
    }
  }
  
  .animate-typing {
    overflow: hidden;
    border-right: 2px solid;
    white-space: nowrap;
    animation: typing 3.5s steps(40, end), blink-caret .75s step-end infinite;
  }
  
  @keyframes typing {
    from {
      width: 0;
    }
    to {
      width: 100%;
    }
  }
  
  @keyframes blink-caret {
    from, to {
      border-color: transparent;
    }
    50% {
      border-color: currentColor;
    }
  }
  
  /* Glass effect */
  .glass {
    @apply bg-white/10 backdrop-blur-md border border-white/20;
  }
  
  .glass-dark {
    @apply bg-black/10 backdrop-blur-md border border-black/20;
  }
  
  /* Gradient text */
  .gradient-text {
    @apply bg-gradient-to-r from-loni-primary to-ai-primary bg-clip-text text-transparent;
  }
  
  /* Chat message animations */
  .message-appear {
    animation: messageAppear 0.3s ease-out;
  }
  
  @keyframes messageAppear {
    from {
      opacity: 0;
      transform: translateY(10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
  
  /* Loading states */
  .loading-dots {
    @apply inline-flex items-center;
  }
  
  .loading-dots::after {
    content: '';
    @apply inline-block w-1 h-1 bg-current rounded-full ml-1;
    animation: loadingDots 1.4s infinite both;
  }
  
  .loading-dots::before {
    content: '';
    @apply inline-block w-1 h-1 bg-current rounded-full mr-1;
    animation: loadingDots 1.4s infinite both;
    animation-delay: -0.16s;
  }
  
  @keyframes loadingDots {
    0%, 80%, 100% {
      transform: scale(0);
      opacity: 0.5;
    }
    40% {
      transform: scale(1);
      opacity: 1;
    }
  }
  
  /* Code syntax highlighting base */
  .prose pre {
    @apply bg-gray-900 text-gray-100 p-4 rounded-lg overflow-x-auto;
  }
  
  .prose code {
    @apply bg-gray-100 text-gray-900 px-1 py-0.5 rounded text-sm;
  }
  
  .prose pre code {
    @apply bg-transparent text-inherit p-0;
  }
  
  /* Mobile optimizations */
  @media (max-width: 768px) {
    .mobile-optimized {
      @apply text-sm p-3;
    }
    
    .mobile-hidden {
      @apply hidden;
    }
    
    .mobile-full {
      @apply w-full;
    }
  }
}

@layer utilities {
  /* Spacing utilities */
  .space-y-0\.5 > :not([hidden]) ~ :not([hidden]) {
    margin-top: 0.125rem;
  }
  
  /* Text utilities */
  .text-balance {
    text-wrap: balance;
  }
  
  /* Layout utilities */
  .container-narrow {
    @apply max-w-4xl mx-auto;
  }
  
  .container-wide {
    @apply max-w-7xl mx-auto;
  }
  
  /* Interactive utilities */
  .interactive {
    @apply transition-all duration-200 ease-in-out;
  }
  
  .interactive:hover {
    @apply scale-105;
  }
  
  .interactive:active {
    @apply scale-95;
  }
}