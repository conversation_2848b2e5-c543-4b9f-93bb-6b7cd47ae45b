# Prometheus alerting rules for LONI platform

groups:
  - name: loni.rules
    rules:
      # Service availability alerts
      - alert: ServiceDown
        expr: up == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "Service {{ $labels.instance }} is down"
          description: "{{ $labels.instance }} of job {{ $labels.job }} has been down for more than 1 minute."

      - alert: HighErrorRate
        expr: rate(http_requests_total{status=~"5.."}[5m]) > 0.1
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High error rate on {{ $labels.instance }}"
          description: "{{ $labels.instance }} has an error rate of {{ $value }} errors per second."

      # Database alerts
      - alert: PostgreSQLDown
        expr: pg_up == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "PostgreSQL is down"
          description: "PostgreSQL database is not responding."

      - alert: PostgreSQLTooManyConnections
        expr: pg_stat_database_numbackends / pg_settings_max_connections > 0.8
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "PostgreSQL has too many connections"
          description: "PostgreSQL is using {{ $value | humanizePercentage }} of available connections."

      # Redis alerts
      - alert: RedisDown
        expr: redis_up == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "Redis is down"
          description: "Redis instance is not responding."

      - alert: RedisMemoryHigh
        expr: redis_memory_used_bytes / redis_memory_max_bytes > 0.9
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "Redis memory usage is high"
          description: "Redis is using {{ $value | humanizePercentage }} of available memory."

      # Application alerts
      - alert: LoniBackendDown
        expr: up{job="loni-backend"} == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "LONI Backend is down"
          description: "LONI Backend API is not responding."

      - alert: LoniFrontendDown
        expr: up{job="loni-frontend"} == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "LONI Frontend is down"
          description: "LONI Frontend application is not responding."

      # Vector database alerts
      - alert: QdrantDown
        expr: up{job="qdrant"} == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "Qdrant vector database is down"
          description: "Qdrant vector database is not responding."

      # System resource alerts
      - alert: HighCPUUsage
        expr: 100 - (avg by(instance) (rate(node_cpu_seconds_total{mode="idle"}[5m])) * 100) > 80
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High CPU usage on {{ $labels.instance }}"
          description: "CPU usage is above 80% for more than 5 minutes."

      - alert: HighMemoryUsage
        expr: (1 - (node_memory_MemAvailable_bytes / node_memory_MemTotal_bytes)) * 100 > 80
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High memory usage on {{ $labels.instance }}"
          description: "Memory usage is above 80% for more than 5 minutes."

      - alert: DiskSpaceLow
        expr: (1 - (node_filesystem_avail_bytes / node_filesystem_size_bytes)) * 100 > 80
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "Disk space low on {{ $labels.instance }}"
          description: "Disk usage is above 80% on {{ $labels.mountpoint }}."

      # AI model alerts
      - alert: OllamaDown
        expr: up{job="ollama"} == 0
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "Ollama AI service is down"
          description: "Ollama local AI service is not responding."

      - alert: HighTokenUsage
        expr: rate(ai_tokens_used_total[1h]) > 10000
        for: 10m
        labels:
          severity: info
        annotations:
          summary: "High AI token usage detected"
          description: "AI token usage rate is {{ $value }} tokens per hour."

      # Monitoring stack alerts
      - alert: PrometheusTargetDown
        expr: up == 0
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "Prometheus target {{ $labels.instance }} is down"
          description: "{{ $labels.instance }} target is down for more than 2 minutes."

      - alert: GrafanaDown
        expr: up{job="grafana"} == 0
        for: 1m
        labels:
          severity: warning
        annotations:
          summary: "Grafana is down"
          description: "Grafana monitoring dashboard is not responding."