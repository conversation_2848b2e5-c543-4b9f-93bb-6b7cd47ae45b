import type { Metada<PERSON> } from 'next'
import { Inter } from 'next/font/google'
import './globals.css'

const inter = Inter({ subsets: ['latin'] })

export const metadata: Metadata = {
  title: 'LONI - AI Platform',
  description: 'Advanced AI platform with retrieval-augmented generation capabilities',
  keywords: ['AI', 'chat', 'GPT', 'RAG', 'artificial intelligence'],
  authors: [{ name: 'LONI Team' }],
  viewport: 'width=device-width, initial-scale=1',
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en">
      <body className={inter.className}>
        {children}
      </body>
    </html>
  )
}