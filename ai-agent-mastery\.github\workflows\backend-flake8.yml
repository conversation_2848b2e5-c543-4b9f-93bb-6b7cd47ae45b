name: Backend Flake8

on:
  workflow_call:
    inputs:
      python-version:
        description: 'Python version to use'
        required: false
        default: '3.12'
        type: string
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]
  workflow_dispatch:

jobs:
  flake8-agent-api:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Set up Python ${{ inputs.python-version || '3.12' }}
      uses: actions/setup-python@v4
      with:
        python-version: ${{ inputs.python-version || '3.12' }}
        
    - name: Install flake8 and dependencies
      run: |
        python -m pip install --upgrade pip
        pip install flake8 flake8-docstrings flake8-import-order flake8-bugbear
        
    - name: Lint Agent API with flake8
      run: |
        cd 6_Agent_Deployment/backend_agent_api
        flake8 . --count --select=E9,F63,F7,F82 --show-source --statistics
        flake8 . --count --exit-zero --max-complexity=10 --max-line-length=127 --statistics
        
  flake8-rag-pipeline:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Set up Python ${{ inputs.python-version || '3.12' }}
      uses: actions/setup-python@v4
      with:
        python-version: ${{ inputs.python-version || '3.12' }}
        
    - name: Install flake8 and dependencies
      run: |
        python -m pip install --upgrade pip
        pip install flake8 flake8-docstrings flake8-import-order flake8-bugbear
        
    - name: Lint RAG Pipeline with flake8
      run: |
        cd 6_Agent_Deployment/backend_rag_pipeline
        flake8 . --count --select=E9,F63,F7,F82 --show-source --statistics
        flake8 . --count --exit-zero --max-complexity=10 --max-line-length=127 --statistics \
          --exclude=venv,venv_linux,__pycache__,.git
        
    - name: Create flake8 annotations
      if: always()
      run: |
        cd 6_Agent_Deployment/backend_rag_pipeline
        flake8 . --format='::error file=%(path)s,line=%(row)d,col=%(col)d::%(path)s:%(row)d:%(col)d: %(code)s %(text)s' \
          --exclude=venv,venv_linux,__pycache__,.git || true