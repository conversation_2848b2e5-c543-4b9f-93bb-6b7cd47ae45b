#!/usr/bin/env python3
"""
Docker Compose Error Monitor
Monitors Docker services and logs only errors and failures with intelligent clearing
"""

import json
import time
import logging
import os
import sys
from datetime import datetime, timezone
from pathlib import Path
from typing import Dict, List, Set, Optional
import docker
import yaml
from http.server import HTTPServer, BaseHTTPRequestHandler
import threading

# Configuration
LOG_DIR = Path("/var/log/docker-compose")
COMPOSE_FILE = Path("/app/docker-compose.yml")
CHECK_INTERVAL = 30  # seconds
LOG_RETENTION_HOURS = 24

# Ensure log directory exists
LOG_DIR.mkdir(parents=True, exist_ok=True)

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class DockerMonitor:
    def __init__(self):
        self.client = docker.from_env()
        self.project_name = "loni"  # From COMPOSE_PROJECT_NAME
        self.error_log_file = LOG_DIR / "docker-errors.jsonl"
        self.status_file = LOG_DIR / "service-status.json"
        self.previous_errors: Set[str] = set()
        self.healthy_services: Set[str] = set()
        
    def get_compose_services(self) -> List[str]:
        """Get list of services from docker-compose.yml"""
        try:
            if COMPOSE_FILE.exists():
                with open(COMPOSE_FILE, 'r') as f:
                    compose_data = yaml.safe_load(f)
                    return list(compose_data.get('services', {}).keys())
            else:
                # Fallback to discovering running containers
                containers = self.client.containers.list(all=True)
                services = set()
                for container in containers:
                    if self.project_name in container.name:
                        service_name = container.name.replace(f"{self.project_name}-", "").replace(f"{self.project_name}_", "")
                        services.add(service_name)
                return list(services)
        except Exception as e:
            logger.error(f"Error getting compose services: {e}")
            return []

    def check_service_health(self, service_name: str) -> Dict:
        """Check health of a specific service"""
        try:
            # Find container for this service
            containers = self.client.containers.list(
                all=True,
                filters={"name": f"{self.project_name}.*{service_name}"}
            )
            
            if not containers:
                return {
                    "status": "missing",
                    "error": f"Container for service '{service_name}' not found"
                }
            
            container = containers[0]
            
            # Check container status
            container.reload()
            status = container.status
            
            if status == "running":
                # Check health if health check is configured
                health = container.attrs.get("State", {}).get("Health", {})
                if health:
                    health_status = health.get("Status", "unknown")
                    if health_status == "healthy":
                        return {"status": "healthy"}
                    elif health_status == "unhealthy":
                        return {
                            "status": "unhealthy",
                            "error": "Health check failed"
                        }
                    else:
                        return {"status": "starting"}
                else:
                    # No health check configured, assume healthy if running
                    return {"status": "healthy"}
            
            elif status == "exited":
                exit_code = container.attrs.get("State", {}).get("ExitCode", 0)
                if exit_code != 0:
                    # Get recent logs for error context
                    logs = container.logs(tail=50).decode('utf-8', errors='ignore')
                    return {
                        "status": "failed",
                        "error": f"Container exited with code {exit_code}",
                        "logs": logs[-500:]  # Last 500 chars
                    }
                else:
                    return {"status": "stopped"}
            
            else:
                return {
                    "status": status,
                    "error": f"Container in unexpected state: {status}"
                }
                
        except Exception as e:
            return {
                "status": "error",
                "error": f"Failed to check service health: {str(e)}"
            }

    def log_error(self, service_name: str, error_type: str, message: str, severity: str = "ERROR", **kwargs):
        """Log an error to the structured log file"""
        timestamp = datetime.now(timezone.utc).isoformat()
        
        error_entry = {
            "timestamp": timestamp,
            "service": service_name,
            "error_type": error_type,
            "message": message,
            "severity": severity,
            **kwargs
        }
        
        # Create unique error ID for deduplication
        error_id = f"{service_name}:{error_type}:{hash(message) % 10000}"
        
        # Write to log file
        with open(self.error_log_file, 'a') as f:
            f.write(json.dumps(error_entry) + '\n')
        
        logger.info(f"Logged {severity} for {service_name}: {message}")
        
        return error_id

    def clear_resolved_errors(self, service_name: str):
        """Remove resolved errors for a service from the log file"""
        if not self.error_log_file.exists():
            return
        
        # Read existing errors
        existing_errors = []
        try:
            with open(self.error_log_file, 'r') as f:
                for line in f:
                    if line.strip():
                        error_entry = json.loads(line.strip())
                        # Keep errors that are not for this service
                        if error_entry.get('service') != service_name:
                            existing_errors.append(error_entry)
        except Exception as e:
            logger.error(f"Error reading existing errors: {e}")
            return
        
        # Rewrite file without resolved errors
        try:
            with open(self.error_log_file, 'w') as f:
                for error_entry in existing_errors:
                    f.write(json.dumps(error_entry) + '\n')
            
            logger.info(f"Cleared resolved errors for service: {service_name}")
        except Exception as e:
            logger.error(f"Error clearing resolved errors: {e}")

    def update_service_status(self, services_status: Dict):
        """Update the service status file"""
        status_data = {
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "services": services_status,
            "healthy_count": sum(1 for s in services_status.values() if s.get("status") == "healthy"),
            "total_count": len(services_status)
        }
        
        try:
            with open(self.status_file, 'w') as f:
                json.dump(status_data, f, indent=2)
        except Exception as e:
            logger.error(f"Error updating status file: {e}")

    def monitor_services(self):
        """Main monitoring loop"""
        logger.info("Starting Docker Compose service monitoring...")
        
        while True:
            try:
                services = self.get_compose_services()
                if not services:
                    logger.warning("No services found to monitor")
                    time.sleep(CHECK_INTERVAL)
                    continue
                
                services_status = {}
                current_healthy = set()
                
                for service_name in services:
                    health_info = self.check_service_health(service_name)
                    services_status[service_name] = health_info
                    
                    status = health_info.get("status")
                    
                    if status == "healthy":
                        current_healthy.add(service_name)
                        # Clear any previous errors for this service
                        if service_name not in self.healthy_services:
                            self.clear_resolved_errors(service_name)
                            logger.info(f"Service {service_name} is now healthy")
                    
                    elif status in ["failed", "unhealthy", "error", "missing"]:
                        error_message = health_info.get("error", f"Service status: {status}")
                        
                        # Determine severity
                        severity = "CRITICAL" if status in ["failed", "missing"] else "ERROR"
                        
                        # Log the error
                        self.log_error(
                            service_name=service_name,
                            error_type=status,
                            message=error_message,
                            severity=severity,
                            container_status=status,
                            logs=health_info.get("logs", "")[:200]  # Truncate logs
                        )
                
                # Update tracking sets
                self.healthy_services = current_healthy
                
                # Update status file
                self.update_service_status(services_status)
                
                # Log summary
                healthy_count = len(current_healthy)
                total_count = len(services)
                logger.info(f"Health check complete: {healthy_count}/{total_count} services healthy")
                
            except Exception as e:
                logger.error(f"Error in monitoring loop: {e}")
            
            time.sleep(CHECK_INTERVAL)

class HealthHandler(BaseHTTPRequestHandler):
    def do_GET(self):
        if self.path == '/health':
            self.send_response(200)
            self.send_header('Content-type', 'application/json')
            self.end_headers()
            self.wfile.write(b'{"status": "healthy"}')
        else:
            self.send_response(404)
            self.end_headers()

def start_health_server():
    """Start health check HTTP server"""
    server = HTTPServer(('0.0.0.0', 8080), HealthHandler)
    server.serve_forever()

def main():
    """Main entry point"""
    logger.info("Starting Docker Compose Error Monitor")
    
    # Start health check server in background
    health_thread = threading.Thread(target=start_health_server, daemon=True)
    health_thread.start()
    
    # Start monitoring
    monitor = DockerMonitor()
    monitor.monitor_services()

if __name__ == "__main__":
    main()
