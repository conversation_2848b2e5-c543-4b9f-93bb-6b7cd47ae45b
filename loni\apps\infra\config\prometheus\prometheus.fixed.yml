# Prometheus configuration for LONI platform monitoring (Fixed)

global:
  scrape_interval: 15s
  evaluation_interval: 15s
  scrape_timeout: 10s

rule_files:
  - "alert_rules.yml"

# Removed alertmanager configuration as it's not included in this setup
# alerting:
#   alertmanagers:
#     - static_configs:
#         - targets:
#           - alertmanager:9093

scrape_configs:
  # Prometheus self-monitoring
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']
    metrics_path: /metrics
    scrape_interval: 30s

  # System metrics from node-exporter
  - job_name: 'node-exporter'
    static_configs:
      - targets: ['node-exporter:9100']
    scrape_interval: 30s

  # Container metrics from cAdvisor
  - job_name: 'cadvisor'
    static_configs:
      - targets: ['cadvisor:8080']
    scrape_interval: 30s

  # PostgreSQL metrics from postgres-exporter
  - job_name: 'postgres'
    static_configs:
      - targets: ['postgres-exporter:9187']
    scrape_interval: 30s

  # Redis metrics from redis-exporter
  - job_name: 'redis'
    static_configs:
      - targets: ['redis-exporter:9121']
    scrape_interval: 30s

  # Qdrant vector database metrics (if available)
  - job_name: 'qdrant'
    static_configs:
      - targets: ['qdrant:6333']
    metrics_path: /metrics
    scrape_interval: 30s
    # Continue on error if metrics endpoint doesn't exist
    honor_labels: true
    scrape_timeout: 5s

  # Grafana metrics
  - job_name: 'grafana'
    static_configs:
      - targets: ['grafana:3000']
    metrics_path: /metrics
    scrape_interval: 60s

  # Nginx metrics (basic)
  - job_name: 'nginx'
    static_configs:
      - targets: ['nginx:80']
    metrics_path: /nginx_status
    scrape_interval: 30s

  # LONI Backend API metrics (when available)
  - job_name: 'loni-backend'
    static_configs:
      - targets: ['host.docker.internal:8000']
    metrics_path: /metrics
    scrape_interval: 15s
    scrape_timeout: 10s
    # Make this optional
    honor_labels: true

  # LONI Frontend metrics (when available)
  - job_name: 'loni-frontend'
    static_configs:
      - targets: ['host.docker.internal:3000']
    metrics_path: /api/metrics
    scrape_interval: 30s
    # Make this optional
    honor_labels: true

  # Ollama metrics (if available)
  - job_name: 'ollama'
    static_configs:
      - targets: ['ollama:11434']
    metrics_path: /metrics
    scrape_interval: 60s
    # Make this optional as Ollama may not expose Prometheus metrics
    honor_labels: true
    scrape_timeout: 5s

  # n8n metrics (if available)
  - job_name: 'n8n'
    static_configs:
      - targets: ['n8n:5678']
    metrics_path: /metrics
    scrape_interval: 60s
    # Make this optional
    honor_labels: true

  # Neo4j metrics (if available)
  - job_name: 'neo4j'
    static_configs:
      - targets: ['neo4j:2004']
    metrics_path: /metrics
    scrape_interval: 30s
    # Make this optional
    honor_labels: true

# Storage configuration
storage:
  tsdb:
    retention.time: 15d
    retention.size: 10GB