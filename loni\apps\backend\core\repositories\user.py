"""
User repository implementation.
"""
from typing import Optional
from uuid import UUI<PERSON>

from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from ..models.user import User
from .base import BaseRepository


class UserRepository(BaseRepository[User]):
    """Repository for User model operations."""
    
    def __init__(self, session: AsyncSession):
        super().__init__(User, session)
    
    async def get_by_email(self, email: str) -> Optional[User]:
        """Get user by email address."""
        query = select(self.model).where(self.model.email == email)
        result = await self.session.execute(query)
        return result.scalar_one_or_none()
    
    async def get_by_username(self, username: str) -> Optional[User]:
        """Get user by username."""
        query = select(self.model).where(self.model.name == username)
        result = await self.session.execute(query)
        return result.scalar_one_or_none()
    
    async def update_quota_usage(self, user_id: UUID, tokens_used: int) -> User:
        """Update user's API quota usage."""
        user = await self.get_by_id(user_id)
        if not user:
            raise ValueError(f"User with id {user_id} not found")
        
        user.api_quota_used += tokens_used
        await self.session.commit()
        await self.session.refresh(user)
        return user
    
    async def reset_quota_usage(self, user_id: UUID) -> User:
        """Reset user's API quota usage to zero."""
        user = await self.get_by_id(user_id)
        if not user:
            raise ValueError(f"User with id {user_id} not found")
        
        user.api_quota_used = 0
        await self.session.commit()
        await self.session.refresh(user)
        return user
    
    async def check_quota_available(self, user_id: UUID, tokens_needed: int) -> bool:
        """Check if user has sufficient quota for the operation."""
        user = await self.get_by_id(user_id)
        if not user:
            return False
        
        return user.api_quota_used + tokens_needed <= user.api_quota_limit