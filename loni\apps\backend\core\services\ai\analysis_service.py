"""
Analysis service for AI-powered content analysis.

This service handles conversation analysis, sentiment analysis,
and content insights following the Single Responsibility Principle.
"""

from typing import Dict, List, Any, Optional
from uuid import UUID
from datetime import datetime

from loguru import logger

from ...repositories.conversation import ConversationRepository, MessageRepository


class AnalysisService:
    """Service for AI-powered analysis and insights."""
    
    def __init__(
        self, 
        conversation_repository: ConversationRepository,
        message_repository: MessageRepository
    ):
        """
        Initialize the analysis service.
        
        Args:
            conversation_repository: Repository for conversation operations
            message_repository: Repository for message operations
        """
        self.conversation_repo = conversation_repository
        self.message_repo = message_repository
    
    async def analyze_conversation(self, conversation_id: UUID) -> Dict[str, Any]:
        """
        Perform comprehensive analysis of a conversation.
        
        Args:
            conversation_id: ID of the conversation to analyze
            
        Returns:
            Analysis results including stats and insights
        """
        try:
            # Get conversation and messages
            conversation = await self.conversation_repo.get_with_messages(conversation_id)
            if not conversation:
                raise ValueError(f"Conversation {conversation_id} not found")
            
            messages = conversation.messages
            
            # Basic statistics
            stats = self._calculate_basic_stats(messages)
            
            # Content analysis
            content_analysis = await self._analyze_content(messages)
            
            # Interaction patterns
            interaction_patterns = self._analyze_interaction_patterns(messages)
            
            # Generate insights
            insights = await self._generate_insights(messages, stats)
            
            return {
                'conversation_id': str(conversation_id),
                'analyzed_at': datetime.utcnow().isoformat(),
                'stats': stats,
                'content_analysis': content_analysis,
                'interaction_patterns': interaction_patterns,
                'insights': insights
            }
            
        except Exception as e:
            logger.error(f"Conversation analysis failed for {conversation_id}: {e}")
            raise
    
    def _calculate_basic_stats(self, messages: List[Any]) -> Dict[str, Any]:
        """
        Calculate basic conversation statistics.
        
        Args:
            messages: List of messages
            
        Returns:
            Basic statistics
        """
        if not messages:
            return {
                'total_messages': 0,
                'user_messages': 0,
                'assistant_messages': 0,
                'avg_message_length': 0,
                'total_characters': 0,
                'conversation_duration': 0
            }
        
        user_messages = [msg for msg in messages if msg.role == 'user']
        assistant_messages = [msg for msg in messages if msg.role == 'assistant']
        
        total_chars = sum(len(msg.content) for msg in messages)
        avg_length = total_chars / len(messages) if messages else 0
        
        # Calculate duration
        first_message = min(messages, key=lambda m: m.created_at)
        last_message = max(messages, key=lambda m: m.created_at)
        duration = (last_message.created_at - first_message.created_at).total_seconds()
        
        return {
            'total_messages': len(messages),
            'user_messages': len(user_messages),
            'assistant_messages': len(assistant_messages),
            'avg_message_length': round(avg_length, 2),
            'total_characters': total_chars,
            'conversation_duration_seconds': round(duration, 2),
            'messages_per_minute': round(len(messages) / (duration / 60), 2) if duration > 0 else 0
        }
    
    async def _analyze_content(self, messages: List[Any]) -> Dict[str, Any]:
        """
        Analyze content patterns and topics.
        
        Args:
            messages: List of messages
            
        Returns:
            Content analysis results
        """
        user_messages = [msg.content for msg in messages if msg.role == 'user']
        assistant_messages = [msg.content for msg in messages if msg.role == 'assistant']
        
        # Topic extraction (simplified - could use more sophisticated NLP)
        topics = self._extract_topics(user_messages)
        
        # Sentiment analysis (simplified)
        sentiment = self._analyze_sentiment(user_messages)
        
        # Question types
        question_types = self._classify_question_types(user_messages)
        
        return {
            'topics': topics,
            'sentiment': sentiment,
            'question_types': question_types,
            'language_complexity': self._analyze_language_complexity(user_messages),
            'response_patterns': self._analyze_response_patterns(assistant_messages)
        }
    
    def _extract_topics(self, messages: List[str]) -> List[Dict[str, Any]]:
        """
        Extract topics from messages (simplified implementation).
        
        Args:
            messages: List of message contents
            
        Returns:
            List of topics with frequencies
        """
        if not messages:
            return []
        
        # Simple keyword extraction
        combined_text = " ".join(messages).lower()
        words = combined_text.split()
        
        # Filter out common words and short words
        stop_words = {
            'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for',
            'of', 'with', 'by', 'is', 'are', 'was', 'were', 'be', 'been', 'being',
            'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could',
            'should', 'may', 'might', 'can', 'this', 'that', 'these', 'those'
        }
        
        word_freq = {}
        for word in words:
            clean_word = ''.join(c for c in word if c.isalnum())
            if len(clean_word) > 3 and clean_word not in stop_words:
                word_freq[clean_word] = word_freq.get(clean_word, 0) + 1
        
        # Get top topics
        sorted_words = sorted(word_freq.items(), key=lambda x: x[1], reverse=True)[:10]
        
        return [
            {'topic': word, 'frequency': freq, 'relevance': freq / len(words)}
            for word, freq in sorted_words
        ]
    
    def _analyze_sentiment(self, messages: List[str]) -> Dict[str, Any]:
        """
        Analyze sentiment of messages (simplified implementation).
        
        Args:
            messages: List of message contents
            
        Returns:
            Sentiment analysis results
        """
        if not messages:
            return {'overall': 'neutral', 'confidence': 0.0, 'distribution': {}}
        
        # Simple sentiment keywords
        positive_words = {
            'good', 'great', 'excellent', 'amazing', 'wonderful', 'fantastic',
            'love', 'like', 'enjoy', 'happy', 'pleased', 'satisfied', 'thank'
        }
        negative_words = {
            'bad', 'terrible', 'awful', 'horrible', 'hate', 'dislike', 'angry',
            'frustrated', 'disappointed', 'problem', 'issue', 'error', 'wrong'
        }
        
        sentiment_scores = []
        for message in messages:
            words = message.lower().split()
            positive_count = sum(1 for word in words if word in positive_words)
            negative_count = sum(1 for word in words if word in negative_words)
            
            if positive_count > negative_count:
                sentiment_scores.append('positive')
            elif negative_count > positive_count:
                sentiment_scores.append('negative')
            else:
                sentiment_scores.append('neutral')
        
        # Calculate distribution
        total = len(sentiment_scores)
        distribution = {
            'positive': sentiment_scores.count('positive') / total,
            'negative': sentiment_scores.count('negative') / total,
            'neutral': sentiment_scores.count('neutral') / total
        }
        
        # Overall sentiment
        if distribution['positive'] > 0.6:
            overall = 'positive'
        elif distribution['negative'] > 0.6:
            overall = 'negative'
        else:
            overall = 'neutral'
        
        return {
            'overall': overall,
            'confidence': max(distribution.values()),
            'distribution': distribution
        }
    
    def _classify_question_types(self, messages: List[str]) -> Dict[str, int]:
        """
        Classify types of questions asked.
        
        Args:
            messages: List of message contents
            
        Returns:
            Question type classification
        """
        question_types = {
            'what': 0, 'how': 0, 'why': 0, 'when': 0, 'where': 0, 'who': 0,
            'can': 0, 'could': 0, 'would': 0, 'should': 0, 'is': 0, 'are': 0,
            'explain': 0, 'help': 0, 'show': 0, 'tell': 0
        }
        
        for message in messages:
            message_lower = message.lower()
            for q_type in question_types:
                if message_lower.startswith(q_type) or f" {q_type} " in message_lower:
                    question_types[q_type] += 1
        
        return {k: v for k, v in question_types.items() if v > 0}
    
    def _analyze_language_complexity(self, messages: List[str]) -> Dict[str, Any]:
        """
        Analyze language complexity of messages.
        
        Args:
            messages: List of message contents
            
        Returns:
            Language complexity metrics
        """
        if not messages:
            return {'avg_words_per_sentence': 0, 'avg_chars_per_word': 0, 'complexity': 'low'}
        
        total_words = 0
        total_sentences = 0
        total_chars = 0
        
        for message in messages:
            sentences = message.split('.')
            total_sentences += len([s for s in sentences if s.strip()])
            
            words = message.split()
            total_words += len(words)
            total_chars += sum(len(word) for word in words)
        
        avg_words_per_sentence = total_words / total_sentences if total_sentences > 0 else 0
        avg_chars_per_word = total_chars / total_words if total_words > 0 else 0
        
        # Simple complexity classification
        if avg_words_per_sentence > 15 and avg_chars_per_word > 6:
            complexity = 'high'
        elif avg_words_per_sentence > 10 and avg_chars_per_word > 5:
            complexity = 'medium'
        else:
            complexity = 'low'
        
        return {
            'avg_words_per_sentence': round(avg_words_per_sentence, 2),
            'avg_chars_per_word': round(avg_chars_per_word, 2),
            'complexity': complexity
        }
    
    def _analyze_response_patterns(self, messages: List[str]) -> Dict[str, Any]:
        """
        Analyze AI response patterns.
        
        Args:
            messages: List of assistant message contents
            
        Returns:
            Response pattern analysis
        """
        if not messages:
            return {'avg_length': 0, 'response_style': 'unknown'}
        
        lengths = [len(msg) for msg in messages]
        avg_length = sum(lengths) / len(lengths)
        
        # Analyze response style based on length and content
        if avg_length > 1000:
            style = 'detailed'
        elif avg_length > 500:
            style = 'comprehensive'
        elif avg_length > 200:
            style = 'concise'
        else:
            style = 'brief'
        
        return {
            'avg_length': round(avg_length, 2),
            'response_style': style,
            'length_variation': round(max(lengths) - min(lengths), 2) if lengths else 0
        }
    
    def _analyze_interaction_patterns(self, messages: List[Any]) -> Dict[str, Any]:
        """
        Analyze interaction patterns between user and assistant.
        
        Args:
            messages: List of messages
            
        Returns:
            Interaction pattern analysis
        """
        if len(messages) < 2:
            return {'turn_taking': 'insufficient_data'}
        
        # Analyze turn-taking patterns
        roles = [msg.role for msg in messages]
        proper_turns = 0
        
        for i in range(1, len(roles)):
            if roles[i] != roles[i-1]:
                proper_turns += 1
        
        turn_ratio = proper_turns / (len(roles) - 1) if len(roles) > 1 else 0
        
        # Analyze response times (if timestamps available)
        response_times = []
        for i in range(1, len(messages)):
            if messages[i].role == 'assistant' and messages[i-1].role == 'user':
                time_diff = (messages[i].created_at - messages[i-1].created_at).total_seconds()
                response_times.append(time_diff)
        
        avg_response_time = sum(response_times) / len(response_times) if response_times else 0
        
        return {
            'turn_taking_ratio': round(turn_ratio, 2),
            'avg_response_time_seconds': round(avg_response_time, 2),
            'interaction_quality': 'good' if turn_ratio > 0.8 else 'needs_improvement'
        }
    
    async def _generate_insights(
        self, 
        messages: List[Any], 
        stats: Dict[str, Any]
    ) -> List[str]:
        """
        Generate insights based on analysis.
        
        Args:
            messages: List of messages
            stats: Basic statistics
            
        Returns:
            List of insights
        """
        insights = []
        
        # Message count insights
        if stats['total_messages'] > 50:
            insights.append("This is a highly engaged conversation with extensive interaction.")
        elif stats['total_messages'] > 20:
            insights.append("This conversation shows good engagement levels.")
        elif stats['total_messages'] < 5:
            insights.append("This is a brief conversation that might benefit from more interaction.")
        
        # Balance insights
        user_ratio = stats['user_messages'] / stats['total_messages'] if stats['total_messages'] > 0 else 0
        if user_ratio > 0.6:
            insights.append("User is highly engaged and asking many questions.")
        elif user_ratio < 0.3:
            insights.append("Assistant is providing detailed responses with less user input.")
        
        # Message length insights
        if stats['avg_message_length'] > 500:
            insights.append("Messages tend to be detailed and comprehensive.")
        elif stats['avg_message_length'] < 50:
            insights.append("Communication style is brief and to-the-point.")
        
        return insights