# =============================================================================
# RAG PIPELINE CONFIGURATION
# =============================================================================

# Determines how environment variables are loaded
# Set this to either production or development
ENVIRONMENT=

# Controls which pipeline to run:
# - "local": Watch local files (default)
# - "google_drive": Watch Google Drive files
RAG_PIPELINE_TYPE=local

# Controls how the RAG pipeline runs:
# - "continuous": Runs continuously, checking for changes at regular intervals (default)
# - "single": Performs one check for changes and exits (for cron jobs, cloud schedulers)
RUN_MODE=continuous

# Unique identifier for this pipeline instance (required for single-run mode)
# Used for database state management to track last_check_time and known_files
# Examples: "prod-drive-pipeline", "dev-local-pipeline", "staging-pipeline"
RAG_PIPELINE_ID=

# =============================================================================
# EMBEDDING MODEL CONFIGURATION
# =============================================================================

# Base URL for the OpenAI compatible instance that has embedding models (default is https://api.openai.com/v1)
# OpenAI: https://api.openai.com/v1
# Ollama (example): http://localhost:11434/v1
EMBEDDING_BASE_URL=

# OpenAI: https://help.openai.com/en/articles/4936850-where-do-i-find-my-openai-api-key
# Ollama: Just set this to a placeholder like ollama unless you specifically configured an API key
EMBEDDING_API_KEY=

# The embedding model you want to use for RAG.
# Make sure the embeddings column in your database has the same dimensions as this embedding model!
# OpenAI example: text-embedding-3-small
# Ollama example: nomic-embed-text
EMBEDDING_MODEL_CHOICE=

# =============================================================================
# SUPABASE DATABASE CONFIGURATION
# =============================================================================

# Supabase configuration
# Get these from your Supabase project settings -> API
# https://supabase.com/dashboard/project/<your project ID>/settings/api
# For the Local AI Package:
#   The Supabase URL is http://kong:8000 (when running in Docker) or http://localhost:8000 (pipeline is outside of Docker)
#   The Service Key you get from your package .env
SUPABASE_URL=
SUPABASE_SERVICE_KEY=

# =============================================================================
# GOOGLE DRIVE CONFIGURATION
# =============================================================================

# Google Drive service account credentials (JSON format as string)
# Required for serverless/cloud deployment without interactive OAuth2
# Get this from Google Cloud Console -> Service Accounts -> Create Key (JSON)
# Example: GOOGLE_DRIVE_CREDENTIALS_JSON='{"type":"service_account","project_id":"your-project",...}'
GOOGLE_DRIVE_CREDENTIALS_JSON=

# Override the Google Drive folder ID to watch (optional)
# If not set, uses the folder_id from config.json or watches entire Drive
# Get folder ID from Google Drive URL: https://drive.google.com/drive/folders/FOLDER_ID_HERE
RAG_WATCH_FOLDER_ID=

# =============================================================================
# LOCAL FILES CONFIGURATION
# =============================================================================

# Override the local directory to watch for files (optional)
# If not set, uses the watch_directory from config.json (relative to script location)
# Examples: "/app/data", "/mnt/shared/documents", "./local_files"
RAG_WATCH_DIRECTORY=


# =============================================================================
# DEPLOYMENT EXAMPLES
# =============================================================================

# Example 1: Local Development (Continuous Mode)
# RAG_PIPELINE_TYPE=local
# RUN_MODE=continuous
# RAG_PIPELINE_ID=dev-local-pipeline
# EMBEDDING_BASE_URL=http://localhost:11434/v1
# EMBEDDING_API_KEY=ollama
# EMBEDDING_MODEL_CHOICE=nomic-embed-text
# SUPABASE_URL=http://localhost:8000
# SUPABASE_SERVICE_KEY=your-local-service-key
# RAG_WATCH_DIRECTORY=./data

# Example 2: Google Drive with Cloud Run (Single-Run Mode)
# RAG_PIPELINE_TYPE=google_drive
# RUN_MODE=single
# RAG_PIPELINE_ID=prod-drive-pipeline
# EMBEDDING_BASE_URL=https://api.openai.com/v1
# EMBEDDING_API_KEY=sk-your-openai-key
# EMBEDDING_MODEL_CHOICE=text-embedding-3-small
# SUPABASE_URL=https://your-project.supabase.co
# SUPABASE_SERVICE_KEY=your-service-key
# GOOGLE_DRIVE_CREDENTIALS_JSON='{"type":"service_account","project_id":"your-project",...}'
# RAG_WATCH_FOLDER_ID=1A2B3C4D5E6F7G8H9I0J

# Example 3: AWS Lambda with Local Files (Single-Run Mode)
# RAG_PIPELINE_TYPE=local
# RUN_MODE=single
# RAG_PIPELINE_ID=prod-local-pipeline
# EMBEDDING_BASE_URL=https://api.openai.com/v1
# EMBEDDING_API_KEY=sk-your-openai-key
# EMBEDDING_MODEL_CHOICE=text-embedding-3-small
# SUPABASE_URL=https://your-project.supabase.co
# SUPABASE_SERVICE_KEY=your-service-key
# RAG_WATCH_DIRECTORY=/mnt/efs/documents

# Example 4: Render with Google Drive (Continuous Mode)
# RAG_PIPELINE_TYPE=google_drive
# RUN_MODE=continuous
# RAG_PIPELINE_ID=render-drive-pipeline
# EMBEDDING_BASE_URL=https://api.openai.com/v1
# EMBEDDING_API_KEY=sk-your-openai-key
# EMBEDDING_MODEL_CHOICE=text-embedding-3-small
# SUPABASE_URL=https://your-project.supabase.co
# SUPABASE_SERVICE_KEY=your-service-key
# GOOGLE_DRIVE_CREDENTIALS_JSON='{"type":"service_account","project_id":"your-project",...}'

# =============================================================================
# USAGE INSTRUCTIONS
# =============================================================================

# 1. CONTINUOUS MODE (Default):
#    - Set RAG_PIPELINE_TYPE=local or google_drive
#    - Set RUN_MODE=continuous (or leave unset)
#    - Pipeline runs indefinitely, checking for changes every 60 seconds
#    - Suitable for: Docker containers, VMs, Render, Railway
#    - Command: python docker_entrypoint.py

# 2. SINGLE-RUN MODE (Cron/Scheduler):
#    - Set RAG_PIPELINE_TYPE=local or google_drive
#    - Set RUN_MODE=single
#    - Set RAG_PIPELINE_ID to unique identifier
#    - Pipeline runs once and exits with status code:
#      * 0: Success (changes processed or no changes found)
#      * 1: Retry needed (temporary errors)
#      * 2: Configuration error (don't retry)
#      * 3: Authentication error (don't retry)
#    - Suitable for: Cron jobs, GCP Cloud Scheduler, AWS EventBridge
#    - Command: python docker_entrypoint.py

# 3. DATABASE STATE MANAGEMENT:
#    - When RAG_PIPELINE_ID is set, state is stored in Supabase
#    - Tracks last_check_time and known_files in JSONB format
#    - Enables proper change detection across multiple runs
#    - Falls back to config.json files when RAG_PIPELINE_ID is unset

# 4. AUTHENTICATION METHODS:
#    - Google Drive: Service account (GOOGLE_DRIVE_CREDENTIALS_JSON) or OAuth2 (credentials.json)
#    - Service account is required for serverless/cloud deployment
#    - OAuth2 is for local development with interactive authentication

# =============================================================================
# TROUBLESHOOTING
# =============================================================================

# Common Issues:
# 1. "SUPABASE_URL and SUPABASE_SERVICE_KEY environment variables are required"
#    - Ensure both Supabase variables are set correctly
#    - Check for typos in variable names
#    - Verify service key has necessary permissions

# 2. "Error parsing service account credentials"
#    - GOOGLE_DRIVE_CREDENTIALS_JSON must be valid JSON string
#    - Ensure proper escaping of quotes in deployment environment
#    - Test JSON validity before deployment

# 3. "Google Drive credentials file not found"
#    - Either set GOOGLE_DRIVE_CREDENTIALS_JSON or provide credentials.json file
#    - Service account authentication is recommended for production

# 4. "Failed to save state to database"
#    - Check Supabase connection and permissions
#    - Ensure rag_pipeline_state table exists (run sql/9-rag_pipeline_state.sql)
#    - Verify service key has INSERT/UPDATE permissions

# 5. Exit code 1 in single-run mode
#    - Indicates temporary error, safe to retry
#    - Check logs for specific error details
#    - Common causes: network issues, rate limits

# 6. Exit code 2 in single-run mode
#    - Configuration error, don't retry without fixing
#    - Check environment variables and config files
#    - Verify database schema and permissions

# 7. Exit code 3 in single-run mode
#    - Authentication error, don't retry without fixing
#    - Check Google Drive credentials or Supabase access
#    - Verify service account permissions