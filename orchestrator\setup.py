"""
Setup configuration for Project Template Orchestrator.

Provides installation and distribution configuration for the orchestrator package.
"""

from setuptools import setup, find_packages
from pathlib import Path

# Read README for long description
readme_file = Path(__file__).parent / "README.md"
long_description = readme_file.read_text(encoding="utf-8") if readme_file.exists() else ""

# Read requirements
requirements_file = Path(__file__).parent / "requirements.txt"
requirements = []
if requirements_file.exists():
    requirements = [
        line.strip() 
        for line in requirements_file.read_text(encoding="utf-8").splitlines()
        if line.strip() and not line.startswith("#")
    ]

setup(
    name="project-template-orchestrator",
    version="1.0.0",
    description="Intelligent project generation with template dependency management",
    long_description=long_description,
    long_description_content_type="text/markdown",
    author="Lonors Project",
    author_email="<EMAIL>",
    url="https://github.com/lonors/orchestrator",
    packages=find_packages(),
    include_package_data=True,
    install_requires=requirements,
    extras_require={
        "dev": [
            "pytest>=7.4.0",
            "pytest-asyncio>=0.21.0",
            "pytest-mock>=3.12.0",
            "pytest-cov>=4.1.0",
            "black>=23.0.0",
            "flake8>=6.0.0",
            "mypy>=1.5.0",
        ],
        "web": [
            "fastapi>=0.104.0",
            "uvicorn>=0.24.0",
            "jinja2>=3.1.2",
        ]
    },
    entry_points={
        "console_scripts": [
            "orchestrator=ui.cli:main",
            "template-orchestrator=ui.cli:main",
        ]
    },
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: Developers",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Programming Language :: Python :: 3.12",
        "Topic :: Software Development :: Code Generators",
        "Topic :: Software Development :: Build Tools",
        "Topic :: System :: Installation/Setup",
    ],
    python_requires=">=3.8",
    keywords="project-templates docker-compose code-generation development-tools",
    project_urls={
        "Bug Reports": "https://github.com/lonors/orchestrator/issues",
        "Source": "https://github.com/lonors/orchestrator",
        "Documentation": "https://github.com/lonors/orchestrator/docs",
    },
)