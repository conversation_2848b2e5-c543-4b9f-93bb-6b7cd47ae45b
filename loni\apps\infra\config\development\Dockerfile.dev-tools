# Development Tools Logger Dockerfile
# Runs TypeScript, E<PERSON>int, Prettier, and other dev tools with error-only logging

FROM node:18-alpine AS base

# Install system dependencies
RUN apk add --no-cache \
    bash \
    curl \
    git \
    python3 \
    py3-pip \
    jq

# Install global development tools
RUN npm install -g \
    typescript@latest \
    eslint@latest \
    prettier@latest \
    @typescript-eslint/parser@latest \
    @typescript-eslint/eslint-plugin@latest \
    jest@latest

# Install Python development tools
RUN pip3 install \
    black \
    flake8 \
    mypy \
    pylint

# Create working directory
WORKDIR /app

# Create log directories
RUN mkdir -p /var/log/loni/development/{typescript,eslint,prettier,jest,python,docker}

# Copy logging scripts
COPY scripts/ /app/scripts/
RUN chmod +x /app/scripts/*.sh

# Create non-root user
RUN addgroup -g 1001 -S devtools && \
    adduser -S devtools -u 1001 -G devtools

# Set ownership
RUN chown -R devtools:devtools /app /var/log/loni/development

# Switch to non-root user
USER devtools

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:3001/health || exit 1

# Default command
CMD ["/app/scripts/dev-tools-runner.sh"]
