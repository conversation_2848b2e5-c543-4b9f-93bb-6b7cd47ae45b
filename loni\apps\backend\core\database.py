"""
Database connection and session management.

This module provides database connection management using async SQLAlchemy
following the Single Responsibility Principle.
"""

import asyncio
from typing import AsyncGenerator

from sqlalchemy import event, pool
from sqlalchemy.ext.asyncio import AsyncSession, async_sessionmaker, create_async_engine
from sqlalchemy.orm import sessionmaker
from loguru import logger

from .config import get_settings
from .models.base import BaseModel


class DatabaseManager:
    """
    Database connection manager for async SQLAlchemy.
    
    Handles database initialization, connection pooling, and session management.
    """
    
    def __init__(self):
        """Initialize database manager."""
        self.settings = get_settings()
        self.engine = None
        self.async_session_maker = None
        self._initialized = False
    
    async def initialize(self) -> None:
        """Initialize database engine and session maker."""
        if self._initialized:
            return
        
        logger.info("Initializing database connection...")
        
        # Create async engine with connection pooling
        self.engine = create_async_engine(
            self.settings.database.url,
            echo=self.settings.debug,
            pool_size=self.settings.database.pool_size,
            max_overflow=self.settings.database.max_overflow,
            pool_timeout=self.settings.database.pool_timeout,
            pool_recycle=self.settings.database.pool_recycle,
            pool_pre_ping=True,  # Validate connections before use
        )
        
        # Create session maker
        self.async_session_maker = async_sessionmaker(
            bind=self.engine,
            class_=AsyncSession,
            expire_on_commit=False,
        )
        
        # Set up event listeners
        self._setup_event_listeners()
        
        # Test connection
        await self._test_connection()
        
        self._initialized = True
        logger.info("Database initialized successfully")
    
    async def create_tables(self) -> None:
        """Create all database tables."""
        if not self.engine:
            raise RuntimeError("Database not initialized")
        
        logger.info("Creating database tables...")
        
        async with self.engine.begin() as conn:
            await conn.run_sync(BaseModel.metadata.create_all)
        
        logger.info("Database tables created successfully")
    
    async def drop_tables(self) -> None:
        """Drop all database tables."""
        if not self.engine:
            raise RuntimeError("Database not initialized")
        
        logger.warning("Dropping all database tables...")
        
        async with self.engine.begin() as conn:
            await conn.run_sync(BaseModel.metadata.drop_all)
        
        logger.info("Database tables dropped")
    
    async def get_session(self) -> AsyncGenerator[AsyncSession, None]:
        """
        Get async database session.
        
        Yields:
            AsyncSession: Database session
        """
        if not self.async_session_maker:
            raise RuntimeError("Database not initialized")
        
        async with self.async_session_maker() as session:
            try:
                yield session
            except Exception:
                await session.rollback()
                raise
            finally:
                await session.close()
    
    async def cleanup(self) -> None:
        """Clean up database connections."""
        if self.engine:
            logger.info("Closing database connections...")
            await self.engine.dispose()
            logger.info("Database connections closed")
    
    def _setup_event_listeners(self) -> None:
        """Set up SQLAlchemy event listeners."""
        if not self.engine:
            return
        
        @event.listens_for(self.engine.sync_engine, "connect")
        def set_sqlite_pragma(dbapi_connection, connection_record):
            """Set SQLite pragmas for performance."""
            # This is for PostgreSQL, but kept for potential SQLite usage
            pass
        
        @event.listens_for(self.engine.sync_engine, "checkout")
        def checkout_listener(dbapi_connection, connection_record, connection_proxy):
            """Log connection checkout."""
            logger.debug("Database connection checked out")
        
        @event.listens_for(self.engine.sync_engine, "checkin")
        def checkin_listener(dbapi_connection, connection_record):
            """Log connection checkin."""
            logger.debug("Database connection checked in")
    
    async def _test_connection(self) -> None:
        """Test database connection."""
        try:
            async with self.get_session() as session:
                await session.execute("SELECT 1")
            logger.info("Database connection test successful")
        except Exception as e:
            logger.error(f"Database connection test failed: {e}")
            raise


# Global database manager instance
database_manager = DatabaseManager()


async def get_database_session() -> AsyncGenerator[AsyncSession, None]:
    """
    Dependency for getting database session.
    
    Yields:
        AsyncSession: Database session
    """
    async with database_manager.get_session() as session:
        yield session