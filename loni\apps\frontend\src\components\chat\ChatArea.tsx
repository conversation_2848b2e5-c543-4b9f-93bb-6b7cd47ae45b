/**
 * Main chat area component
 */
'use client'

import { useEffect, useRef } from 'react'
import { ChatMessage } from './ChatMessage'
import { ChatInput } from './ChatInput'
import { Button } from '@/components/ui/button'
import { Card } from '@/components/ui/card'
import { 
  MessageSquare, 
  Sparkles, 
  FileText, 
  Code, 
  Image,
  Brain
} from 'lucide-react'
import type { Message, ModelInfo, Conversation } from '@/types'

interface ChatAreaProps {
  conversation: Conversation | null
  messages: Message[]
  isGenerating?: boolean
  streamingMessage?: string
  models?: ModelInfo[]
  onSendMessage: (message: string) => void
  onSendMessageStream?: (message: string) => void
  onRegenerateMessage?: (messageId: string) => void
  onMessageFeedback?: (messageId: string, feedback: 'up' | 'down') => void
  onModelChange?: (modelId: string) => void
  onRagToggle?: () => void
  onTemperatureChange?: (temp: number) => void
  className?: string
}

export function ChatArea({
  conversation,
  messages,
  isGenerating = false,
  streamingMessage = '',
  models = [],
  onSendMessage,
  onSendMessageStream,
  onRegenerateMessage,
  onMessageFeedback,
  onModelChange,
  onRagToggle,
  onTemperatureChange,
  className
}: ChatAreaProps) {
  const messagesEndRef = useRef<HTMLDivElement>(null)
  const chatContainerRef = useRef<HTMLDivElement>(null)

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }, [messages, streamingMessage])

  const handleSuggestedPrompt = (prompt: string) => {
    onSendMessage(prompt)
  }

  if (!conversation && messages.length === 0) {
    return (
      <div className={`flex flex-col h-full ${className}`}>
        <WelcomeScreen onSuggestedPrompt={handleSuggestedPrompt} />
        <ChatInput
          onSend={onSendMessage}
          onSendStream={onSendMessageStream}
          isGenerating={isGenerating}
          currentModel={undefined}
          models={models}
          onModelChange={onModelChange}
          ragEnabled={false}
          onRagToggle={onRagToggle}
          temperature={0.7}
          onTemperatureChange={onTemperatureChange}
        />
      </div>
    )
  }

  return (
    <div className={`flex flex-col h-full ${className}`}>
      {/* Chat Header */}
      {conversation && (
        <div className="border-b bg-background p-4">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-lg font-semibold">{conversation.title}</h2>
              <div className="flex items-center gap-2 mt-1">
                <span className="text-sm text-gray-500">
                  Model: {conversation.model_name}
                </span>
                {conversation.rag_enabled && (
                  <span className="text-xs bg-ai-secondary text-ai-secondary-foreground px-2 py-1 rounded">
                    RAG Enabled
                  </span>
                )}
                <span className="text-xs text-gray-500">
                  Temp: {conversation.temperature}
                </span>
              </div>
            </div>
            
            <div className="flex items-center gap-2">
              <Button variant="ghost" size="sm">
                <FileText className="w-4 h-4 mr-2" />
                Export
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Messages Container */}
      <div 
        ref={chatContainerRef}
        className="flex-1 overflow-y-auto p-4 space-y-4"
      >
        {messages.map((message, index) => (
          <ChatMessage
            key={message.id}
            message={message}
            isGenerating={isGenerating && index === messages.length - 1}
            streamingContent={index === messages.length - 1 ? streamingMessage : undefined}
            onRegenerate={() => onRegenerateMessage?.(message.id)}
            onFeedback={onMessageFeedback}
          />
        ))}
        
        {/* Streaming Message */}
        {isGenerating && streamingMessage && messages.length === 0 && (
          <ChatMessage
            message={{
              id: 'streaming',
              conversation_id: conversation?.id || '',
              role: 'assistant',
              content: '',
              created_at: new Date().toISOString()
            }}
            isGenerating={true}
            streamingContent={streamingMessage}
          />
        )}
        
        <div ref={messagesEndRef} />
      </div>

      {/* Chat Input */}
      <ChatInput
        onSend={onSendMessage}
        onSendStream={onSendMessageStream}
        isGenerating={isGenerating}
        currentModel={conversation?.model_name}
        models={models}
        onModelChange={onModelChange}
        ragEnabled={conversation?.rag_enabled}
        onRagToggle={onRagToggle}
        temperature={conversation?.temperature}
        onTemperatureChange={onTemperatureChange}
      />
    </div>
  )
}

interface WelcomeScreenProps {
  onSuggestedPrompt: (prompt: string) => void
}

function WelcomeScreen({ onSuggestedPrompt }: WelcomeScreenProps) {
  const suggestedPrompts = [
    {
      icon: <MessageSquare className="w-5 h-5" />,
      title: "General Chat",
      description: "Ask me anything you'd like to discuss",
      prompt: "Hello! What can you help me with today?"
    },
    {
      icon: <Code className="w-5 h-5" />,
      title: "Code Help",
      description: "Get assistance with programming and development",
      prompt: "I need help with a coding project. Can you assist me with writing clean, efficient code?"
    },
    {
      icon: <FileText className="w-5 h-5" />,
      title: "Writing & Analysis",
      description: "Help with writing, editing, and document analysis",
      prompt: "I need help with writing and analyzing documents. Can you help me improve my writing?"
    },
    {
      icon: <Brain className="w-5 h-5" />,
      title: "Problem Solving",
      description: "Work through complex problems step by step",
      prompt: "I have a complex problem that needs a systematic approach. Can you help me break it down?"
    },
    {
      icon: <Sparkles className="w-5 h-5" />,
      title: "Creative Ideas",
      description: "Brainstorm and explore creative solutions",
      prompt: "I'm looking for creative ideas and innovative solutions. Can you help me brainstorm?"
    },
    {
      icon: <Image className="w-5 h-5" />,
      title: "Visual Content",
      description: "Analyze images and create visual descriptions",
      prompt: "Can you help me analyze and understand visual content?"
    }
  ]

  return (
    <div className="flex-1 flex flex-col items-center justify-center p-8 bg-gradient-to-br from-loni-primary/5 to-ai-primary/5">
      <div className="max-w-4xl w-full">
        {/* Header */}
        <div className="text-center mb-12">
          <div className="w-16 h-16 bg-loni-primary rounded-2xl flex items-center justify-center mx-auto mb-6">
            <Sparkles className="w-8 h-8 text-white" />
          </div>
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            Welcome to <span className="text-loni-primary">LONI</span>
          </h1>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            Your intelligent AI assistant powered by advanced language models and retrieval-augmented generation.
            Choose a starting point below or type your own message.
          </p>
        </div>

        {/* Suggested Prompts Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-8">
          {suggestedPrompts.map((suggestion, index) => (
            <Card
              key={index}
              className="p-6 cursor-pointer hover:shadow-lg transition-all duration-200 hover:scale-105 hover:border-loni-primary/30 group"
              onClick={() => onSuggestedPrompt(suggestion.prompt)}
            >
              <div className="flex items-start gap-4">
                <div className="p-2 bg-loni-primary/10 rounded-lg group-hover:bg-loni-primary/20 transition-colors">
                  {suggestion.icon}
                </div>
                <div className="flex-1">
                  <h3 className="font-semibold text-gray-900 mb-2">
                    {suggestion.title}
                  </h3>
                  <p className="text-sm text-gray-600">
                    {suggestion.description}
                  </p>
                </div>
              </div>
            </Card>
          ))}
        </div>

        {/* Features */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 text-center">
          <div className="flex flex-col items-center">
            <div className="w-12 h-12 bg-ai-primary/10 rounded-lg flex items-center justify-center mb-3">
              <Brain className="w-6 h-6 text-ai-primary" />
            </div>
            <h3 className="font-semibold mb-2">Advanced AI Models</h3>
            <p className="text-sm text-gray-600">
              Powered by state-of-the-art language models including GPT-4 and Claude
            </p>
          </div>
          
          <div className="flex flex-col items-center">
            <div className="w-12 h-12 bg-ai-secondary/10 rounded-lg flex items-center justify-center mb-3">
              <FileText className="w-6 h-6 text-ai-secondary-foreground" />
            </div>
            <h3 className="font-semibold mb-2">RAG-Enhanced</h3>
            <p className="text-sm text-gray-600">
              Retrieval-augmented generation for accurate, context-aware responses
            </p>
          </div>
          
          <div className="flex flex-col items-center">
            <div className="w-12 h-12 bg-loni-primary/10 rounded-lg flex items-center justify-center mb-3">
              <Sparkles className="w-6 h-6 text-loni-primary" />
            </div>
            <h3 className="font-semibold mb-2">Real-time Streaming</h3>
            <p className="text-sm text-gray-600">
              Get responses as they're generated with smooth streaming
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}