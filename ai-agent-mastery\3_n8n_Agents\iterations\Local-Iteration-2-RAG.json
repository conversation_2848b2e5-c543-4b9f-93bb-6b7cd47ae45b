{"name": "AI Agent Mastery Local P2", "nodes": [{"parameters": {"jsonMode": "expressionData", "jsonData": "={{ $json.data || $json.text || $json.concatenated_data }}", "options": {"metadata": {"metadataValues": [{"name": "=file_id", "value": "={{ $('Set File ID').first().json.file_id }}"}, {"name": "file_title", "value": "={{ $('Set File ID').first().json.file_title }}"}]}}}, "id": "48f9f786-5983-481d-815c-fe69f490c609", "name": "Default Data Loader", "type": "@n8n/n8n-nodes-langchain.documentDefaultDataLoader", "typeVersion": 1, "position": [680, 1100]}, {"parameters": {"content": "## Tool to Add a Google Drive File to Vector DB", "height": 867, "width": 2733, "color": 5}, "id": "dc3925aa-93a5-4334-a54e-adba96f0749f", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-1740, 540]}, {"parameters": {"operation": "text", "options": {}}, "id": "1cc18c6f-d62d-4de3-8715-c96719cca87d", "name": "Extract Document Text", "type": "n8n-nodes-base.extractFromFile", "typeVersion": 1, "position": [-120, 1120], "alwaysOutputData": true}, {"parameters": {}, "id": "6b436366-3905-4faf-ba46-8d04284f04b1", "name": "Postgres Chat Memory", "type": "@n8n/n8n-nodes-langchain.memoryPostgresChat", "typeVersion": 1, "position": [-1400, 320], "notesInFlow": false, "credentials": {"postgres": {"id": "UaTmh0frrACTMPxG", "name": "Postgres account"}}}, {"parameters": {"assignments": {"assignments": [{"id": "10646eae-ae46-4327-a4dc-9987c2d76173", "name": "file_id", "value": "={{ $json.path }}", "type": "string"}, {"id": "f4536df5-d0b1-4392-bf17-b8137fb31a44", "name": "file_type", "value": "={{ $json.path.split(/[\\\\/]/).pop().split('.').pop(); }}", "type": "string"}, {"id": "77d782de-169d-4a46-8a8e-a3831c04d90f", "name": "file_title", "value": "={{ $json.path.split(/[\\\\/]/).pop().split('.').slice(0, -1).join('.'); }}", "type": "string"}]}, "options": {}}, "id": "375ab378-282d-4c3d-9c29-30876d1f5257", "name": "Set File ID", "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [-1320, 820]}, {"parameters": {"content": "## RAG AI Agent with Cha<PERSON> Interface", "height": 605, "width": 1036}, "id": "9aa4afb1-e9d0-48cd-a651-af5395a4c568", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-1740, -80]}, {"parameters": {"options": {}}, "id": "9047bdd6-9a59-45b7-8947-c6056bc3f1bd", "name": "Respond to Webhook", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1.1, "position": [-880, 20]}, {"parameters": {"assignments": {"assignments": [{"id": "9a9a245e-f1a1-4282-bb02-a81ffe629f0f", "name": "chatInput", "value": "={{ $json?.chatInput || $json.body.chatInput }}", "type": "string"}, {"id": "b80831d8-c653-4203-8706-adedfdb98f77", "name": "sessionId", "value": "={{ $json?.sessionId || $json.body.sessionId}}", "type": "string"}]}, "options": {}}, "id": "450abfb2-62b1-4951-9e3e-78ae9fafaf5c", "name": "<PERSON>", "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [-1440, 20]}, {"parameters": {"public": true, "options": {}}, "id": "c2e13fd6-ff3b-47d3-a7c0-6b345a95b07d", "name": "When chat message received", "type": "@n8n/n8n-nodes-langchain.chatTrigger", "typeVersion": 1.1, "position": [-1700, 20], "webhookId": "243ea2e8-5f58-473c-9262-a60fe087b0d8"}, {"parameters": {"httpMethod": "POST", "path": "83ff266d-3d42-4e58-bbd5-6bb27fda7333", "responseMode": "responseNode", "options": {}}, "id": "74533700-e6f9-44aa-9fc9-eddfadfde903", "name": "Webhook", "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [-1700, 220], "webhookId": "83ff266d-3d42-4e58-bbd5-6bb27fda7333"}, {"parameters": {"operation": "pdf", "options": {}}, "id": "d6a23d47-777e-4442-a2a4-3077c3448e65", "name": "Extract PDF Text", "type": "n8n-nodes-base.extractFromFile", "typeVersion": 1, "position": [-120, 560]}, {"parameters": {"aggregate": "aggregateAllItemData", "options": {}}, "id": "9f8d0fac-95a7-4334-b4eb-a9d4df7db7ac", "name": "Aggregate", "type": "n8n-nodes-base.aggregate", "typeVersion": 1, "position": [-80, 820]}, {"parameters": {"fieldsToSummarize": {"values": [{"aggregation": "concatenate", "field": "data"}]}, "options": {}}, "id": "0ef952ab-beb0-4c76-ae01-489882f9b5d0", "name": "Summarize", "type": "n8n-nodes-base.summarize", "typeVersion": 1, "position": [120, 820]}, {"parameters": {"promptType": "define", "text": "={{ $json.chatInput }}", "options": {"systemMessage": "You are a personal assistant who helps answer questions from a corpus of documents. The documents are either text based (Txt, docs, extracted PDFs, etc.) or tabular data (CSVs or Excel documents).\n\nYou are given tools to perform RAG in the 'documents' table, look up the documents available in your knowledge base in the 'document_metadata' table, extract all the text from a given document, and query the tabular files with SQL in the 'document_rows' table.\n\nAlways start by performing RAG unless the users asks you to check a document or the question requires a SQL query for tabular data (fetching a sum, finding a max, something a RAG lookup would be unreliable for). If RAG doesn't help, then look at the documents that are available to you, find a few that you think would contain the answer, and then analyze those.\n\nAlways tell the user if you didn't find the answer. Don't make something up just to please them."}}, "id": "80ceff15-be91-4ed5-8b55-ee354f757664", "name": "RAG AI Agent", "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.6, "position": [-1220, 20]}, {"parameters": {"rules": {"values": [{"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 1}, "conditions": [{"leftValue": "={{ $('Set File ID').item.json.file_type }}", "rightValue": "pdf", "operator": {"type": "string", "operation": "equals"}}], "combinator": "and"}}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 1}, "conditions": [{"id": "2ae7faa7-a936-4621-a680-60c512163034", "leftValue": "={{ $('Set File ID').item.json.file_type }}", "rightValue": "xlsx", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 1}, "conditions": [{"id": "fc193b06-363b-4699-a97d-e5a850138b0e", "leftValue": "={{ $('Set File ID').item.json.file_type }}", "rightValue": "=csv", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 1}, "conditions": [{"id": "b69f5605-0179-4b02-9a32-e34bb085f82d", "leftValue": "={{ $('Set File ID').item.json.file_type }}", "rightValue": "txt", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}}]}, "options": {"fallbackOutput": "extra"}}, "id": "04142448-232d-4f3b-9943-79359398ee44", "name": "Switch", "type": "n8n-nodes-base.switch", "typeVersion": 3, "position": [-700, 800]}, {"parameters": {"operation": "xlsx", "options": {}}, "id": "e2a48df3-6b27-4002-b2c5-28992c0f988e", "name": "Extract from Excel", "type": "n8n-nodes-base.extractFromFile", "typeVersion": 1, "position": [-300, 740]}, {"parameters": {"options": {}}, "type": "n8n-nodes-base.extractFromFile", "typeVersion": 1, "position": [-300, 920], "id": "636029fc-4e34-48f9-9e00-351176f242eb", "name": "Extract from CSV"}, {"parameters": {"options": {"reset": false}}, "type": "n8n-nodes-base.splitInBatches", "typeVersion": 3, "position": [-1500, 660], "id": "6f3d9c80-5fa3-4a26-8f6c-1d181e448cc1", "name": "Loop Over Items"}, {"parameters": {"triggerOn": "folder", "path": "/data/shared", "events": ["add", "change"], "options": {"followSymlinks": true, "usePolling": true}}, "type": "n8n-nodes-base.localFileTrigger", "typeVersion": 1, "position": [-1700, 660], "id": "3772f858-6417-4185-acc6-49d8105b33cf", "name": "Local File Trigger"}, {"parameters": {"fileSelector": "={{ $('Set File ID').item.json.file_id }}", "options": {"dataPropertyName": "=data"}}, "type": "n8n-nodes-base.readWriteFile", "typeVersion": 1, "position": [-940, 840], "id": "402acd71-4ad2-4743-8dbb-1edc4271df79", "name": "Read/Write Files from Disk"}, {"parameters": {"model": "nomic-embed-text:latest"}, "type": "@n8n/n8n-nodes-langchain.embeddingsOllama", "typeVersion": 1, "position": [440, 1100], "id": "ef6e4ccb-f5df-4084-8ccf-5858f64ca935", "name": "Embeddings <PERSON><PERSON><PERSON>", "credentials": {"ollamaApi": {"id": "GwjiKiEsG5HnTaAf", "name": "Ollama account"}}}, {"parameters": {"model": "nomic-embed-text:latest"}, "type": "@n8n/n8n-nodes-langchain.embeddingsOllama", "typeVersion": 1, "position": [-960, 360], "id": "c54376d6-a8df-4903-85ce-15783627598c", "name": "Embeddings Ollama1", "credentials": {"ollamaApi": {"id": "GwjiKiEsG5HnTaAf", "name": "Ollama account"}}}, {"parameters": {"chunkSize": 400, "options": {}}, "type": "@n8n/n8n-nodes-langchain.textSplitterRecursiveCharacterTextSplitter", "typeVersion": 1, "position": [560, 1220], "id": "923e8f78-b729-46df-bbb0-5450126547ab", "name": "Recursive Character Text Splitter"}, {"parameters": {"model": {"__rl": true, "value": "qwen2.5:14b-8k", "mode": "list", "cachedResultName": "qwen2.5:14b-8k"}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1.2, "position": [-1540, 320], "id": "c666363d-a04e-4b0b-a4b0-991c548c371f", "name": "Ollama (Change Base URL)", "credentials": {"openAiApi": {"id": "yjDbkWOXNXC959ei", "name": "OpenAi account"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "DO $$\nBEGIN\n    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'documents_pg') THEN\n        EXECUTE 'DELETE FROM documents_pg WHERE metadata->>''file_id'' LIKE ''%' || $1 || '%''';\n    END IF;\nEND\n$$;", "options": {"queryReplacement": "={{ $json.file_id }}"}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.5, "position": [-1140, 680], "id": "ee0f0fa5-8193-46bb-9f1a-7b402fd15c66", "name": "Delete Old Doc Records", "credentials": {"postgres": {"id": "UaTmh0frrACTMPxG", "name": "Postgres account"}}}, {"parameters": {"mode": "insert", "tableName": "documents_pg", "options": {}}, "type": "@n8n/n8n-nodes-langchain.vectorStorePGVector", "typeVersion": 1, "position": [580, 840], "id": "fb384bc8-b866-4745-89ac-bf4b8d374507", "name": "Postgres PGVector Store", "credentials": {"postgres": {"id": "UaTmh0frrACTMPxG", "name": "Postgres account"}}}, {"parameters": {"mode": "retrieve-as-tool", "toolName": "documents", "toolDescription": "Use RAG to look up information in the knowledgebase.", "tableName": "documents_pg", "options": {}}, "type": "@n8n/n8n-nodes-langchain.vectorStorePGVector", "typeVersion": 1, "position": [-1000, 220], "id": "5ad305d8-1db9-44cd-b241-f780d35dac1c", "name": "Document RAG Tool", "credentials": {"postgres": {"id": "UaTmh0frrACTMPxG", "name": "Postgres account"}}}, {"parameters": {"content": "## Workflow Agent Tools", "height": 380, "width": 1680, "color": 4}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-680, 140], "id": "37fe7dba-80b4-4527-937b-3434548f9894", "name": "Sticky Note7"}, {"parameters": {"rules": {"values": [{"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"leftValue": "={{ $json.tool_type }}", "rightValue": "image_analysis", "operator": {"type": "string", "operation": "equals"}}], "combinator": "and"}}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "a50a5a5a-cc87-4654-97ea-05090efdb416", "leftValue": "={{ $json.tool_type }}", "rightValue": "web_search", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}}]}, "options": {"fallbackOutput": "none"}}, "type": "n8n-nodes-base.switch", "typeVersion": 3.2, "position": [-380, 260], "id": "39087fdb-2218-4067-9d82-b8df724a6f29", "name": "Determine Tool Type"}, {"parameters": {"workflowInputs": {"values": [{"name": "tool_type"}, {"name": "query"}, {"name": "image_path"}]}}, "type": "n8n-nodes-base.executeWorkflowTrigger", "typeVersion": 1.1, "position": [-600, 260], "id": "143b4b8a-aee3-49aa-9095-4dc3581d31a0", "name": "Tool Start"}, {"parameters": {"name": "web_search", "description": "Call this tool to do an advanced web search based on a query you define.\n\nThis tool will return the contents of the 3 most relevant web pages from the search.", "workflowId": {"__rl": true, "value": "={{ $workflow.id }}", "mode": "id"}, "workflowInputs": {"mappingMode": "defineBelow", "value": {"query": "={{ $fromAI('query') }}", "tool_type": "web_search"}, "matchingColumns": [], "schema": [{"id": "tool_type", "displayName": "tool_type", "required": false, "defaultMatch": false, "display": true, "canBeUsedToMatch": true, "type": "string", "removed": false}, {"id": "query", "displayName": "query", "required": false, "defaultMatch": false, "display": true, "canBeUsedToMatch": true, "type": "string", "removed": false}, {"id": "image_path", "displayName": "image_path", "required": false, "defaultMatch": false, "display": true, "canBeUsedToMatch": true, "type": "string", "removed": true}], "attemptToConvertTypes": false, "convertFieldsToString": false}}, "type": "@n8n/n8n-nodes-langchain.toolWorkflow", "typeVersion": 2, "position": [-1220, 340], "id": "3f3c70dd-70c6-429e-804a-289ab0c4ca12", "name": "Web Search Tool"}, {"parameters": {"url": "=http://searxng:8080/search?q={{ $json.query }}&format=json", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [-140, 260], "id": "94185574-6677-4d41-9845-2b72c01febb5", "name": "SearXNG"}, {"parameters": {"fieldToSplitOut": "results", "options": {}}, "type": "n8n-nodes-base.splitOut", "typeVersion": 1, "position": [20, 260], "id": "ad800b8b-c460-4a9f-8910-c3049353e378", "name": "Split Out"}, {"parameters": {"assignments": {"assignments": [{"id": "169ce734-0077-4c34-b7f1-40a35184fad6", "name": "url", "value": "={{ $json.url }}", "type": "string"}, {"id": "310e45f1-904e-4350-971f-a8519a49ab91", "name": "title", "value": "={{ $json.title }}", "type": "string"}, {"id": "f6ac5cd2-4504-4f37-a766-33bc6ef09d47", "name": "content", "value": "={{ $json.content }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [180, 260], "id": "f878e4b7-6e2b-49c5-8b78-fac17a1b0d6b", "name": "Edit Fields2"}, {"parameters": {"maxItems": 3}, "type": "n8n-nodes-base.limit", "typeVersion": 1, "position": [340, 260], "id": "7525836e-15ae-46f8-9d71-c7fef2463ea9", "name": "Limit"}, {"parameters": {"aggregate": "aggregateAllItemData", "destinationFieldName": "search_results", "options": {}}, "type": "n8n-nodes-base.aggregate", "typeVersion": 1, "position": [800, 260], "id": "707f2774-820f-45a8-b852-85f13a581b31", "name": "Aggregate1"}, {"parameters": {"url": "={{ $json.url }}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [500, 260], "id": "9c3d5f82-81e4-4213-b109-64e9d2f5a060", "name": "HTTP Request", "alwaysOutputData": true, "onError": "continueRegularOutput"}, {"parameters": {"operation": "extractHtmlContent", "extractionValues": {"values": [{"key": "site_html", "cssSelector": "body"}]}, "options": {}}, "type": "n8n-nodes-base.html", "typeVersion": 1.2, "position": [640, 260], "id": "9030e589-f3d6-4a3b-a0a1-8380734dbcc2", "name": "HTML", "alwaysOutputData": true, "onError": "continueRegularOutput"}], "pinData": {"Tool Start": [{"json": {"tool_type": "web_search", "query": "Best AI Agent Frameworks", "tool_type 2": "image_analysis", "query 2": "Describe this image", "image_path": "/data/shared/ArchonMCPThumbnail.jpg"}}]}, "connections": {"Extract Document Text": {"main": [[{"node": "Postgres PGVector Store", "type": "main", "index": 0}]]}, "Default Data Loader": {"ai_document": [[{"node": "Postgres PGVector Store", "type": "ai_document", "index": 0}]]}, "Postgres Chat Memory": {"ai_memory": [[{"node": "RAG AI Agent", "type": "ai_memory", "index": 0}]]}, "Set File ID": {"main": [[{"node": "Delete Old Doc Records", "type": "main", "index": 0}]]}, "Edit Fields": {"main": [[{"node": "RAG AI Agent", "type": "main", "index": 0}]]}, "When chat message received": {"main": [[{"node": "<PERSON>", "type": "main", "index": 0}]]}, "Webhook": {"main": [[{"node": "<PERSON>", "type": "main", "index": 0}]]}, "Extract PDF Text": {"main": [[{"node": "Postgres PGVector Store", "type": "main", "index": 0}]]}, "Aggregate": {"main": [[{"node": "Summarize", "type": "main", "index": 0}]]}, "Summarize": {"main": [[{"node": "Postgres PGVector Store", "type": "main", "index": 0}]]}, "RAG AI Agent": {"main": [[{"node": "Respond to Webhook", "type": "main", "index": 0}]]}, "Switch": {"main": [[{"node": "Extract PDF Text", "type": "main", "index": 0}], [{"node": "Extract from Excel", "type": "main", "index": 0}], [{"node": "Extract from CSV", "type": "main", "index": 0}], [{"node": "Extract Document Text", "type": "main", "index": 0}], [{"node": "Extract Document Text", "type": "main", "index": 0}]]}, "Extract from Excel": {"main": [[{"node": "Aggregate", "type": "main", "index": 0}]]}, "Extract from CSV": {"main": [[{"node": "Aggregate", "type": "main", "index": 0}]]}, "Loop Over Items": {"main": [[], [{"node": "Set File ID", "type": "main", "index": 0}]]}, "Local File Trigger": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}]]}, "Read/Write Files from Disk": {"main": [[{"node": "Switch", "type": "main", "index": 0}]]}, "Embeddings Ollama": {"ai_embedding": [[{"node": "Postgres PGVector Store", "type": "ai_embedding", "index": 0}]]}, "Embeddings Ollama1": {"ai_embedding": [[{"node": "Document RAG Tool", "type": "ai_embedding", "index": 0}]]}, "Recursive Character Text Splitter": {"ai_textSplitter": [[{"node": "Default Data Loader", "type": "ai_textSplitter", "index": 0}]]}, "Ollama (Change Base URL)": {"ai_languageModel": [[{"node": "RAG AI Agent", "type": "ai_languageModel", "index": 0}]]}, "Delete Old Doc Records": {"main": [[{"node": "Read/Write Files from Disk", "type": "main", "index": 0}]]}, "Postgres PGVector Store": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}]]}, "Document RAG Tool": {"ai_tool": [[{"node": "RAG AI Agent", "type": "ai_tool", "index": 0}]]}, "Determine Tool Type": {"main": [[], [{"node": "SearXNG", "type": "main", "index": 0}]]}, "Tool Start": {"main": [[{"node": "Determine Tool Type", "type": "main", "index": 0}]]}, "Web Search Tool": {"ai_tool": [[{"node": "RAG AI Agent", "type": "ai_tool", "index": 0}]]}, "Respond to Webhook": {"main": [[]]}, "SearXNG": {"main": [[{"node": "Split Out", "type": "main", "index": 0}]]}, "Split Out": {"main": [[{"node": "Edit Fields2", "type": "main", "index": 0}]]}, "Edit Fields2": {"main": [[{"node": "Limit", "type": "main", "index": 0}]]}, "Limit": {"main": [[{"node": "HTTP Request", "type": "main", "index": 0}]]}, "HTTP Request": {"main": [[{"node": "HTML", "type": "main", "index": 0}]]}, "HTML": {"main": [[{"node": "Aggregate1", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "4e9d034e-2dfa-48f3-ba7e-0b0a5525273a", "meta": {"templateCredsSetupCompleted": true, "instanceId": "73cb7a3e883df514bb47e8d1b34526d30e2abb8f56cd99f10d5948a1e11b25aa"}, "id": "tomoflTrAokmE3Sq", "tags": []}