"""
Abstract interfaces for the template orchestrator system.

Following the Interface Segregation Principle (ISP), each interface
has a focused responsibility and can be implemented independently.
"""

from abc import ABC, abstractmethod
from typing import List, Dict, Set, Any, Optional
from pathlib import Path

from .models import (
    TemplateMetadata, 
    ValidationResult, 
    DependencyResult,
    GenerationResult,
    ProjectConfig
)


class ITemplate(ABC):
    """Abstract interface for all template types."""
    
    @abstractmethod
    def get_metadata(self) -> TemplateMetadata:
        """Return template metadata including dependencies and conflicts."""
        pass
    
    @abstractmethod
    def validate_compatibility(self, other_templates: Set[str]) -> ValidationResult:
        """Validate compatibility with other selected templates."""
        pass
    
    @abstractmethod
    def get_environment_variables(self) -> Dict[str, str]:
        """Return required environment variables for this template."""
        pass
    
    @abstractmethod
    def get_docker_services(self) -> Dict[str, Dict[str, Any]]:
        """Return Docker Compose service definitions."""
        pass


class ITemplateRegistry(ABC):
    """Abstract interface for template discovery and management."""
    
    @abstractmethod
    def register_template(self, template: ITemplate) -> None:
        """Register a new template in the registry."""
        pass
    
    @abstractmethod
    def get_template(self, template_id: str) -> Optional[ITemplate]:
        """Retrieve a template by its identifier."""
        pass
    
    @abstractmethod
    def get_all_templates(self) -> List[ITemplate]:
        """Get all registered templates."""
        pass
    
    @abstractmethod
    def discover_templates(self, directory: Path) -> List[ITemplate]:
        """Automatically discover templates from a directory."""
        pass
    
    @abstractmethod
    def get_templates_by_category(self, category: str) -> List[ITemplate]:
        """Get all templates in a specific category."""
        pass


class IValidator(ABC):
    """Abstract interface for validation services."""
    
    @abstractmethod
    def validate(self, data: Any) -> ValidationResult:
        """Validate the provided data and return validation result."""
        pass


class IDependencyValidator(IValidator):
    """Interface for dependency-specific validation."""
    
    @abstractmethod
    def validate_dependencies(self, selected_templates: Set[str]) -> ValidationResult:
        """Validate template dependencies and conflicts."""
        pass
    
    @abstractmethod
    def get_missing_dependencies(self, selected_templates: Set[str]) -> List[str]:
        """Get list of missing required dependencies."""
        pass
    
    @abstractmethod
    def get_conflicts(self, selected_templates: Set[str]) -> List[tuple[str, str]]:
        """Get list of conflicting template pairs."""
        pass


class IConfigurationValidator(IValidator):
    """Interface for configuration validation."""
    
    @abstractmethod
    def validate_configuration(self, config: Dict[str, Any]) -> ValidationResult:
        """Validate project configuration for completeness and correctness."""
        pass
    
    @abstractmethod
    def validate_environment_variables(self, env_vars: Dict[str, str]) -> ValidationResult:
        """Validate environment variable configuration."""
        pass


class IDependencyResolver(ABC):
    """Abstract interface for dependency resolution."""
    
    @abstractmethod
    def resolve_dependencies(self, selected_templates: Set[str]) -> DependencyResult:
        """Resolve all dependencies for selected templates."""
        pass
    
    @abstractmethod
    def get_dependency_order(self, templates: Set[str]) -> List[str]:
        """Get templates in dependency order (topological sort)."""
        pass
    
    @abstractmethod
    def detect_circular_dependencies(self, templates: Set[str]) -> List[List[str]]:
        """Detect circular dependency chains."""
        pass


class IGenerator(ABC):
    """Abstract interface for file and configuration generators."""
    
    @abstractmethod
    def generate(self, project_path: Path, config: ProjectConfig) -> GenerationResult:
        """Generate files/configuration for the project."""
        pass
    
    @abstractmethod
    def can_generate(self, templates: Set[str]) -> bool:
        """Check if this generator can handle the selected templates."""
        pass


class IDockerComposeGenerator(IGenerator):
    """Interface for Docker Compose file generation."""
    
    @abstractmethod
    def generate_compose_file(self, services: Dict[str, Dict[str, Any]]) -> str:
        """Generate Docker Compose YAML content."""
        pass
    
    @abstractmethod
    def merge_service_definitions(self, services: List[Dict[str, Any]]) -> Dict[str, Dict[str, Any]]:
        """Merge multiple service definitions handling conflicts."""
        pass


class IEnvironmentGenerator(IGenerator):
    """Interface for environment configuration generation."""
    
    @abstractmethod
    def generate_env_file(self, variables: Dict[str, str]) -> str:
        """Generate .env file content."""
        pass
    
    @abstractmethod
    def generate_env_example(self, variables: Dict[str, str]) -> str:
        """Generate .env.example file with documentation."""
        pass


class IProjectStructureGenerator(IGenerator):
    """Interface for project directory structure generation."""
    
    @abstractmethod
    def create_directory_structure(self, project_path: Path, templates: Set[str]) -> List[Path]:
        """Create directory structure based on selected templates."""
        pass
    
    @abstractmethod
    def copy_template_files(self, project_path: Path, template_id: str) -> List[Path]:
        """Copy template-specific files to project directory."""
        pass


class ITemplatePlugin(ABC):
    """Interface for template plugins to extend the system."""
    
    @abstractmethod
    def get_plugin_name(self) -> str:
        """Return the plugin name."""
        pass
    
    @abstractmethod
    def get_supported_templates(self) -> List[str]:
        """Return list of template IDs this plugin supports."""
        pass
    
    @abstractmethod
    def create_template(self, template_id: str) -> ITemplate:
        """Create a template instance for the given ID."""
        pass
    
    @abstractmethod
    def validate_plugin_dependencies(self) -> ValidationResult:
        """Validate that plugin dependencies are satisfied."""
        pass


class IOrchestrator(ABC):
    """Main orchestrator interface coordinating all components."""
    
    @abstractmethod
    def select_templates(self, template_ids: Set[str]) -> ValidationResult:
        """Select templates with validation."""
        pass
    
    @abstractmethod
    def generate_project(self, project_name: str, selected_templates: Set[str]) -> GenerationResult:
        """Generate complete project with selected templates."""
        pass
    
    @abstractmethod
    def get_available_templates(self) -> List[TemplateMetadata]:
        """Get all available templates with metadata."""
        pass
    
    @abstractmethod
    def preview_generation(self, selected_templates: Set[str]) -> Dict[str, Any]:
        """Preview what will be generated without creating files."""
        pass


class IUserInterface(ABC):
    """Abstract interface for user interaction."""
    
    @abstractmethod
    def display_templates(self, templates: List[TemplateMetadata]) -> None:
        """Display available templates to the user."""
        pass
    
    @abstractmethod
    def get_user_selection(self, templates: List[TemplateMetadata]) -> Set[str]:
        """Get template selection from user input."""
        pass
    
    @abstractmethod
    def display_validation_result(self, result: ValidationResult) -> None:
        """Display validation results to the user."""
        pass
    
    @abstractmethod
    def display_generation_progress(self, current: int, total: int, current_task: str) -> None:
        """Display generation progress to the user."""
        pass
    
    @abstractmethod
    def confirm_generation(self, preview: Dict[str, Any]) -> bool:
        """Ask user to confirm project generation."""
        pass