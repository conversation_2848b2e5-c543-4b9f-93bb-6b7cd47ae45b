"""
Base service class for business logic.

This module provides base classes and interfaces for all services
to ensure consistent design and error handling.
"""
import asyncio
from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional, Type, TypeVar, Generic
from uuid import UUID

from loguru import logger
from sqlalchemy.ext.asyncio import AsyncSession
from pydantic import BaseModel

from ..container import Container
from ..models.base import BaseModel as DBBaseModel


ServiceType = TypeVar('ServiceType')
T = TypeVar('T', bound=DBBaseModel)
CreateSchemaType = TypeVar('CreateSchemaType', bound=BaseModel)
UpdateSchemaType = TypeVar('UpdateSchemaType', bound=BaseModel)


class ServiceError(Exception):
    """Base service error."""

    def __init__(self, message: str, error_code: str = "SERVICE_ERROR", details: Optional[Dict] = None):
        super().__init__(message)
        self.message = message
        self.error_code = error_code
        self.details = details or {}


class ValidationError(ServiceError):
    """Validation error."""

    def __init__(self, message: str, field: Optional[str] = None, details: Optional[Dict] = None):
        super().__init__(message, "VALIDATION_ERROR", details)
        self.field = field


class NotFoundError(ServiceError):
    """Resource not found error."""

    def __init__(self, resource_type: str, identifier: Any, details: Optional[Dict] = None):
        message = f"{resource_type} with identifier '{identifier}' not found"
        super().__init__(message, "NOT_FOUND", details)
        self.resource_type = resource_type
        self.identifier = identifier


class PermissionError(ServiceError):
    """Permission denied error."""

    def __init__(self, action: str, resource: str, details: Optional[Dict] = None):
        message = f"Permission denied for action '{action}' on resource '{resource}'"
        super().__init__(message, "PERMISSION_DENIED", details)
        self.action = action
        self.resource = resource


class ConflictError(ServiceError):
    """Resource conflict error."""

    def __init__(self, message: str, conflicting_field: Optional[str] = None, details: Optional[Dict] = None):
        super().__init__(message, "CONFLICT", details)
        self.conflicting_field = conflicting_field


class BaseService(ABC):
    """Base class for all business services."""

    def __init__(self, session: AsyncSession, container: Optional[Container] = None):
        self.session = session
        self.container = container
        self.logger = logger.bind(service=self.__class__.__name__)

    async def __aenter__(self):
        """Async context manager entry."""
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit with transaction handling."""
        if exc_type is not None:
            await self.session.rollback()
        else:
            await self.session.commit()

    def _log_operation(self, operation: str, **kwargs) -> None:
        """Log service operation."""
        self.logger.info(f"Service operation: {operation}", **kwargs)

    def _log_error(self, operation: str, error: Exception, **kwargs) -> None:
        """Log service error."""
        self.logger.error(f"Service error in {operation}: {str(error)}", **kwargs)

    async def _handle_error(self, operation: str, error: Exception) -> None:
        """Handle service errors with logging and potential recovery."""
        self._log_error(operation, error)

        # Re-raise service errors as-is
        if isinstance(error, ServiceError):
            raise error

        # Convert common database errors
        if "unique constraint" in str(error).lower():
            raise ConflictError("Resource already exists")

        if "foreign key constraint" in str(error).lower():
            raise ValidationError("Referenced resource does not exist")

        # Generic service error for unexpected exceptions
        raise ServiceError(f"Unexpected error in {operation}: {str(error)}")


class ServiceFactory(Generic[ServiceType]):
    """Factory for creating service instances with dependency injection."""
    
    def __init__(self, service_class: type[ServiceType]):
        self.service_class = service_class
    
    def create(self, session: AsyncSession, container: Container) -> ServiceType:
        """Create service instance with dependencies."""
        return self.service_class(session, container)