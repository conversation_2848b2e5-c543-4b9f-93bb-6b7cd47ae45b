"""
User model for authentication and user management.

This module provides the User model following the Single Responsibility Principle.
"""

from datetime import datetime
from typing import List, Optional
from uuid import UUID

from fastapi_users_db_sqlalchemy import SQLAlchemyBaseUserTableUUID
from sqlalchemy import <PERSON><PERSON><PERSON>, DateTime, String, Text
from sqlalchemy.dialects.postgresql import <PERSON><PERSON><PERSON>
from sqlalchemy.orm import Mapped, mapped_column, relationship

from .base import BaseModel


class User(SQLAlchemyBaseUserTableUUID, BaseModel):
    """
    User model for authentication and profile management.
    
    Extends FastAPI-Users base model with additional fields.
    """
    
    # Profile information
    name: Mapped[str] = mapped_column(
        String(255),
        nullable=False,
        index=True
    )
    
    avatar_url: Mapped[Optional[str]] = mapped_column(
        String(500),
        nullable=True
    )
    
    bio: Mapped[Optional[str]] = mapped_column(
        Text,
        nullable=True
    )
    
    # User preferences
    preferences: Mapped[dict] = mapped_column(
        JSON,
        default=dict,
        nullable=False
    )
    
    # Account settings
    timezone: Mapped[str] = mapped_column(
        String(50),
        default="UTC",
        nullable=False
    )
    
    language: Mapped[str] = mapped_column(
        String(10),
        default="en",
        nullable=False
    )
    
    # Subscription and limits
    subscription_tier: Mapped[str] = mapped_column(
        String(50),
        default="free",
        nullable=False
    )
    
    api_quota_used: Mapped[int] = mapped_column(
        default=0,
        nullable=False
    )
    
    api_quota_limit: Mapped[int] = mapped_column(
        default=1000,
        nullable=False
    )
    
    # Activity tracking
    last_active_at: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True),
        nullable=True
    )
    
    login_count: Mapped[int] = mapped_column(
        default=0,
        nullable=False
    )
    
    # Feature flags
    beta_features_enabled: Mapped[bool] = mapped_column(
        Boolean,
        default=False,
        nullable=False
    )
    
    # Relationships
    conversations: Mapped[List["Conversation"]] = relationship(
        "Conversation",
        back_populates="user",
        cascade="all, delete-orphan"
    )
    
    documents: Mapped[List["Document"]] = relationship(
        "Document", 
        back_populates="user",
        cascade="all, delete-orphan"
    )
    
    def update_last_active(self) -> None:
        """Update last active timestamp."""
        self.last_active_at = datetime.utcnow()
    
    def increment_login_count(self) -> None:
        """Increment login counter."""
        self.login_count += 1
        self.update_last_active()
    
    def increment_api_usage(self, amount: int = 1) -> None:
        """Increment API quota usage."""
        self.api_quota_used += amount
    
    def reset_api_quota(self) -> None:
        """Reset API quota usage (typically monthly)."""
        self.api_quota_used = 0
    
    @property
    def api_quota_remaining(self) -> int:
        """Get remaining API quota."""
        return max(0, self.api_quota_limit - self.api_quota_used)
    
    @property
    def is_quota_exceeded(self) -> bool:
        """Check if API quota is exceeded."""
        return self.api_quota_used >= self.api_quota_limit
    
    @property
    def display_name(self) -> str:
        """Get display name for the user."""
        return self.name or self.email.split("@")[0]
    
    def get_preference(self, key: str, default=None):
        """Get user preference by key."""
        return self.preferences.get(key, default)
    
    def set_preference(self, key: str, value) -> None:
        """Set user preference."""
        if self.preferences is None:
            self.preferences = {}
        self.preferences[key] = value
    
    def to_dict(self) -> dict:
        """Convert user to dictionary (excluding sensitive fields)."""
        return {
            "id": str(self.id),
            "email": self.email,
            "name": self.name,
            "avatar_url": self.avatar_url,
            "bio": self.bio,
            "timezone": self.timezone,
            "language": self.language,
            "subscription_tier": self.subscription_tier,
            "api_quota_used": self.api_quota_used,
            "api_quota_limit": self.api_quota_limit,
            "api_quota_remaining": self.api_quota_remaining,
            "last_active_at": self.last_active_at.isoformat() if self.last_active_at else None,
            "login_count": self.login_count,
            "beta_features_enabled": self.beta_features_enabled,
            "is_active": self.is_active,
            "is_verified": self.is_verified,
            "created_at": self.created_at.isoformat(),
            "updated_at": self.updated_at.isoformat(),
        }