# Docker Compose Operations with Comprehensive Error Logging
# Executes docker-compose commands and captures all output for AI analysis

param(
    [string]$Operation = "restart",
    [string]$LogDir = "..\..\data\logs\docker-compose"
)

# Colors for output
$Red = "`e[31m"
$Green = "`e[32m"
$Yellow = "`e[33m"
$Blue = "`e[34m"
$Reset = "`e[0m"

function Write-Log {
    param([string]$Message, [string]$Color = $Blue)
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    Write-Host "${Color}[$timestamp]${Reset} $Message"
}

function Write-Error-Log {
    param([string]$Message)
    Write-Log $Message $Red
}

function Write-Success {
    param([string]$Message)
    Write-Log $Message $Green
}

function Write-Warning {
    param([string]$Message)
    Write-Log $Message $Yellow
}

# Ensure we're in the correct directory
$InfraDir = "E:\Projects\lonors\loni\apps\infra"
if (-not (Test-Path $InfraDir)) {
    Write-Error-Log "Infrastructure directory not found: $InfraDir"
    exit 1
}

Set-Location $InfraDir
Write-Log "Changed to directory: $InfraDir"

# Create log directory
$FullLogDir = Join-Path $InfraDir $LogDir
if (-not (Test-Path $FullLogDir)) {
    New-Item -ItemType Directory -Path $FullLogDir -Force | Out-Null
    Write-Log "Created log directory: $FullLogDir"
}

# Generate timestamp for log files
$Timestamp = Get-Date -Format "yyyyMMdd_HHmmss"
$LogFile = Join-Path $FullLogDir "docker-compose-$Timestamp.log"
$ErrorFile = Join-Path $FullLogDir "docker-compose-errors-$Timestamp.jsonl"

function Write-Structured-Error {
    param(
        [string]$Service,
        [string]$ErrorType,
        [string]$Message,
        [string]$Severity = "ERROR",
        [hashtable]$Additional = @{}
    )
    
    $ErrorEntry = @{
        timestamp = (Get-Date -Format "yyyy-MM-ddTHH:mm:ss.fffZ")
        service = $Service
        error_type = $ErrorType
        message = $Message
        severity = $Severity
    }
    
    # Add additional fields
    foreach ($key in $Additional.Keys) {
        $ErrorEntry[$key] = $Additional[$key]
    }
    
    $JsonLine = $ErrorEntry | ConvertTo-Json -Compress
    Add-Content -Path $ErrorFile -Value $JsonLine
    Write-Error-Log "$Service`: $Message"
}

function Execute-Docker-Command {
    param(
        [string]$Command,
        [string]$Description
    )
    
    Write-Log "Executing: $Description"
    Write-Log "Command: docker-compose $Command"
    
    # Start process and capture output
    $ProcessInfo = New-Object System.Diagnostics.ProcessStartInfo
    $ProcessInfo.FileName = "docker-compose"
    $ProcessInfo.Arguments = $Command
    $ProcessInfo.RedirectStandardOutput = $true
    $ProcessInfo.RedirectStandardError = $true
    $ProcessInfo.UseShellExecute = $false
    $ProcessInfo.CreateNoWindow = $true
    
    $Process = New-Object System.Diagnostics.Process
    $Process.StartInfo = $ProcessInfo
    
    # Event handlers for output
    $OutputBuilder = New-Object System.Text.StringBuilder
    $ErrorBuilder = New-Object System.Text.StringBuilder
    
    $OutputHandler = {
        if ($Event.SourceEventArgs.Data) {
            $OutputBuilder.AppendLine($Event.SourceEventArgs.Data)
            Write-Host $Event.SourceEventArgs.Data
        }
    }
    
    $ErrorHandler = {
        if ($Event.SourceEventArgs.Data) {
            $ErrorBuilder.AppendLine($Event.SourceEventArgs.Data)
            Write-Host $Event.SourceEventArgs.Data -ForegroundColor Red
        }
    }
    
    Register-ObjectEvent -InputObject $Process -EventName OutputDataReceived -Action $OutputHandler | Out-Null
    Register-ObjectEvent -InputObject $Process -EventName ErrorDataReceived -Action $ErrorHandler | Out-Null
    
    # Start process
    $Process.Start() | Out-Null
    $Process.BeginOutputReadLine()
    $Process.BeginErrorReadLine()
    
    # Wait for completion
    $Process.WaitForExit()
    
    # Clean up events
    Get-EventSubscriber | Unregister-Event
    
    $ExitCode = $Process.ExitCode
    $Output = $OutputBuilder.ToString()
    $ErrorOutput = $ErrorBuilder.ToString()
    
    # Log all output
    $LogEntry = @"
=== $Description ===
Command: docker-compose $Command
Exit Code: $ExitCode
Timestamp: $(Get-Date -Format "yyyy-MM-dd HH:mm:ss")

STDOUT:
$Output

STDERR:
$ErrorOutput

"@
    
    Add-Content -Path $LogFile -Value $LogEntry
    
    # Parse errors and create structured logs
    if ($ExitCode -ne 0 -or $ErrorOutput) {
        Parse-Docker-Errors -Output $Output -ErrorOutput $ErrorOutput -Command $Command
    }
    
    return @{
        ExitCode = $ExitCode
        Output = $Output
        ErrorOutput = $ErrorOutput
        Success = ($ExitCode -eq 0)
    }
}

function Parse-Docker-Errors {
    param(
        [string]$Output,
        [string]$ErrorOutput,
        [string]$Command
    )
    
    $AllOutput = "$Output`n$ErrorOutput"
    $Lines = $AllOutput -split "`n"
    
    foreach ($Line in $Lines) {
        $Line = $Line.Trim()
        if (-not $Line) { continue }
        
        # Parse different types of Docker errors
        if ($Line -match "ERROR|FATAL|CRITICAL|failed|Failed|FAILED") {
            # Extract service name if possible
            $Service = "unknown"
            if ($Line -match "(\w+)\s*\|\s*") {
                $Service = $Matches[1]
            } elseif ($Line -match "service\s+(\w+)") {
                $Service = $Matches[1]
            }
            
            # Determine error type
            $ErrorType = "general_error"
            if ($Line -match "build.*failed") { $ErrorType = "build_failure" }
            elseif ($Line -match "health.*check.*failed") { $ErrorType = "health_check_failure" }
            elseif ($Line -match "container.*exited") { $ErrorType = "container_exit" }
            elseif ($Line -match "network.*error") { $ErrorType = "network_error" }
            elseif ($Line -match "volume.*error") { $ErrorType = "volume_error" }
            
            # Determine severity
            $Severity = "ERROR"
            if ($Line -match "FATAL|CRITICAL") { $Severity = "CRITICAL" }
            elseif ($Line -match "failed.*to.*start") { $Severity = "CRITICAL" }
            
            Write-Structured-Error -Service $Service -ErrorType $ErrorType -Message $Line -Severity $Severity -Additional @{
                command = $Command
                raw_output = $Line
            }
        }
    }
}

function Check-Service-Health {
    Write-Log "Checking service health..."
    
    $Result = Execute-Docker-Command "ps" "Check running services"
    if ($Result.Success) {
        $Services = $Result.Output -split "`n" | Where-Object { $_ -match "loni-" }
        
        Write-Log "Running services:"
        foreach ($Service in $Services) {
            if ($Service.Trim()) {
                Write-Log "  $Service"
            }
        }
        
        # Check for unhealthy services
        $UnhealthyServices = $Result.Output -split "`n" | Where-Object { $_ -match "unhealthy|exited" }
        foreach ($UnhealthyService in $UnhealthyServices) {
            if ($UnhealthyService.Trim()) {
                $ServiceName = if ($UnhealthyService -match "loni-(\w+)") { $Matches[1] } else { "unknown" }
                Write-Structured-Error -Service $ServiceName -ErrorType "unhealthy" -Message $UnhealthyService.Trim() -Severity "ERROR"
            }
        }
    }
}

# Main execution
Write-Log "Starting Docker Compose operations with comprehensive error logging"
Write-Log "Log file: $LogFile"
Write-Log "Error file: $ErrorFile"

try {
    # Step 1: Stop all services
    Write-Log "=== STEP 1: Stopping all services ==="
    $DownResult = Execute-Docker-Command "down --remove-orphans" "Stop all services and remove orphans"
    
    if ($DownResult.Success) {
        Write-Success "Successfully stopped all services"
    } else {
        Write-Error-Log "Failed to stop services (Exit Code: $($DownResult.ExitCode))"
    }
    
    # Step 2: Build and start services
    Write-Log "=== STEP 2: Building and starting services ==="
    $UpResult = Execute-Docker-Command "up -d --build" "Build and start all services"
    
    if ($UpResult.Success) {
        Write-Success "Successfully started all services"
    } else {
        Write-Error-Log "Failed to start services (Exit Code: $($UpResult.ExitCode))"
    }
    
    # Step 3: Wait for services to initialize
    Write-Log "=== STEP 3: Waiting for services to initialize ==="
    Start-Sleep -Seconds 30
    
    # Step 4: Check service health
    Write-Log "=== STEP 4: Checking service health ==="
    Check-Service-Health
    
    # Step 5: Show final status
    Write-Log "=== STEP 5: Final status ==="
    $StatusResult = Execute-Docker-Command "ps" "Show final service status"
    
    Write-Success "Docker Compose operations completed"
    Write-Log "Full logs available at: $LogFile"
    Write-Log "Structured errors available at: $ErrorFile"
    
} catch {
    Write-Error-Log "Unexpected error during Docker Compose operations: $_"
    Write-Structured-Error -Service "docker-compose" -ErrorType "script_error" -Message $_.Exception.Message -Severity "CRITICAL"
}

Write-Log "Docker Compose operations with error logging completed"
