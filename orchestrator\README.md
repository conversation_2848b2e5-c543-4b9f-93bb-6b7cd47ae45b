# Project Template Orchestrator

🚀 **Intelligent project generation with dependency management and conflict prevention**

The Project Template Orchestrator is a sophisticated system for generating Docker-based project environments with smart template selection, automatic dependency resolution, and conflict detection. Built following SOLID principles and Context Engineering methodology.

## Features

### 🧩 **Smart Template Management**
- **60+ Built-in Templates**: Comprehensive coverage of modern tech stack
- **Intelligent Dependencies**: Automatic dependency resolution (e.g., ShadCN → React + TailwindCSS)
- **Conflict Prevention**: Mutually exclusive templates (<PERSON><PERSON> ↔ Electron, npm ↔ pnpm ↔ bun)
- **Category Organization**: Backend, Frontend, Database, AI/ML, Infrastructure

### 🔍 **Advanced Validation**
- **Real-time Conflict Detection**: Identifies incompatible template combinations
- **Dependency Validation**: Ensures all required dependencies are included
- **Port Conflict Resolution**: Automatically resolves Docker port conflicts
- **Security Scanning**: Validates templates for security vulnerabilities

### 🐳 **Docker Integration**
- **Automatic Docker Compose Generation**: Creates optimized service configurations
- **Environment Management**: Secure .env file generation with auto-generated secrets
- **Volume Management**: Intelligent volume mapping and conflict resolution
- **Service Health Checks**: Built-in monitoring and dependency management

### 💻 **Developer Experience**
- **Interactive CLI**: Rich terminal interface with checkbox selection
- **Real-time Preview**: See what will be generated before creation
- **Comprehensive Documentation**: Auto-generated README and documentation
- **Project Structure**: Intelligent directory layout based on selected technologies

## Quick Start

### Installation

```bash
# Clone the repository
git clone https://github.com/lonors/orchestrator.git
cd orchestrator

# Install dependencies
pip install -r requirements.txt

# Install the package
pip install -e .
```

### Usage

#### Interactive Mode (Recommended)
```bash
orchestrator interactive
```

#### Command Line Mode
```bash
# List available templates
orchestrator list-templates

# Create a project with specific templates
orchestrator create my-project -t react -t postgresql -t caddy

# Search templates
orchestrator search -c frontend -s "react"

# Validate template combination
orchestrator validate react shadcn tailwindcss

# Preview generation
orchestrator preview nextjs supabase qdrant
```

## Available Templates

### Backend Services
- **PostgreSQL** - Relational database with pgvector
- **Supabase** - Complete backend-as-a-service
- **FastAPI** - High-performance Python web framework
- **Redis** - In-memory data store

### Frontend Frameworks
- **React 18** - Modern frontend framework
- **Next.js** - React framework with SSR
- **Streamlit** - Python web UI framework

### UI Libraries
- **ShadCN UI** - Modern component library (requires React + TailwindCSS)
- **TailwindCSS** - Utility-first CSS framework
- **Framer Motion** - Animation library

### Package Managers (Mutually Exclusive)
- **npm** - Node.js package manager
- **pnpm** - Fast, disk-efficient package manager
- **bun** - All-in-one JavaScript runtime

### Desktop Frameworks (Mutually Exclusive)
- **Tauri** - Rust-based desktop apps
- **Electron** - Cross-platform desktop apps

### Databases
- **Qdrant** - Vector database for AI applications
- **Neo4j** - Graph database
- **ClickHouse** - Analytics database

### AI/ML Services
- **Ollama** - Local LLM serving
- **LangFuse** - LLM observability
- **Mem0** - AI memory management

### Infrastructure
- **Docker** - Containerization platform
- **Caddy** - Modern web server with auto-HTTPS
- **n8n** - Workflow automation
- **Nginx** - High-performance web server

## Architecture

The orchestrator follows SOLID principles with a modular architecture:

```
orchestrator/
├── core/                    # Core business logic
│   ├── interfaces.py        # Abstract interfaces (ISP)
│   ├── models.py           # Data models with validation
│   ├── services.py         # Main orchestrator service
│   ├── registry.py         # Template registry
│   ├── dependency_resolver.py # Graph-based dependency resolution
│   └── validators.py       # Validation services
├── generators/             # File generators
│   ├── docker_compose.py   # Docker Compose generation
│   ├── environment.py      # Environment file generation
│   └── project_structure.py # Project structure creation
├── templates/              # Template definitions
├── ui/                     # User interfaces
│   ├── cli.py             # Command-line interface
│   └── interactive.py     # Interactive selection
└── tests/                  # Comprehensive test suite
```

### Key Design Patterns

- **Dependency Injection**: Clean separation of concerns with `DependencyContainer`
- **Strategy Pattern**: Pluggable validators and generators
- **Template Method**: Consistent generation workflow
- **Repository Pattern**: Centralized template management
- **Factory Pattern**: Template creation and instantiation

## Template Selection Logic

### Dependency Resolution
```python
# Example: Selecting ShadCN automatically includes dependencies
selected = {"shadcn"}
resolved = resolver.resolve_dependencies(selected)
# Result: {"shadcn", "react", "tailwindcss", "nodejs"}
```

### Conflict Detection
```python
# Example: Conflicting package managers
selected = {"npm", "pnpm"}
validation = validator.validate(selected)
# Result: Error - "npm and pnpm are mutually exclusive"
```

### Smart Port Management
```python
# Example: Port conflict resolution
services = {
    "postgres": {"ports": ["5432:5432"]},
    "postgres-test": {"ports": ["5432:5432"]}  # Conflict!
}
# Automatically resolved to:
# postgres-test: {"ports": ["6432:5432"]}
```

## Generated Project Structure

```
my-project/
├── docker-compose.yml       # Multi-service orchestration
├── .env                     # Environment configuration
├── .env.example            # Environment template
├── .gitignore              # Git ignore rules
├── README.md               # Project documentation
├── package.json            # Node.js dependencies (if applicable)
├── src/                    # Source code structure
│   ├── components/         # React components (if React selected)
│   ├── pages/             # Page components
│   └── utils/             # Utility functions
└── tests/                  # Test structure
```

## Environment Management

The orchestrator generates secure environment configurations:

### Automatic Secret Generation
```bash
# Generated .env with secure values
POSTGRES_PASSWORD=x8K9mN2pQ7vR3wE6  # Auto-generated
JWT_SECRET=sk-a1b2c3d4e5f6...      # Secure token
API_KEY=your_api_key_here          # Placeholder for manual entry
```

### Environment Variants
```bash
.env              # Main environment file
.env.example      # Template for sharing
.env.development  # Development-specific settings
.env.production   # Production configuration
```

## Docker Compose Features

### Service Management
- **Automatic service orchestration** with proper dependencies
- **Health checks** and restart policies
- **Resource limits** and optimization
- **Network isolation** and communication

### Volume Management
- **Persistent data storage** for databases
- **Source code mounting** for development
- **Conflict resolution** for shared volumes

### Environment Integration
- **Variable substitution** from .env files
- **Service discovery** through container names
- **Port mapping** with conflict resolution

## Development

### Project Setup
```bash
# Clone and setup development environment
git clone https://github.com/lonors/orchestrator.git
cd orchestrator

# Create virtual environment
python -m venv venv
source venv/bin/activate  # Linux/Mac
# or venv\Scripts\activate  # Windows

# Install development dependencies
pip install -r requirements.txt
pip install -e .[dev]
```

### Running Tests
```bash
# Run all tests
pytest

# Run with coverage
pytest --cov=orchestrator

# Run specific test file
pytest tests/test_dependency_resolver.py

# Run interactive tests
pytest -v tests/test_interactive.py
```

### Code Quality
```bash
# Format code
black orchestrator/

# Lint code
flake8 orchestrator/

# Type checking
mypy orchestrator/
```

## Contributing

1. **Fork the repository**
2. **Create a feature branch**: `git checkout -b feature/amazing-feature`
3. **Make your changes** following the coding standards
4. **Add tests** for new functionality
5. **Run the test suite**: `pytest`
6. **Commit your changes**: `git commit -m 'Add amazing feature'`
7. **Push to the branch**: `git push origin feature/amazing-feature`
8. **Open a Pull Request**

### Adding New Templates

```python
# Example: Adding a new template
new_template = {
    'id': 'fastify',
    'name': 'Fastify',
    'description': 'Fast and low overhead web framework for Node.js',
    'category': 'backend',
    'dependencies': ['nodejs'],
    'conflicts': ['express'],
    'docker_services': {
        'fastify': {
            'image': 'node:18-alpine',
            'ports': ['3000:3000'],
            'environment': {
                'NODE_ENV': '${NODE_ENV}'
            }
        }
    },
    'environment_variables': {
        'NODE_ENV': 'development'
    }
}
```

## Roadmap

### Short Term (1-3 months)
- [ ] **Web-based UI** for template selection
- [ ] **Custom template creation** wizard
- [ ] **Template marketplace** with community templates
- [ ] **Kubernetes deployment** support

### Medium Term (3-6 months)
- [ ] **CI/CD pipeline** generation
- [ ] **Multi-environment** deployment configurations
- [ ] **Infrastructure as Code** (Terraform) generation
- [ ] **Monitoring stack** integration (Prometheus, Grafana)

### Long Term (6+ months)
- [ ] **AI-powered** template recommendation
- [ ] **Visual drag-and-drop** interface
- [ ] **Team collaboration** features
- [ ] **Enterprise integration** (LDAP, SSO)

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## Acknowledgments

- **Context Engineering Methodology** - Systematic approach to AI-assisted development
- **SOLID Principles** - Clean architecture and maintainable code
- **Docker Community** - Containerization best practices
- **Open Source Community** - Inspiration and contributions

## Support

- **Documentation**: [GitHub Wiki](https://github.com/lonors/orchestrator/wiki)
- **Issues**: [GitHub Issues](https://github.com/lonors/orchestrator/issues)
- **Discussions**: [GitHub Discussions](https://github.com/lonors/orchestrator/discussions)

---

**Built with ❤️ using Context Engineering principles and SOLID architecture**