#!/usr/bin/env python3
"""
Docker Compose Error Log Cleanup
Removes resolved errors from log files when services become healthy
"""

import json
import os
import sys
from datetime import datetime, timezone
from pathlib import Path
import subprocess
import logging

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ErrorLogCleaner:
    def __init__(self, log_dir: str = "../../data/logs/docker-compose"):
        self.log_dir = Path(log_dir)
        self.log_dir.mkdir(parents=True, exist_ok=True)
        self.current_errors_file = self.log_dir / "current-errors.jsonl"
        
    def get_healthy_services(self) -> set:
        """Get list of currently healthy services from docker-compose"""
        try:
            # Run docker-compose ps to get service status
            result = subprocess.run(
                ["docker-compose", "ps", "--format", "json"],
                capture_output=True,
                text=True,
                cwd="E:/Projects/lonors/loni/apps/infra"
            )
            
            if result.returncode != 0:
                logger.error(f"Failed to get service status: {result.stderr}")
                return set()
            
            healthy_services = set()
            
            # Parse JSON output
            for line in result.stdout.strip().split('\n'):
                if line.strip():
                    try:
                        service_info = json.loads(line)
                        service_name = service_info.get('Service', '')
                        status = service_info.get('Status', '')
                        
                        # Consider service healthy if it's running
                        if 'running' in status.lower() and 'unhealthy' not in status.lower():
                            healthy_services.add(service_name)
                            
                    except json.JSONDecodeError:
                        continue
            
            logger.info(f"Found {len(healthy_services)} healthy services: {healthy_services}")
            return healthy_services
            
        except Exception as e:
            logger.error(f"Error getting healthy services: {e}")
            return set()
    
    def collect_current_errors(self) -> list:
        """Collect all current errors from recent log files"""
        current_errors = []
        
        # Get all error files from today
        today = datetime.now().strftime("%Y%m%d")
        error_files = list(self.log_dir.glob(f"docker-compose-errors-{today}_*.jsonl"))
        
        # Also check for the main error file
        main_error_file = self.log_dir / "docker-errors.jsonl"
        if main_error_file.exists():
            error_files.append(main_error_file)
        
        for error_file in error_files:
            try:
                with open(error_file, 'r') as f:
                    for line in f:
                        line = line.strip()
                        if line:
                            try:
                                error_entry = json.loads(line)
                                current_errors.append(error_entry)
                            except json.JSONDecodeError:
                                continue
            except Exception as e:
                logger.error(f"Error reading {error_file}: {e}")
        
        return current_errors
    
    def filter_unresolved_errors(self, errors: list, healthy_services: set) -> list:
        """Filter out errors for services that are now healthy"""
        unresolved_errors = []
        resolved_count = 0
        
        for error in errors:
            service = error.get('service', '')
            error_type = error.get('error_type', '')
            
            # Keep errors for services that are not healthy
            if service not in healthy_services:
                unresolved_errors.append(error)
            else:
                resolved_count += 1
                logger.info(f"Resolved error for {service}: {error.get('message', '')}")
        
        logger.info(f"Filtered out {resolved_count} resolved errors")
        return unresolved_errors
    
    def update_current_errors_file(self, unresolved_errors: list):
        """Update the current errors file with only unresolved errors"""
        try:
            with open(self.current_errors_file, 'w') as f:
                for error in unresolved_errors:
                    f.write(json.dumps(error) + '\n')
            
            logger.info(f"Updated current errors file with {len(unresolved_errors)} unresolved errors")
            
        except Exception as e:
            logger.error(f"Error updating current errors file: {e}")
    
    def create_status_summary(self, healthy_services: set, unresolved_errors: list):
        """Create a status summary file for AI consumption"""
        summary_file = self.log_dir / "status-summary.json"
        
        # Group errors by service
        errors_by_service = {}
        for error in unresolved_errors:
            service = error.get('service', 'unknown')
            if service not in errors_by_service:
                errors_by_service[service] = []
            errors_by_service[service].append(error)
        
        summary = {
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "healthy_services": list(healthy_services),
            "services_with_errors": list(errors_by_service.keys()),
            "total_healthy": len(healthy_services),
            "total_with_errors": len(errors_by_service),
            "total_errors": len(unresolved_errors),
            "errors_by_service": errors_by_service,
            "status": "healthy" if len(unresolved_errors) == 0 else "has_errors"
        }
        
        try:
            with open(summary_file, 'w') as f:
                json.dump(summary, f, indent=2)
            
            logger.info(f"Created status summary: {summary['total_healthy']} healthy, {summary['total_with_errors']} with errors")
            
        except Exception as e:
            logger.error(f"Error creating status summary: {e}")
    
    def cleanup_old_logs(self, days_to_keep: int = 7):
        """Remove old log files to prevent disk space issues"""
        try:
            cutoff_date = datetime.now().timestamp() - (days_to_keep * 24 * 60 * 60)
            
            removed_count = 0
            for log_file in self.log_dir.glob("docker-compose-*.log"):
                if log_file.stat().st_mtime < cutoff_date:
                    log_file.unlink()
                    removed_count += 1
            
            for error_file in self.log_dir.glob("docker-compose-errors-*.jsonl"):
                if error_file.stat().st_mtime < cutoff_date:
                    error_file.unlink()
                    removed_count += 1
            
            if removed_count > 0:
                logger.info(f"Cleaned up {removed_count} old log files")
                
        except Exception as e:
            logger.error(f"Error cleaning up old logs: {e}")
    
    def run_cleanup(self):
        """Main cleanup process"""
        logger.info("Starting Docker Compose error log cleanup")
        
        # Get currently healthy services
        healthy_services = self.get_healthy_services()
        
        # Collect all current errors
        all_errors = self.collect_current_errors()
        logger.info(f"Found {len(all_errors)} total errors")
        
        # Filter out resolved errors
        unresolved_errors = self.filter_unresolved_errors(all_errors, healthy_services)
        
        # Update current errors file
        self.update_current_errors_file(unresolved_errors)
        
        # Create status summary
        self.create_status_summary(healthy_services, unresolved_errors)
        
        # Cleanup old logs
        self.cleanup_old_logs()
        
        logger.info("Error log cleanup completed")
        
        # Print summary
        print(f"\n=== Docker Compose Status Summary ===")
        print(f"Healthy services: {len(healthy_services)}")
        print(f"Services with errors: {len(set(e.get('service') for e in unresolved_errors))}")
        print(f"Total unresolved errors: {len(unresolved_errors)}")
        
        if unresolved_errors:
            print(f"\nCurrent errors:")
            for error in unresolved_errors[-5:]:  # Show last 5 errors
                service = error.get('service', 'unknown')
                message = error.get('message', 'No message')
                print(f"  {service}: {message}")
        else:
            print("\n✅ No current errors - all services are healthy!")

def main():
    """Main entry point"""
    cleaner = ErrorLogCleaner()
    cleaner.run_cleanup()

if __name__ == "__main__":
    main()
