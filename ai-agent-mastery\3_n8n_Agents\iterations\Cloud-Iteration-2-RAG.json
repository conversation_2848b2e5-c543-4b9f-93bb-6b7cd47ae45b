{"name": "AI Agent Mastery P2", "nodes": [{"parameters": {"options": {}}, "id": "e7842da7-691f-4d57-b381-876507b0f1dd", "name": "OpenAI Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1, "position": [-600, 700], "credentials": {"openAiApi": {"id": "05Q6PbnSdyEcu9Ze", "name": "OpenAi account"}}}, {"parameters": {"jsonMode": "expressionData", "jsonData": "={{ $json.data || $json.text || $json.concatenated_data }}", "options": {"metadata": {"metadataValues": [{"name": "=file_id", "value": "={{ $('Set File ID').first().json.file_id }}"}, {"name": "file_title", "value": "={{ $('Set File ID').first().json.file_title }}"}, {"name": "file_url", "value": "={{ $('Set File ID').first().json.file_url }}"}]}}}, "id": "751999f0-9199-43bc-b64c-459b3d728d92", "name": "Default Data Loader", "type": "@n8n/n8n-nodes-langchain.documentDefaultDataLoader", "typeVersion": 1, "position": [1280, 1560]}, {"parameters": {"model": "text-embedding-3-small", "options": {}}, "id": "1301f42d-48b5-4f72-8f6f-d22ff477031d", "name": "Embeddings OpenAI1", "type": "@n8n/n8n-nodes-langchain.embeddingsOpenAi", "typeVersion": 1, "position": [1060, 1560], "credentials": {"openAiApi": {"id": "05Q6PbnSdyEcu9Ze", "name": "OpenAi account"}}}, {"parameters": {"operation": "download", "fileId": {"__rl": true, "value": "={{ $('Set File ID').item.json.file_id }}", "mode": "id"}, "options": {"googleFileConversion": {"conversion": {"docsToFormat": "text/plain"}}}}, "id": "b67b93cf-8e95-40e2-b817-8abb1c5dff6b", "name": "Download File", "type": "n8n-nodes-base.googleDrive", "typeVersion": 3, "position": [-380, 1280], "executeOnce": true, "credentials": {"googleDriveOAuth2Api": {"id": "p411dEzjKc4VbQGi", "name": "Google Drive account"}}}, {"parameters": {"pollTimes": {"item": [{"mode": "everyMinute"}]}, "triggerOn": "specificFolder", "folderToWatch": {"__rl": true, "value": "1HtaIIK3kWwjbwhsmEbtJ-upalxVn3Ek9", "mode": "list", "cachedResultName": "n8n Documents", "cachedResultUrl": "https://drive.google.com/drive/folders/1HtaIIK3kWwjbwhsmEbtJ-upalxVn3Ek9"}, "event": "fileCreated", "options": {}}, "id": "fcd61c9d-ef46-4963-83ec-935891f0a960", "name": "File Created", "type": "n8n-nodes-base.googleDriveTrigger", "typeVersion": 1, "position": [-1220, 1120], "credentials": {"googleDriveOAuth2Api": {"id": "p411dEzjKc4VbQGi", "name": "Google Drive account"}}}, {"parameters": {"pollTimes": {"item": [{"mode": "everyMinute"}]}, "triggerOn": "specificFolder", "folderToWatch": {"__rl": true, "value": "1HtaIIK3kWwjbwhsmEbtJ-upalxVn3Ek9", "mode": "list", "cachedResultName": "n8n Documents", "cachedResultUrl": "https://drive.google.com/drive/folders/1HtaIIK3kWwjbwhsmEbtJ-upalxVn3Ek9"}, "event": "fileUpdated", "options": {}}, "id": "f98a6de4-17ca-45fe-809d-f446d1517cd9", "name": "File Updated", "type": "n8n-nodes-base.googleDriveTrigger", "typeVersion": 1, "position": [-1220, 1280], "credentials": {"googleDriveOAuth2Api": {"id": "p411dEzjKc4VbQGi", "name": "Google Drive account"}}}, {"parameters": {"operation": "text", "options": {}}, "id": "********-11c7-46b8-8f84-3e686845906b", "name": "Extract Document Text", "type": "n8n-nodes-base.extractFromFile", "typeVersion": 1, "position": [500, 1600], "alwaysOutputData": true}, {"parameters": {}, "id": "1893fe1c-2e22-439e-a8be-1385b1688d9b", "name": "Postgres Chat Memory", "type": "@n8n/n8n-nodes-langchain.memoryPostgresChat", "typeVersion": 1, "position": [-440, 700], "notesInFlow": false, "credentials": {"postgres": {"id": "PPVUOCGy8dTN3kdl", "name": "Live Agent Studio Test"}}}, {"parameters": {"operation": "delete", "tableId": "documents", "filterType": "string", "filterString": "=metadata->>file_id=like.*{{ $json.file_id }}*"}, "id": "37e59cc9-afdf-487e-9439-1f5e6318e741", "name": "Delete Old Doc Rows", "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [-600, 1120], "alwaysOutputData": true, "credentials": {"supabaseApi": {"id": "Eu6anmgplJHxqZu2", "name": "Studio Test"}}}, {"parameters": {"assignments": {"assignments": [{"id": "10646eae-ae46-4327-a4dc-9987c2d76173", "name": "file_id", "value": "={{ $json.id }}", "type": "string"}, {"id": "f4536df5-d0b1-4392-bf17-b8137fb31a44", "name": "file_type", "value": "={{ $json.mimeType }}", "type": "string"}, {"id": "77d782de-169d-4a46-8a8e-a3831c04d90f", "name": "file_title", "value": "={{ $json.name }}", "type": "string"}, {"id": "9bde4d7f-e4f3-4ebd-9338-dce1350f9eab", "name": "file_url", "value": "={{ $json.webViewLink }}", "type": "string"}]}, "options": {}}, "id": "2c098d1c-5718-4862-8925-81339772b47f", "name": "Set File ID", "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [-820, 1260]}, {"parameters": {"content": "## RAG AI Agent with Cha<PERSON> Interface", "height": 625, "width": 1056}, "id": "951f7d21-3b1c-4b72-b50b-9074dfd79039", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-800, 360]}, {"parameters": {"options": {}}, "id": "8691b6bd-ebc1-458a-957f-534489d707e9", "name": "Respond to Webhook", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1.1, "position": [60, 440]}, {"parameters": {"assignments": {"assignments": [{"id": "9a9a245e-f1a1-4282-bb02-a81ffe629f0f", "name": "chatInput", "value": "={{ $json?.chatInput || $json.body.chatInput }}", "type": "string"}, {"id": "b80831d8-c653-4203-8706-adedfdb98f77", "name": "sessionId", "value": "={{ $json?.sessionId || $json.body.sessionId}}", "type": "string"}]}, "options": {}}, "id": "011cb346-724a-4d98-9e0a-bae44f6648dc", "name": "<PERSON>", "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [-500, 440]}, {"parameters": {"public": true, "options": {}}, "id": "c3f26299-6c67-4b77-9e8e-ed901c9ae5bc", "name": "When chat message received", "type": "@n8n/n8n-nodes-langchain.chatTrigger", "typeVersion": 1.1, "position": [-760, 440], "webhookId": "e30a5fc2-0bfb-46ad-8393-5ef955273928"}, {"parameters": {"httpMethod": "POST", "path": "e912ab8e-c9f3-447d-a358-876234435222", "authentication": "headerAuth", "responseMode": "responseNode", "options": {}}, "id": "83681432-212b-4047-83ec-1f42a7d7544a", "name": "Webhook", "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [-760, 640], "webhookId": "e912ab8e-c9f3-447d-a358-876234435222", "credentials": {"httpHeaderAuth": {"id": "6wzSkRM1jflKXEHm", "name": "<PERSON><PERSON><PERSON>"}}}, {"parameters": {"operation": "pdf", "options": {}}, "id": "af80ae6f-4a4c-45b8-87e2-ba60afcb1707", "name": "Extract PDF Text", "type": "n8n-nodes-base.extractFromFile", "typeVersion": 1, "position": [500, 1040]}, {"parameters": {"aggregate": "aggregateAllItemData", "options": {}}, "id": "eadbb977-667c-432c-933d-a09594d4248a", "name": "Aggregate", "type": "n8n-nodes-base.aggregate", "typeVersion": 1, "position": [540, 1300]}, {"parameters": {"fieldsToSummarize": {"values": [{"aggregation": "concatenate", "field": "data"}]}, "options": {}}, "id": "5035f649-b355-45ed-a56e-b1f5355a01fd", "name": "Summarize", "type": "n8n-nodes-base.summarize", "typeVersion": 1, "position": [740, 1300]}, {"parameters": {"promptType": "define", "text": "={{ $json.chatInput }}", "options": {"systemMessage": "You are a personal assistant who helps answer questions from a corpus of documents. The documents are either text based (Txt, docs, extracted PDFs, etc.) or tabular data (CSVs or Excel documents).\n\nYou are given tools to perform RAG in the 'documents' table, look up the documents available in your knowledge base in the 'document_metadata' table, extract all the text from a given document, and query the tabular files with SQL in the 'document_rows' table.\n\nAlways start by performing RAG unless the question requires a SQL query for tabular data (fetching a sum, finding a max, something a RAG lookup would be unreliable for). If RAG doesn't help, then look at the documents that are available to you, find a few that you think would contain the answer, and then analyze those.\n\nAlways tell the user if you didn't find the answer. Don't make something up just to please them."}}, "id": "9f9636c4-492a-497e-a698-b79e5e075ecc", "name": "RAG AI Agent", "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.6, "position": [-280, 440]}, {"parameters": {"rules": {"values": [{"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 1}, "conditions": [{"leftValue": "={{ $('Set File ID').item.json.file_type }}", "rightValue": "application/pdf", "operator": {"type": "string", "operation": "equals"}, "id": "ff748fd3-c5a4-4b86-a83d-8e8fdc4412c9"}], "combinator": "and"}}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 1}, "conditions": [{"id": "2ae7faa7-a936-4621-a680-60c512163034", "leftValue": "={{ $('Set File ID').item.json.file_type }}", "rightValue": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 1}, "conditions": [{"id": "fc193b06-363b-4699-a97d-e5a850138b0e", "leftValue": "={{ $('Set File ID').item.json.file_type }}", "rightValue": "=application/vnd.google-apps.spreadsheet", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 1}, "conditions": [{"id": "b69f5605-0179-4b02-9a32-e34bb085f82d", "leftValue": "={{ $('Set File ID').item.json.file_type }}", "rightValue": "application/vnd.google-apps.document", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}}]}, "options": {"fallbackOutput": "extra"}}, "id": "6a5a340c-4ca8-4545-8683-1a4a72328859", "name": "Switch", "type": "n8n-nodes-base.switch", "typeVersion": 3, "position": [-100, 1260]}, {"parameters": {"mode": "insert", "tableName": {"__rl": true, "value": "documents", "mode": "list", "cachedResultName": "documents"}, "options": {"queryName": "match_documents"}}, "id": "7ec68bdb-b47e-4b97-8a9c-c95c58dd513b", "name": "Insert into Supabase Vectorstore", "type": "@n8n/n8n-nodes-langchain.vectorStoreSupabase", "typeVersion": 1, "position": [1160, 1320], "credentials": {"supabaseApi": {"id": "Eu6anmgplJHxqZu2", "name": "Studio Test"}}}, {"parameters": {"operation": "xlsx", "options": {}}, "id": "5278226c-b87f-4771-8287-7138059<PERSON>dee", "name": "Extract from Excel", "type": "n8n-nodes-base.extractFromFile", "typeVersion": 1, "position": [320, 1220]}, {"parameters": {"options": {}}, "type": "n8n-nodes-base.extractFromFile", "typeVersion": 1, "position": [320, 1400], "id": "7f11de8c-72b0-42c1-a150-fcb57538641e", "name": "Extract from CSV"}, {"parameters": {"content": "## Run once to set up the RAG table", "height": 260, "width": 460, "color": 3}, "type": "n8n-nodes-base.stickyNote", "position": [-1280, 720], "typeVersion": 1, "id": "bbfac6af-55e4-48e3-9918-2aec960bff94", "name": "Sticky Note3"}, {"parameters": {"options": {}}, "type": "@n8n/n8n-nodes-langchain.embeddingsOpenAi", "typeVersion": 1.2, "position": [-120, 800], "id": "029708df-b80c-49a3-9545-60842ec15b6b", "name": "Embeddings OpenAI2", "credentials": {"openAiApi": {"id": "05Q6PbnSdyEcu9Ze", "name": "OpenAi account"}}}, {"parameters": {"options": {"reset": false}}, "type": "n8n-nodes-base.splitInBatches", "typeVersion": 3, "position": [-1000, 1120], "id": "f1c72cb4-bbce-421d-955d-1ba8dcd9b1af", "name": "Loop Over Items"}, {"parameters": {"operation": "execute<PERSON>uery", "query": "-- Enable the pgvector extension to work with embedding vectors\ncreate extension vector;\n\n-- Create a table to store your documents\ncreate table documents (\n  id bigserial primary key,\n  content text, -- corresponds to Document.pageContent\n  metadata jsonb, -- corresponds to Document.metadata\n  embedding vector(1536) -- 1536 works for OpenAI embeddings, change if needed\n);\n\n-- Create a function to search for documents\ncreate function match_documents (\n  query_embedding vector(1536),\n  match_count int default null,\n  filter jsonb DEFAULT '{}'\n) returns table (\n  id bigint,\n  content text,\n  metadata jsonb,\n  similarity float\n)\nlanguage plpgsql\nas $$\n#variable_conflict use_column\nbegin\n  return query\n  select\n    id,\n    content,\n    metadata,\n    1 - (documents.embedding <=> query_embedding) as similarity\n  from documents\n  where metadata @> filter\n  order by documents.embedding <=> query_embedding\n  limit match_count;\nend;\n$$;", "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.5, "position": [-1120, 800], "id": "5ef88878-d01f-4a02-aadf-48f6cd614366", "name": "Create Documents Table and Match Function", "credentials": {"postgres": {"id": "PPVUOCGy8dTN3kdl", "name": "Live Agent Studio Test"}}}, {"parameters": {"chunkSize": 400, "chunkOverlap": null, "options": {}}, "type": "@n8n/n8n-nodes-langchain.textSplitterRecursiveCharacterTextSplitter", "typeVersion": 1, "position": [1200, 1700], "id": "53f79b44-7d6b-4147-bccc-c08b4bca7a6e", "name": "Recursive Character Text Splitter"}, {"parameters": {"mode": "retrieve-as-tool", "toolName": "documents", "toolDescription": "Use RAG to look up information in the knowledgebase.", "tableName": {"__rl": true, "value": "documents", "mode": "list", "cachedResultName": "documents"}, "options": {"queryName": "match_documents"}}, "type": "@n8n/n8n-nodes-langchain.vectorStoreSupabase", "typeVersion": 1, "position": [-60, 640], "id": "3d23fb4b-fcba-4448-8b26-a6f2ddd205ca", "name": "Document RAG Tool", "credentials": {"supabaseApi": {"id": "Eu6anmgplJHxqZu2", "name": "Studio Test"}}}, {"parameters": {"content": "## Workflow Agent Tools", "height": 380, "width": 1100, "color": 4}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [280, 600], "id": "cea7f8eb-0034-4a2e-9080-15a3d11e92b6", "name": "Sticky Note7"}, {"parameters": {"name": "web_search", "description": "Call this tool to do an advanced web search based on a query you define.", "workflowId": {"__rl": true, "value": "={{ $workflow.id }}", "mode": "id"}, "workflowInputs": {"mappingMode": "defineBelow", "value": {"query": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('query', ``, 'string') }}", "tool_type": "web_search"}, "matchingColumns": [], "schema": [{"id": "tool_type", "displayName": "tool_type", "required": false, "defaultMatch": false, "display": true, "canBeUsedToMatch": true, "type": "string", "removed": false}, {"id": "query", "displayName": "query", "required": false, "defaultMatch": false, "display": true, "canBeUsedToMatch": true, "type": "string", "removed": false}], "attemptToConvertTypes": false, "convertFieldsToString": false}}, "type": "@n8n/n8n-nodes-langchain.toolWorkflow", "typeVersion": 2, "position": [-280, 700], "id": "7456fa42-ca20-404a-b965-0c3f8e7b396a", "name": "Web Search Tool"}, {"parameters": {"rules": {"values": [{"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"leftValue": "={{ $json.tool_type }}", "rightValue": "image_analysis", "operator": {"type": "string", "operation": "equals"}, "id": "b22be562-3277-48fc-87be-f3a3930f7fd6"}], "combinator": "and"}}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "a50a5a5a-cc87-4654-97ea-05090efdb416", "leftValue": "={{ $json.tool_type }}", "rightValue": "web_search", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}}]}, "options": {"fallbackOutput": "none"}}, "type": "n8n-nodes-base.switch", "typeVersion": 3.2, "position": [580, 720], "id": "67f67d68-de95-46c0-934a-98276e6fb726", "name": "Determine Tool Type"}, {"parameters": {"url": "=https://api.search.brave.com/res/v1/web/search?q={{ $('Tool Start').item.json.query }} }}&summary=1", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Accept", "value": "application/json"}, {"name": "Accept-Encoding", "value": "gzip"}]}, "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [860, 720], "id": "cbeba4e0-f64a-497c-904f-17eb63a72ac0", "name": "Brave Web Search", "credentials": {"httpHeaderAuth": {"id": "vQywoUkt33Kl6bxG", "name": "Brave API"}}}, {"parameters": {"workflowInputs": {"values": [{"name": "tool_type"}, {"name": "query"}, {"name": "image_url"}]}}, "type": "n8n-nodes-base.executeWorkflowTrigger", "typeVersion": 1.1, "position": [360, 720], "id": "68e3623f-d20b-44a7-a27a-69739257942b", "name": "Tool Start"}, {"parameters": {"url": "=https://api.search.brave.com/res/v1/summarizer/search?key={{ $json.summarizer.key }}&entity_info=1", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Accept", "value": "application/json"}, {"name": "Accept-Encoding", "value": "gzip"}]}, "options": {}}, "id": "d85ddacd-1d07-4734-8a00-7c0c2a8ec9de", "name": "Summarize Web Research", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1120, 720], "credentials": {"httpHeaderAuth": {"id": "vQywoUkt33Kl6bxG", "name": "Brave API"}}}, {"parameters": {"content": "## Tool to Add Google Drive Files to Vector DB", "height": 887, "width": 2913, "color": 5}, "id": "ce19c022-355a-418b-8233-e907194fdcca", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-1280, 1000]}], "pinData": {"Tool Start": [{"json": {"tool_type": "image_analysis", "query": "https://drive.google.com/file/d/1vnjEMW9XKgWwmy17VjZO8ci2Mi7RO_9e/view?usp=drive_link"}}]}, "connections": {"OpenAI Chat Model": {"ai_languageModel": [[{"node": "RAG AI Agent", "type": "ai_languageModel", "index": 0}]]}, "Download File": {"main": [[{"node": "Switch", "type": "main", "index": 0}]]}, "File Created": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}]]}, "Extract Document Text": {"main": [[{"node": "Insert into Supabase Vectorstore", "type": "main", "index": 0}]]}, "Embeddings OpenAI1": {"ai_embedding": [[{"node": "Insert into Supabase Vectorstore", "type": "ai_embedding", "index": 0}]]}, "Default Data Loader": {"ai_document": [[{"node": "Insert into Supabase Vectorstore", "type": "ai_document", "index": 0}]]}, "Postgres Chat Memory": {"ai_memory": [[{"node": "RAG AI Agent", "type": "ai_memory", "index": 0}]]}, "Delete Old Doc Rows": {"main": [[{"node": "Download File", "type": "main", "index": 0}]]}, "Set File ID": {"main": [[{"node": "Delete Old Doc Rows", "type": "main", "index": 0}]]}, "File Updated": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}]]}, "Edit Fields": {"main": [[{"node": "RAG AI Agent", "type": "main", "index": 0}]]}, "When chat message received": {"main": [[{"node": "<PERSON>", "type": "main", "index": 0}]]}, "Webhook": {"main": [[{"node": "<PERSON>", "type": "main", "index": 0}]]}, "Extract PDF Text": {"main": [[{"node": "Insert into Supabase Vectorstore", "type": "main", "index": 0}]]}, "Aggregate": {"main": [[{"node": "Summarize", "type": "main", "index": 0}]]}, "Summarize": {"main": [[{"node": "Insert into Supabase Vectorstore", "type": "main", "index": 0}]]}, "RAG AI Agent": {"main": [[{"node": "Respond to Webhook", "type": "main", "index": 0}]]}, "Switch": {"main": [[{"node": "Extract PDF Text", "type": "main", "index": 0}], [{"node": "Extract from Excel", "type": "main", "index": 0}], [{"node": "Extract from CSV", "type": "main", "index": 0}], [{"node": "Extract Document Text", "type": "main", "index": 0}], [{"node": "Extract Document Text", "type": "main", "index": 0}]]}, "Extract from Excel": {"main": [[{"node": "Aggregate", "type": "main", "index": 0}]]}, "Extract from CSV": {"main": [[{"node": "Aggregate", "type": "main", "index": 0}]]}, "Embeddings OpenAI2": {"ai_embedding": [[{"node": "Document RAG Tool", "type": "ai_embedding", "index": 0}]]}, "Loop Over Items": {"main": [[], [{"node": "Set File ID", "type": "main", "index": 0}]]}, "Insert into Supabase Vectorstore": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}]]}, "Create Documents Table and Match Function": {"main": [[]]}, "Recursive Character Text Splitter": {"ai_textSplitter": [[{"node": "Default Data Loader", "type": "ai_textSplitter", "index": 0}]]}, "Respond to Webhook": {"main": [[]]}, "Document RAG Tool": {"ai_tool": [[{"node": "RAG AI Agent", "type": "ai_tool", "index": 0}]]}, "Web Search Tool": {"ai_tool": [[{"node": "RAG AI Agent", "type": "ai_tool", "index": 0}]]}, "Determine Tool Type": {"main": [[], [{"node": "Brave Web Search", "type": "main", "index": 0}]]}, "Tool Start": {"main": [[{"node": "Determine Tool Type", "type": "main", "index": 0}]]}, "Brave Web Search": {"main": [[{"node": "Summarize Web Research", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "87c9a926-7ea8-4be6-a305-3bc8abac4dc2", "meta": {"templateCredsSetupCompleted": true, "instanceId": "f65a08c0adc90a3cde2c633d24c6daecde3817033b75588ee10a781b0b7aa3f5"}, "id": "BgSAZqJj5Vgoej4t", "tags": []}