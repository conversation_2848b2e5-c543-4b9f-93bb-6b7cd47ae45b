"""
Main application configuration settings.

This module contains the main AppSettings class that
composes all domain-specific configuration classes
following the Single Responsibility Principle.
"""

import json
from functools import lru_cache
from typing import List

from pydantic import validator
from pydantic_settings import BaseSettings

from .ai import AnthropicSettings, MCPSettings, OllamaSettings, OpenAISettings
from .database import DatabaseSettings, QdrantSettings
from .logging import LoggingSettings
from .monitoring import MonitoringSettings
from .redis import RedisSettings
from .security import SecuritySettings


class AppSettings(BaseSettings):
    """Main application settings."""
    
    # Basic app configuration
    app_name: str = "LONI AI Platform"
    version: str = "1.0.0"
    debug: bool = False
    
    # Server settings
    host: str = "0.0.0.0"
    port: int = 8000
    workers: int = 1
    
    # CORS settings
    cors_origins: List[str] = ["http://localhost:3000"]
    allowed_hosts: List[str] = ["*"]
    
    # Feature flags
    enable_chat: bool = True
    enable_rag: bool = True
    enable_mcp: bool = True
    enable_multi_agent: bool = True
    
    # File upload settings
    max_upload_size: int = 50 * 1024 * 1024  # 50MB
    allowed_file_types: List[str] = [
        "text/plain",
        "text/markdown", 
        "application/pdf",
        "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
    ]
    
    # Component settings
    database: DatabaseSettings = DatabaseSettings()
    qdrant: QdrantSettings = QdrantSettings()
    ollama: OllamaSettings = OllamaSettings()
    openai: OpenAISettings = OpenAISettings()
    anthropic: AnthropicSettings = AnthropicSettings()
    redis: RedisSettings = RedisSettings()
    mcp: MCPSettings = MCPSettings()
    security: SecuritySettings = SecuritySettings()
    logging: LoggingSettings = LoggingSettings()
    monitoring: MonitoringSettings = MonitoringSettings()
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False
    
    @validator("cors_origins", pre=True)
    def assemble_cors_origins(cls, v: str | List[str]) -> List[str]:
        """Parse CORS origins from string or list."""
        if isinstance(v, str) and not v.startswith("["):
            return [i.strip() for i in v.split(",")]
        elif isinstance(v, list):
            return v
        try:
            # Safe JSON parsing instead of eval()
            return json.loads(v)
        except (json.JSONDecodeError, TypeError):
            # Fallback to comma-separated parsing
            return [i.strip() for i in str(v).split(",")]
    
    @validator("allowed_hosts", pre=True)
    def assemble_allowed_hosts(cls, v: str | List[str]) -> List[str]:
        """Parse allowed hosts from string or list."""
        if isinstance(v, str) and not v.startswith("["):
            return [i.strip() for i in v.split(",")]
        elif isinstance(v, list):
            return v
        try:
            # Safe JSON parsing instead of eval()
            return json.loads(v)
        except (json.JSONDecodeError, TypeError):
            # Fallback to comma-separated parsing
            return [i.strip() for i in str(v).split(",")]


@lru_cache()
def get_settings() -> AppSettings:
    """
    Get cached application settings.
    
    Returns:
        Application settings instance
    """
    return AppSettings()