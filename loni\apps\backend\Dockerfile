# Backend Dockerfile for LONI Platform
# Multi-stage build with UV package manager and FastAPI

# Stage 1: Base image with UV
FROM python:3.11-slim AS base

# Install system dependencies
RUN apt-get update && apt-get install -y \
    curl \
    build-essential \
    && rm -rf /var/lib/apt/lists/*

# Install UV package manager
RUN pip install uv

# Set working directory
WORKDIR /app

# Stage 2: Dependencies stage
FROM base AS deps

# Copy dependency files
COPY pyproject.toml ./
COPY requirements.txt* ./

# Create virtual environment and install dependencies
RUN uv venv
RUN uv pip install -r pyproject.toml

# Stage 3: Development stage (optional)
FROM deps AS dev

# Install development dependencies
RUN uv pip install pytest pytest-cov pytest-asyncio \
    ruff mypy black isort \
    httpx

# Copy source code
COPY . .

# Stage 4: Production stage
FROM base AS production

# Create non-root user for security
RUN useradd --create-home --shell /bin/bash loni

# Set working directory
WORKDIR /app

# Copy virtual environment from deps stage
COPY --from=deps /app/.venv /app/.venv

# Make sure venv is in PATH
ENV PATH="/app/.venv/bin:$PATH"

# Copy application code
COPY . .

# Create logs directory
RUN mkdir -p /app/logs && chown loni:loni /app/logs

# Set correct permissions
RUN chown -R loni:loni /app

# Switch to non-root user
USER loni

# Set environment variables
ENV PYTHONPATH=/app
ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONUNBUFFERED=1

# Expose port
EXPOSE 8000

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:8000/health || exit 1

# Command to run the application
CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000", "--workers", "1"]