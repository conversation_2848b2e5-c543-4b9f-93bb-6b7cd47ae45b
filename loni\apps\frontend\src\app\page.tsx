/**
 * Main application page - Chat interface
 */
'use client'

import { useState, useEffect } from 'react'
import { Sidebar } from '@/components/layout/Sidebar'
import { ChatArea } from '@/components/chat/ChatArea'
import { LoginForm } from '@/components/auth/LoginForm'
import { useAuth } from '@/hooks/useAuth'
import { useChat } from '@/hooks/useChat'
import { Button } from '@/components/ui/button'
import { Menu, X } from 'lucide-react'
import type { LoginRequest } from '@/types'

export default function HomePage() {
  const [sidebarOpen, setSidebarOpen] = useState(true)
  const [authMode, setAuthMode] = useState<'login' | 'register'>('login')
  const [authError, setAuthError] = useState<string | undefined>(undefined)

  const { 
    user, 
    isAuthenticated, 
    isLoading: authLoading, 
    login, 
    register, 
    logout 
  } = useAuth()

  const {
    conversations,
    currentConversation,
    messages,
    isGenerating,
    streamingMessage,
    error: chatError,
    models,
    sendMessage,
    sendMessageStream,
    createConversation,
    selectConversation,
    deleteConversation,
    updateConversation,
    loadConversations,
    loadModels,
    clearError
  } = useChat()

  // Load data when authenticated
  useEffect(() => {
    if (isAuthenticated && user) {
      loadConversations()
      loadModels()
    }
  }, [isAuthenticated, user, loadConversations, loadModels])

  const handleLogin = async (credentials: LoginRequest) => {
    try {
      setAuthError(undefined)
      await login(credentials)
    } catch (error: any) {
      setAuthError(error.message || 'Login failed')
      throw error
    }
  }

  const handleRegister = async (userData: any) => {
    try {
      setAuthError(undefined)
      await register(userData)
    } catch (error: any) {
      setAuthError(error.message || 'Registration failed')
      throw error
    }
  }

  const handleLogout = async () => {
    await logout()
  }

  const handleNewConversation = async () => {
    try {
      const conversation = await createConversation()
      await selectConversation(conversation.id)
    } catch (error) {
      console.error('Failed to create conversation:', error)
    }
  }

  const handleConversationSelect = async (conversation: any) => {
    try {
      await selectConversation(conversation.id)
    } catch (error) {
      console.error('Failed to select conversation:', error)
    }
  }

  const handleConversationDelete = async (conversationId: string) => {
    try {
      await deleteConversation(conversationId)
    } catch (error) {
      console.error('Failed to delete conversation:', error)
    }
  }

  const handleConversationRename = async (conversationId: string, newTitle: string) => {
    try {
      await updateConversation(conversationId, { title: newTitle })
    } catch (error) {
      console.error('Failed to rename conversation:', error)
    }
  }

  const handleSendMessage = async (message: string) => {
    try {
      clearError()
      await sendMessage(message)
    } catch (error) {
      console.error('Failed to send message:', error)
    }
  }

  const handleSendMessageStream = async (message: string) => {
    try {
      clearError()
      await sendMessageStream(message)
    } catch (error) {
      console.error('Failed to send streaming message:', error)
    }
  }

  const handleModelChange = async (modelId: string) => {
    if (currentConversation) {
      try {
        await updateConversation(currentConversation.id, { model_name: modelId })
      } catch (error) {
        console.error('Failed to change model:', error)
      }
    }
  }

  const handleRagToggle = async () => {
    if (currentConversation) {
      try {
        await updateConversation(currentConversation.id, { 
          rag_enabled: !currentConversation.rag_enabled 
        })
      } catch (error) {
        console.error('Failed to toggle RAG:', error)
      }
    }
  }

  const handleTemperatureChange = async (temperature: number) => {
    if (currentConversation) {
      try {
        await updateConversation(currentConversation.id, { temperature })
      } catch (error) {
        console.error('Failed to change temperature:', error)
      }
    }
  }

  // Show loading state
  if (authLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-background">
        <div className="text-center">
          <div className="w-8 h-8 border-2 border-loni-primary/30 border-t-loni-primary rounded-full animate-spin mx-auto mb-4" />
          <p className="text-muted-foreground">Loading...</p>
        </div>
      </div>
    )
  }

  // Show authentication screen
  if (!isAuthenticated || !user) {
    return (
      <LoginForm
        onLogin={handleLogin}
        onSwitchToRegister={() => setAuthMode('register')}
        isLoading={authLoading}
        error={authError}
      />
    )
  }

  // Main application interface
  return (
    <div className="flex h-screen bg-background">
      {/* Mobile Menu Button */}
      <div className="md:hidden fixed top-4 left-4 z-50">
        <Button
          variant="ghost"
          size="icon"
          onClick={() => setSidebarOpen(!sidebarOpen)}
          className="bg-background/80 backdrop-blur-sm"
        >
          {sidebarOpen ? <X className="w-4 h-4" /> : <Menu className="w-4 h-4" />}
        </Button>
      </div>

      {/* Sidebar */}
      <div className={`
        fixed md:relative inset-y-0 left-0 z-40 w-80 
        transform transition-transform duration-300 ease-in-out
        ${sidebarOpen ? 'translate-x-0' : '-translate-x-full md:translate-x-0'}
        ${sidebarOpen ? 'md:block' : 'md:hidden'}
      `}>
        <Sidebar
          user={user}
          conversations={conversations}
          currentConversation={currentConversation}
          onConversationSelect={handleConversationSelect}
          onNewConversation={handleNewConversation}
          onConversationDelete={handleConversationDelete}
          onConversationRename={handleConversationRename}
          onLogout={handleLogout}
          className="h-full"
        />
      </div>

      {/* Mobile Sidebar Overlay */}
      {sidebarOpen && (
        <div 
          className="md:hidden fixed inset-0 z-30 bg-black/50"
          onClick={() => setSidebarOpen(false)}
        />
      )}

      {/* Main Content */}
      <div className={`
        flex-1 flex flex-col
        ${sidebarOpen ? 'md:ml-0' : 'md:ml-0'}
      `}>
        <ChatArea
          conversation={currentConversation}
          messages={messages}
          isGenerating={isGenerating}
          streamingMessage={streamingMessage}
          models={models}
          onSendMessage={handleSendMessage}
          onSendMessageStream={handleSendMessageStream}
          onModelChange={handleModelChange}
          onRagToggle={handleRagToggle}
          onTemperatureChange={handleTemperatureChange}
          className="flex-1"
        />
      </div>

      {/* Error Display */}
      {chatError && (
        <div className="fixed bottom-4 right-4 max-w-md p-4 bg-red-50 border border-red-200 rounded-lg shadow-lg z-50">
          <div className="flex items-center justify-between">
            <p className="text-sm text-red-600">{chatError}</p>
            <Button
              variant="ghost"
              size="sm"
              onClick={clearError}
              className="text-red-600 hover:text-red-700"
            >
              <X className="w-4 h-4" />
            </Button>
          </div>
        </div>
      )}
    </div>
  )
}