/**
 * Sidebar component for navigation and conversation list
 */
'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Card } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { 
  MessageSquarePlus, 
  Settings, 
  LogOut, 
  User, 
  FileText,
  Search,
  MoreHorizontal,
  Trash2,
  Edit
} from 'lucide-react'
import { formatRelativeTime, getInitials, truncateText } from '@/lib/utils'
import type { Conversation, User as UserType } from '@/types'

interface SidebarProps {
  user: UserType | null
  conversations: Conversation[]
  currentConversation: Conversation | null
  onConversationSelect: (conversation: Conversation) => void
  onNewConversation: () => void
  onConversationDelete: (conversationId: string) => void
  onConversationRename: (conversationId: string, newTitle: string) => void
  onLogout: () => void
  className?: string
}

export function Sidebar({
  user,
  conversations,
  currentConversation,
  onConversationSelect,
  onNewConversation,
  onConversationDelete,
  onConversationRename,
  onLogout,
  className
}: SidebarProps) {
  const [searchQuery, setSearchQuery] = useState('')
  const [editingId, setEditingId] = useState<string | null>(null)
  const [editTitle, setEditTitle] = useState('')

  const filteredConversations = conversations.filter(conv =>
    conv.title.toLowerCase().includes(searchQuery.toLowerCase())
  )

  const handleRename = (conversation: Conversation) => {
    setEditingId(conversation.id)
    setEditTitle(conversation.title)
  }

  const handleSaveRename = (conversationId: string) => {
    if (editTitle.trim()) {
      onConversationRename(conversationId, editTitle.trim())
    }
    setEditingId(null)
    setEditTitle('')
  }

  const handleCancelRename = () => {
    setEditingId(null)
    setEditTitle('')
  }

  return (
    <div className={`flex flex-col h-full bg-background border-r ${className}`}>
      {/* Header */}
      <div className="p-4 border-b">
        <div className="flex items-center gap-3 mb-4">
          <div className="w-8 h-8 bg-loni-primary rounded-lg flex items-center justify-center">
            <span className="text-white font-bold text-sm">L</span>
          </div>
          <div>
            <h1 className="font-semibold text-lg">LONI</h1>
            <p className="text-xs text-muted-foreground">AI Platform</p>
          </div>
        </div>

        <Button
          onClick={onNewConversation}
          variant="loni"
          className="w-full"
          size="sm"
        >
          <MessageSquarePlus className="w-4 h-4 mr-2" />
          New Chat
        </Button>
      </div>

      {/* Search */}
      <div className="p-4 border-b">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
          <input
            type="text"
            placeholder="Search conversations..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="w-full pl-10 pr-4 py-2 text-sm border rounded-md bg-background focus:outline-none focus:ring-2 focus:ring-loni-primary focus:border-transparent"
          />
        </div>
      </div>

      {/* Conversations List */}
      <div className="flex-1 overflow-y-auto p-2">
        {filteredConversations.length === 0 ? (
          <div className="text-center py-8 text-muted-foreground">
            <MessageSquarePlus className="w-8 h-8 mx-auto mb-2 opacity-50" />
            <p className="text-sm">No conversations yet</p>
            <p className="text-xs">Start a new chat to begin</p>
          </div>
        ) : (
          <div className="space-y-1">
            {filteredConversations.map((conversation) => (
              <ConversationItem
                key={conversation.id}
                conversation={conversation}
                isActive={currentConversation?.id === conversation.id}
                isEditing={editingId === conversation.id}
                editTitle={editTitle}
                onSelect={() => onConversationSelect(conversation)}
                onRename={() => handleRename(conversation)}
                onDelete={() => onConversationDelete(conversation.id)}
                onSaveRename={() => handleSaveRename(conversation.id)}
                onCancelRename={handleCancelRename}
                onEditTitleChange={setEditTitle}
              />
            ))}
          </div>
        )}
      </div>

      {/* User Profile */}
      {user && (
        <div className="p-4 border-t">
          <Card className="p-3">
            <div className="flex items-center gap-3 mb-3">
              <Avatar className="w-8 h-8">
                <AvatarImage src={`https://avatar.vercel.sh/${user.email}`} />
                <AvatarFallback>{getInitials(user.name)}</AvatarFallback>
              </Avatar>
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium truncate">{user.name}</p>
                <p className="text-xs text-muted-foreground truncate">{user.email}</p>
              </div>
            </div>

            {/* Quota Status */}
            <div className="mb-3">
              <div className="flex justify-between text-xs mb-1">
                <span>API Usage</span>
                <span>{Math.round((user.api_quota_used / user.api_quota_limit) * 100)}%</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-1.5">
                <div
                  className="bg-loni-primary h-1.5 rounded-full transition-all"
                  style={{
                    width: `${Math.min((user.api_quota_used / user.api_quota_limit) * 100, 100)}%`
                  }}
                />
              </div>
            </div>

            <div className="flex gap-1">
              <Button variant="ghost" size="sm" className="flex-1">
                <User className="w-4 h-4 mr-1" />
                Profile
              </Button>
              <Button variant="ghost" size="sm" className="flex-1">
                <Settings className="w-4 h-4 mr-1" />
                Settings
              </Button>
              <Button 
                variant="ghost" 
                size="sm" 
                onClick={onLogout}
                className="text-red-600 hover:text-red-700 hover:bg-red-50"
              >
                <LogOut className="w-4 h-4" />
              </Button>
            </div>
          </Card>
        </div>
      )}
    </div>
  )
}

interface ConversationItemProps {
  conversation: Conversation
  isActive: boolean
  isEditing: boolean
  editTitle: string
  onSelect: () => void
  onRename: () => void
  onDelete: () => void
  onSaveRename: () => void
  onCancelRename: () => void
  onEditTitleChange: (title: string) => void
}

function ConversationItem({
  conversation,
  isActive,
  isEditing,
  editTitle,
  onSelect,
  onRename,
  onDelete,
  onSaveRename,
  onCancelRename,
  onEditTitleChange
}: ConversationItemProps) {
  const [showActions, setShowActions] = useState(false)

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      onSaveRename()
    } else if (e.key === 'Escape') {
      onCancelRename()
    }
  }

  return (
    <div
      className={`group relative p-3 rounded-lg cursor-pointer transition-colors hover:bg-accent ${
        isActive ? 'bg-loni-primary/10 border border-loni-primary/20' : ''
      }`}
      onClick={!isEditing ? onSelect : undefined}
      onMouseEnter={() => setShowActions(true)}
      onMouseLeave={() => setShowActions(false)}
    >
      <div className="flex items-center gap-3">
        <div className="flex-1 min-w-0">
          {isEditing ? (
            <input
              type="text"
              value={editTitle}
              onChange={(e) => onEditTitleChange(e.target.value)}
              onKeyDown={handleKeyDown}
              onBlur={onSaveRename}
              className="w-full text-sm font-medium bg-transparent border-none outline-none focus:bg-white focus:border focus:border-loni-primary rounded px-1"
              autoFocus
              onClick={(e) => e.stopPropagation()}
            />
          ) : (
            <p className="text-sm font-medium truncate">
              {truncateText(conversation.title, 50)}
            </p>
          )}
          
          <div className="flex items-center gap-2 mt-1">
            <span className="text-xs text-muted-foreground">
              {formatRelativeTime(conversation.updated_at)}
            </span>
            <Badge variant="secondary" className="text-xs py-0 px-1">
              {conversation.model_name}
            </Badge>
            {conversation.rag_enabled && (
              <Badge variant="ai-secondary" className="text-xs py-0 px-1">
                RAG
              </Badge>
            )}
          </div>
        </div>

        {/* Actions */}
        {showActions && !isEditing && (
          <div className="flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
            <Button
              variant="ghost"
              size="icon"
              className="w-6 h-6"
              onClick={(e) => {
                e.stopPropagation()
                onRename()
              }}
            >
              <Edit className="w-3 h-3" />
            </Button>
            <Button
              variant="ghost"
              size="icon"
              className="w-6 h-6 text-red-600 hover:text-red-700"
              onClick={(e) => {
                e.stopPropagation()
                onDelete()
              }}
            >
              <Trash2 className="w-3 h-3" />
            </Button>
          </div>
        )}
      </div>
    </div>
  )
}