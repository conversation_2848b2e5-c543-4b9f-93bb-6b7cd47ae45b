"""
Integration tests for API endpoints.

This module tests the API endpoints with real database interactions.
"""

import pytest
from httpx import Async<PERSON><PERSON>
from uuid import uuid4

from tests.conftest import assert_response_success, assert_response_error


class TestHealthEndpoint:
    """Test health check endpoint."""
    
    @pytest.mark.asyncio
    async def test_health_check(self, async_client: AsyncClient):
        """Test health check endpoint."""
        response = await async_client.get("/health")
        
        assert_response_success(response)
        data = response.json()
        
        assert "status" in data
        assert data["status"] == "healthy"
        assert "timestamp" in data
        assert "version" in data


class TestUserEndpoints:
    """Test user management endpoints."""
    
    @pytest.mark.asyncio
    async def test_create_user(self, async_client: AsyncClient):
        """Test user creation endpoint."""
        user_data = {
            "email": "<EMAIL>",
            "password": "securepassword123",
            "name": "New User"
        }
        
        response = await async_client.post("/users/", json=user_data)
        
        assert_response_success(response, 201)
        data = response.json()
        
        assert data["email"] == user_data["email"]
        assert data["name"] == user_data["name"]
        assert "id" in data
        assert "hashed_password" not in data  # Should not expose password
    
    @pytest.mark.asyncio
    async def test_create_user_duplicate_email(self, async_client: AsyncClient, test_user):
        """Test user creation with duplicate email."""
        user_data = {
            "email": test_user.email,  # Use existing user's email
            "password": "securepassword123",
            "name": "Duplicate User"
        }
        
        response = await async_client.post("/users/", json=user_data)
        
        assert_response_error(response, 400)
        data = response.json()
        assert "email" in data["detail"].lower()
    
    @pytest.mark.asyncio
    async def test_get_user_profile(self, async_client: AsyncClient, test_user):
        """Test getting user profile."""
        # This would require authentication in a real implementation
        response = await async_client.get(f"/users/{test_user.id}")
        
        # For now, test the endpoint structure
        # In a real implementation, this would require proper auth headers
        assert response.status_code in [200, 401, 404]
    
    @pytest.mark.asyncio
    async def test_update_user_profile(self, async_client: AsyncClient, test_user):
        """Test updating user profile."""
        update_data = {
            "name": "Updated Name",
            "bio": "Updated bio"
        }
        
        response = await async_client.patch(f"/users/{test_user.id}", json=update_data)
        
        # For now, test the endpoint structure
        # In a real implementation, this would require proper auth
        assert response.status_code in [200, 401, 404]


class TestConversationEndpoints:
    """Test conversation management endpoints."""
    
    @pytest.mark.asyncio
    async def test_create_conversation(self, async_client: AsyncClient, test_user):
        """Test conversation creation."""
        conversation_data = {
            "title": "New Conversation",
            "model_name": "gpt-4",
            "temperature": 0.7,
            "rag_enabled": True
        }
        
        # This would require authentication
        response = await async_client.post(
            "/conversations/",
            json=conversation_data,
            headers={"Authorization": f"Bearer fake-token-{test_user.id}"}
        )
        
        # For now, test endpoint exists
        assert response.status_code in [201, 401, 422]
    
    @pytest.mark.asyncio
    async def test_list_conversations(self, async_client: AsyncClient, test_user):
        """Test listing user conversations."""
        response = await async_client.get(
            "/conversations/",
            headers={"Authorization": f"Bearer fake-token-{test_user.id}"}
        )
        
        # For now, test endpoint exists
        assert response.status_code in [200, 401]
    
    @pytest.mark.asyncio
    async def test_get_conversation(self, async_client: AsyncClient, test_conversation):
        """Test getting specific conversation."""
        response = await async_client.get(
            f"/conversations/{test_conversation.id}",
            headers={"Authorization": f"Bearer fake-token-{test_conversation.user_id}"}
        )
        
        # For now, test endpoint exists
        assert response.status_code in [200, 401, 404]
    
    @pytest.mark.asyncio
    async def test_delete_conversation(self, async_client: AsyncClient, test_conversation):
        """Test deleting conversation."""
        response = await async_client.delete(
            f"/conversations/{test_conversation.id}",
            headers={"Authorization": f"Bearer fake-token-{test_conversation.user_id}"}
        )
        
        # For now, test endpoint exists
        assert response.status_code in [204, 401, 404]


class TestChatEndpoints:
    """Test chat functionality endpoints."""
    
    @pytest.mark.asyncio
    async def test_send_message(self, async_client: AsyncClient, test_conversation):
        """Test sending a chat message."""
        message_data = {
            "content": "Hello, how are you?",
            "role": "user"
        }
        
        response = await async_client.post(
            f"/chat/{test_conversation.id}/messages",
            json=message_data,
            headers={"Authorization": f"Bearer fake-token-{test_conversation.user_id}"}
        )
        
        # For now, test endpoint exists
        assert response.status_code in [201, 401, 404, 422]
    
    @pytest.mark.asyncio
    async def test_get_conversation_messages(self, async_client: AsyncClient, test_conversation):
        """Test getting conversation messages."""
        response = await async_client.get(
            f"/chat/{test_conversation.id}/messages",
            headers={"Authorization": f"Bearer fake-token-{test_conversation.user_id}"}
        )
        
        # For now, test endpoint exists
        assert response.status_code in [200, 401, 404]
    
    @pytest.mark.asyncio
    async def test_chat_completion(self, async_client: AsyncClient, test_conversation):
        """Test chat completion endpoint."""
        completion_data = {
            "messages": [
                {"role": "user", "content": "Hello"}
            ],
            "model": "gpt-4",
            "temperature": 0.7
        }
        
        response = await async_client.post(
            f"/chat/{test_conversation.id}/completions",
            json=completion_data,
            headers={"Authorization": f"Bearer fake-token-{test_conversation.user_id}"}
        )
        
        # For now, test endpoint exists
        assert response.status_code in [200, 401, 404, 422]
    
    @pytest.mark.asyncio
    async def test_streaming_chat(self, async_client: AsyncClient, test_conversation):
        """Test streaming chat endpoint."""
        completion_data = {
            "messages": [
                {"role": "user", "content": "Tell me a story"}
            ],
            "model": "gpt-4",
            "stream": True
        }
        
        response = await async_client.post(
            f"/chat/{test_conversation.id}/stream",
            json=completion_data,
            headers={"Authorization": f"Bearer fake-token-{test_conversation.user_id}"}
        )
        
        # For now, test endpoint exists
        assert response.status_code in [200, 401, 404, 422]


class TestDocumentEndpoints:
    """Test document management endpoints."""
    
    @pytest.mark.asyncio
    async def test_upload_document(self, async_client: AsyncClient, test_user):
        """Test document upload."""
        # Simulate file upload
        files = {
            "file": ("test.txt", "This is test content", "text/plain")
        }
        data = {
            "title": "Test Document"
        }
        
        response = await async_client.post(
            "/documents/upload",
            files=files,
            data=data,
            headers={"Authorization": f"Bearer fake-token-{test_user.id}"}
        )
        
        # For now, test endpoint exists
        assert response.status_code in [201, 401, 422]
    
    @pytest.mark.asyncio
    async def test_list_documents(self, async_client: AsyncClient, test_user):
        """Test listing user documents."""
        response = await async_client.get(
            "/documents/",
            headers={"Authorization": f"Bearer fake-token-{test_user.id}"}
        )
        
        # For now, test endpoint exists
        assert response.status_code in [200, 401]
    
    @pytest.mark.asyncio
    async def test_get_document(self, async_client: AsyncClient, test_document):
        """Test getting specific document."""
        response = await async_client.get(
            f"/documents/{test_document.id}",
            headers={"Authorization": f"Bearer fake-token-{test_document.user_id}"}
        )
        
        # For now, test endpoint exists
        assert response.status_code in [200, 401, 404]
    
    @pytest.mark.asyncio
    async def test_delete_document(self, async_client: AsyncClient, test_document):
        """Test deleting document."""
        response = await async_client.delete(
            f"/documents/{test_document.id}",
            headers={"Authorization": f"Bearer fake-token-{test_document.user_id}"}
        )
        
        # For now, test endpoint exists
        assert response.status_code in [204, 401, 404]


class TestModelEndpoints:
    """Test AI model management endpoints."""
    
    @pytest.mark.asyncio
    async def test_list_available_models(self, async_client: AsyncClient):
        """Test listing available AI models."""
        response = await async_client.get("/models/")
        
        # For now, test endpoint exists
        assert response.status_code in [200, 401]
    
    @pytest.mark.asyncio
    async def test_get_model_info(self, async_client: AsyncClient, test_ai_model):
        """Test getting model information."""
        response = await async_client.get(f"/models/{test_ai_model.name}")
        
        # For now, test endpoint exists
        assert response.status_code in [200, 404]
    
    @pytest.mark.asyncio
    async def test_download_model(self, async_client: AsyncClient):
        """Test downloading a model (Ollama)."""
        download_data = {
            "model_name": "llama3.2:latest"
        }
        
        response = await async_client.post(
            "/models/download",
            json=download_data,
            headers={"Authorization": "Bearer admin-token"}
        )
        
        # For now, test endpoint exists
        assert response.status_code in [202, 401, 422]


class TestErrorHandling:
    """Test API error handling."""
    
    @pytest.mark.asyncio
    async def test_404_endpoint(self, async_client: AsyncClient):
        """Test 404 for non-existent endpoint."""
        response = await async_client.get("/nonexistent")
        
        assert response.status_code == 404
    
    @pytest.mark.asyncio
    async def test_invalid_uuid(self, async_client: AsyncClient):
        """Test invalid UUID handling."""
        response = await async_client.get("/conversations/invalid-uuid")
        
        assert response.status_code in [400, 422]
    
    @pytest.mark.asyncio
    async def test_malformed_json(self, async_client: AsyncClient):
        """Test malformed JSON handling."""
        response = await async_client.post(
            "/conversations/",
            content="invalid json",
            headers={"Content-Type": "application/json"}
        )
        
        assert response.status_code == 422
    
    @pytest.mark.asyncio
    async def test_missing_required_fields(self, async_client: AsyncClient):
        """Test missing required fields."""
        incomplete_data = {
            "name": "Test User"
            # Missing email and password
        }
        
        response = await async_client.post("/users/", json=incomplete_data)
        
        assert response.status_code == 422
        data = response.json()
        assert "detail" in data
