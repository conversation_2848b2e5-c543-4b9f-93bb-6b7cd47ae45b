{"name": "AI Agent Mastery Local Prototype", "nodes": [{"parameters": {"jsonMode": "expressionData", "jsonData": "={{ $json.data || $json.text || $json.concatenated_data }}", "options": {"metadata": {"metadataValues": [{"name": "=file_id", "value": "={{ $('Set File ID').first().json.file_id }}"}, {"name": "file_title", "value": "={{ $('Set File ID').first().json.file_title }}"}]}}}, "id": "0238e274-178f-4d94-9ef5-f60347d31f46", "name": "Default Data Loader", "type": "@n8n/n8n-nodes-langchain.documentDefaultDataLoader", "typeVersion": 1, "position": [1000, 1140]}, {"parameters": {"content": "## Agent Tools for Agentic RAG", "height": 409, "width": 583, "color": 4}, "id": "9842e717-965d-466b-8116-25da10fa20c6", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-680, 120]}, {"parameters": {"content": "## Tool to Add a Google Drive File to Vector DB", "height": 867, "width": 3073, "color": 5}, "id": "a6c7219f-bd64-42ce-9d30-f04b460a5c8c", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-1740, 540]}, {"parameters": {"operation": "text", "options": {}}, "id": "157deff1-0d31-4569-b9e1-29978577bcae", "name": "Extract Document Text", "type": "n8n-nodes-base.extractFromFile", "typeVersion": 1, "position": [200, 1140], "alwaysOutputData": true}, {"parameters": {}, "id": "220ae3de-f15b-49f3-808a-cdad49a8ce1f", "name": "Postgres Chat Memory", "type": "@n8n/n8n-nodes-langchain.memoryPostgresChat", "typeVersion": 1, "position": [-1400, 80], "notesInFlow": false, "credentials": {"postgres": {"id": "UaTmh0frrACTMPxG", "name": "Postgres account"}}}, {"parameters": {"assignments": {"assignments": [{"id": "10646eae-ae46-4327-a4dc-9987c2d76173", "name": "file_id", "value": "={{ $json.path }}", "type": "string"}, {"id": "f4536df5-d0b1-4392-bf17-b8137fb31a44", "name": "file_type", "value": "={{ $json.path.split(/[\\\\/]/).pop().split('.').pop(); }}", "type": "string"}, {"id": "77d782de-169d-4a46-8a8e-a3831c04d90f", "name": "file_title", "value": "={{ $json.path.split(/[\\\\/]/).pop().split('.').slice(0, -1).join('.'); }}", "type": "string"}]}, "options": {}}, "id": "c8cf0d30-3ef6-439f-a9dd-a1d90d7627b9", "name": "Set File ID", "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [-1320, 820]}, {"parameters": {"content": "## RAG AI Agent with Cha<PERSON> Interface", "height": 845, "width": 1036}, "id": "de853aeb-7b75-4b9e-8132-d83f033dbf1b", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-1740, -320]}, {"parameters": {"options": {}}, "id": "482cec38-5ec7-4e47-ad90-3e5f56e6a996", "name": "Respond to Webhook", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1.1, "position": [-880, -220]}, {"parameters": {"assignments": {"assignments": [{"id": "9a9a245e-f1a1-4282-bb02-a81ffe629f0f", "name": "chatInput", "value": "={{ $json?.chatInput || $json.body.chatInput }}", "type": "string"}, {"id": "b80831d8-c653-4203-8706-adedfdb98f77", "name": "sessionId", "value": "={{ $json?.sessionId || $json.body.sessionId}}", "type": "string"}]}, "options": {}}, "id": "cb7d6070-6d6b-42a5-b180-4e7fb02f323a", "name": "<PERSON>", "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [-1440, -220]}, {"parameters": {"public": true, "options": {}}, "id": "869bdd71-68db-493a-a1ef-fcad02c5b0d7", "name": "When chat message received", "type": "@n8n/n8n-nodes-langchain.chatTrigger", "typeVersion": 1.1, "position": [-1700, -220], "webhookId": "fc3025fa-01bd-4fb2-a32e-cd9d76f20a72"}, {"parameters": {"httpMethod": "POST", "path": "9dfe04b7-55f0-4e77-938a-64c364c3337d", "responseMode": "responseNode", "options": {}}, "id": "fd6d55ea-7e5c-4ed8-b526-412601917a2f", "name": "Webhook", "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [-1700, -20], "webhookId": "9dfe04b7-55f0-4e77-938a-64c364c3337d"}, {"parameters": {"operation": "pdf", "options": {}}, "id": "88689eb6-a7f1-4032-af1d-139ae99ca5bb", "name": "Extract PDF Text", "type": "n8n-nodes-base.extractFromFile", "typeVersion": 1, "position": [200, 580]}, {"parameters": {"aggregate": "aggregateAllItemData", "options": {}}, "id": "4bb06a09-ed70-4e93-a8d2-8af1344ca549", "name": "Aggregate", "type": "n8n-nodes-base.aggregate", "typeVersion": 1, "position": [240, 760]}, {"parameters": {"fieldsToSummarize": {"values": [{"aggregation": "concatenate", "field": "data"}]}, "options": {}}, "id": "1d3946df-2c6b-4fee-98d6-09b53c257b6d", "name": "Summarize", "type": "n8n-nodes-base.summarize", "typeVersion": 1, "position": [440, 840]}, {"parameters": {"promptType": "define", "text": "={{ $json.chatInput }}", "options": {"systemMessage": "=You are an intelligent AI assistant with advanced research and analysis capabilities. You excel at retrieving, processing, and synthesizing information from diverse document types to provide accurate, comprehensive answers. You are intuitive, friendly, and proactive, always aiming to deliver the most relevant information while maintaining clarity and precision.\n\nGoal:\n\nYour goal is to provide accurate, relevant, and well-sourced information by utilizing your suite of tools. You aim to streamline the user's research process, offer insightful analysis, and ensure they receive reliable answers to their queries. You help users by delivering thoughtful, well-researched responses that save them time and enhance their understanding of complex topics.\n\nTool Instructions:\n\n- Always begin with Memory: Before doing anything, use the memory tool to fetch relevant memories. You prioritize using this tool first and you always use it if the answer needs to be personalized to the user in ANY way!\n\n- Document Retrieval Strategy:\nFor general information queries: Use RAG first. Then analyze individual documents if RAG is insufficient.\nFor numerical analysis or data queries: Use SQL on tabular data\n\n- Knowledge Boundaries: Explicitly acknowledge when you cannot find an answer in the available resources.\n\nFor the rest of the tools, use them as necessary based on their descriptions.\n\nOutput Format:\n\nStructure your responses to be clear, concise, and well-organized. Begin with a direct answer to the user's query when possible, followed by supporting information and your reasoning process.\n\nMisc Instructions:\n\n- Query Clarification:\nRequest clarification when queries are ambiguous - but check memories first because that might clarify things.\n\nData Analysis Best Practices:\n- Explain your analytical approach when executing code or SQL queries\nPresent numerical findings with appropriate context and units\n\n- Source Prioritization:\nPrioritize the most recent and authoritative documents when information varies\n\n- Transparency About Limitations:\nClearly state when information appears outdated or incomplete\nAcknowledge when web search might provide more current information than your document corpus"}}, "id": "d4aa51c3-c767-4d99-9764-dded44354211", "name": "RAG AI Agent", "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.6, "position": [-1220, -220]}, {"parameters": {"rules": {"values": [{"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 1}, "conditions": [{"leftValue": "={{ $('Set File ID').item.json.file_type }}", "rightValue": "pdf", "operator": {"type": "string", "operation": "equals"}}], "combinator": "and"}}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 1}, "conditions": [{"id": "2ae7faa7-a936-4621-a680-60c512163034", "leftValue": "={{ $('Set File ID').item.json.file_type }}", "rightValue": "xlsx", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 1}, "conditions": [{"id": "fc193b06-363b-4699-a97d-e5a850138b0e", "leftValue": "={{ $('Set File ID').item.json.file_type }}", "rightValue": "=csv", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 1}, "conditions": [{"id": "b69f5605-0179-4b02-9a32-e34bb085f82d", "leftValue": "={{ $('Set File ID').item.json.file_type }}", "rightValue": "txt", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}}]}, "options": {"fallbackOutput": "extra"}}, "id": "1cd5daa2-ebfd-4521-9554-722a81d3a870", "name": "Switch", "type": "n8n-nodes-base.switch", "typeVersion": 3, "position": [-460, 800]}, {"parameters": {"operation": "xlsx", "options": {}}, "id": "92fb1820-f646-400a-a7ef-c56bf9e86952", "name": "Extract from Excel", "type": "n8n-nodes-base.extractFromFile", "typeVersion": 1, "position": [20, 760]}, {"parameters": {"assignments": {"assignments": [{"id": "f422e2e0-381c-46ea-8f38-3f58c501d8b9", "name": "schema", "value": "={{ $('Extract from Excel').isExecuted ? $('Extract from Excel').first().json.keys().toJsonString() : $('Extract from CSV').first().json.keys().toJsonString() }}", "type": "string"}, {"id": "bb07c71e-5b60-4795-864c-cc3845b6bc46", "name": "data", "value": "={{ $json.concatenated_data }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [880, 700], "id": "6843185b-b928-46fb-9b7c-6223778c9792", "name": "<PERSON>"}, {"parameters": {"options": {}}, "type": "n8n-nodes-base.extractFromFile", "typeVersion": 1, "position": [20, 940], "id": "d75b8325-9352-4b41-9bb3-e69f00d42492", "name": "Extract from CSV"}, {"parameters": {"content": "## Run Each Node Once to Set Up Database Tables", "height": 380, "width": 1040, "color": 3}, "type": "n8n-nodes-base.stickyNote", "position": [-1740, -720], "typeVersion": 1, "id": "4624a8ca-139b-41f3-b4f2-11ebffff2546", "name": "Sticky Note3"}, {"parameters": {"operation": "execute<PERSON>uery", "query": "CREATE TABLE document_metadata (\n    id TEXT PRIMARY KEY,\n    title TEXT,\n    created_at TIMESTAMP DEFAULT NOW(),\n    schema TEXT\n);", "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.5, "position": [-1600, -580], "id": "b1595252-950f-4955-978b-f33dbde1f49b", "name": "Create Document Metadata Table", "credentials": {"postgres": {"id": "UaTmh0frrACTMPxG", "name": "Postgres account"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "CREATE TABLE document_rows (\n    id SERIAL PRIMARY KEY,\n    dataset_id TEXT REFERENCES document_metadata(id),\n    row_data JSONB  -- Store the actual row data\n);", "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.5, "position": [-1000, -580], "id": "d57bb4cd-16d7-4d40-8349-929a0956fc58", "name": "Create Document Rows Table (for Tabular Data)", "credentials": {"postgres": {"id": "UaTmh0frrACTMPxG", "name": "Postgres account"}}}, {"parameters": {"descriptionType": "manual", "toolDescription": "Use this tool to fetch all available documents, including the table schema if the file is a CSV or Excel file.", "operation": "select", "schema": {"__rl": true, "mode": "list", "value": "public"}, "table": {"__rl": true, "value": "document_metadata", "mode": "list", "cachedResultName": "document_metadata"}, "returnAll": true, "options": {}}, "type": "n8n-nodes-base.postgresTool", "typeVersion": 2.5, "position": [-660, 380], "id": "c2ba996f-50fe-4e6c-b877-389aececdc7a", "name": "List Documents", "credentials": {"postgres": {"id": "UaTmh0frrACTMPxG", "name": "Postgres account"}}}, {"parameters": {"descriptionType": "manual", "toolDescription": "Given a file ID, fetches the text from the document.", "operation": "execute<PERSON>uery", "query": "SELECT \n    string_agg(text, ' ') as document_text\nFROM documents_pg\n  WHERE metadata->>'file_id' = $1\nGROUP BY metadata->>'file_id';", "options": {"queryReplacement": "={{ $fromAI('file_id') }}"}}, "type": "n8n-nodes-base.postgresTool", "typeVersion": 2.5, "position": [-560, 260], "id": "bcf82f16-f400-4ac9-9070-4b9d864ff55c", "name": "Get File Contents", "credentials": {"postgres": {"id": "UaTmh0frrACTMPxG", "name": "Postgres account"}}}, {"parameters": {"descriptionType": "manual", "toolDescription": "Run a SQL query - use this to query from the document_rows table once you know the file ID (which is the file path) you are querying. dataset_id is the file_id (file path) and you are always using the row_data for filtering, which is a jsonb field that has all the keys from the file schema given in the document_metadata table.\n\nExample query:\n\nSELECT AVG((row_data->>'revenue')::numeric)\nFROM document_rows\nWHERE dataset_id = '/data/shared/document.csv';\n\nExample query 2:\n\nSELECT \n    row_data->>'category' as category,\n    SUM((row_data->>'sales')::numeric) as total_sales\nFROM dataset_rows\nWHERE dataset_id = '/data/shared/document2.csv'\nGROUP BY row_data->>'category';", "operation": "execute<PERSON>uery", "query": "{{ $fromAI('sql_query') }}", "options": {}}, "type": "n8n-nodes-base.postgresTool", "typeVersion": 2.5, "position": [-460, 380], "id": "3b580994-7db3-40fa-850a-b852f36e0245", "name": "Query Document Rows", "credentials": {"postgres": {"id": "UaTmh0frrACTMPxG", "name": "Postgres account"}}}, {"parameters": {"options": {"reset": false}}, "type": "n8n-nodes-base.splitInBatches", "typeVersion": 3, "position": [-1500, 660], "id": "54e2c70e-786b-43f7-957b-0de940c237db", "name": "Loop Over Items"}, {"parameters": {"operation": "upsert", "schema": {"__rl": true, "mode": "list", "value": "public"}, "table": {"__rl": true, "value": "document_metadata", "mode": "list", "cachedResultName": "document_metadata"}, "columns": {"mappingMode": "defineBelow", "value": {"id": "={{ $('Set File ID').item.json.file_id }}", "title": "={{ $('Set File ID').item.json.file_title }}"}, "matchingColumns": ["id"], "schema": [{"id": "id", "displayName": "id", "required": true, "defaultMatch": true, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "title", "displayName": "title", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": false}, {"id": "url", "displayName": "url", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": false, "removed": true}, {"id": "created_at", "displayName": "created_at", "required": false, "defaultMatch": false, "display": true, "type": "dateTime", "canBeUsedToMatch": false}, {"id": "schema", "displayName": "schema", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": false, "removed": true}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.5, "position": [-820, 680], "id": "ef257a11-813d-429f-a5f7-96cc42129b9c", "name": "Insert Document Metadata", "executeOnce": true, "credentials": {"postgres": {"id": "UaTmh0frrACTMPxG", "name": "Postgres account"}}}, {"parameters": {"schema": {"__rl": true, "mode": "list", "value": "public"}, "table": {"__rl": true, "value": "document_rows", "mode": "list", "cachedResultName": "document_rows"}, "columns": {"mappingMode": "defineBelow", "value": {"dataset_id": "={{ $('Set File ID').item.json.file_id }}", "row_data": "={{ $json.toJsonString().replaceAll(/'/g, \"''\") }}"}, "matchingColumns": ["id"], "schema": [{"id": "id", "displayName": "id", "required": false, "defaultMatch": true, "display": true, "type": "number", "canBeUsedToMatch": true, "removed": true}, {"id": "dataset_id", "displayName": "dataset_id", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "row_data", "displayName": "row_data", "required": false, "defaultMatch": false, "display": true, "type": "object", "canBeUsedToMatch": true, "removed": false}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.5, "position": [240, 940], "id": "8fbb400a-9dff-422c-8062-2fb24ff29fbc", "name": "Insert Table Rows", "credentials": {"postgres": {"id": "UaTmh0frrACTMPxG", "name": "Postgres account"}}}, {"parameters": {"operation": "upsert", "schema": {"__rl": true, "mode": "list", "value": "public"}, "table": {"__rl": true, "value": "document_metadata", "mode": "list", "cachedResultName": "document_metadata"}, "columns": {"mappingMode": "defineBelow", "value": {"id": "={{ $('Set File ID').item.json.file_id }}", "schema": "={{ $json.schema }}"}, "matchingColumns": ["id"], "schema": [{"id": "id", "displayName": "id", "required": true, "defaultMatch": true, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "title", "displayName": "title", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": false, "removed": true}, {"id": "url", "displayName": "url", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": false, "removed": true}, {"id": "created_at", "displayName": "created_at", "required": false, "defaultMatch": false, "display": true, "type": "dateTime", "canBeUsedToMatch": false}, {"id": "schema", "displayName": "schema", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": false, "removed": false}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.5, "position": [1100, 700], "id": "984a30d0-9191-48a2-ba95-5fe004a87bbe", "name": "Update <PERSON><PERSON>a for Document Metadata", "credentials": {"postgres": {"id": "UaTmh0frrACTMPxG", "name": "Postgres account"}}}, {"parameters": {"triggerOn": "folder", "path": "/data/shared", "events": ["add", "change"], "options": {"followSymlinks": true, "usePolling": true}}, "type": "n8n-nodes-base.localFileTrigger", "typeVersion": 1, "position": [-1700, 660], "id": "3be25893-a560-4048-b52e-a79aaf5ca589", "name": "Local File Trigger"}, {"parameters": {"fileSelector": "={{ $('Set File ID').item.json.file_id }}", "options": {"dataPropertyName": "=data"}}, "type": "n8n-nodes-base.readWriteFile", "typeVersion": 1, "position": [-660, 820], "id": "e45b7093-5c4c-42b2-92be-502190cc8cc7", "name": "Read/Write Files from Disk"}, {"parameters": {"model": "nomic-embed-text:latest"}, "type": "@n8n/n8n-nodes-langchain.embeddingsOllama", "typeVersion": 1, "position": [760, 1140], "id": "9a56dd2f-6ed1-4e20-aabb-963a3d4cbd20", "name": "Embeddings <PERSON><PERSON><PERSON>", "credentials": {"ollamaApi": {"id": "GwjiKiEsG5HnTaAf", "name": "Ollama account"}}}, {"parameters": {"model": "nomic-embed-text:latest"}, "type": "@n8n/n8n-nodes-langchain.embeddingsOllama", "typeVersion": 1, "position": [-280, 380], "id": "9bdeb5d6-e8bc-4784-8e9c-471b01e66dca", "name": "Embeddings Ollama1", "credentials": {"ollamaApi": {"id": "GwjiKiEsG5HnTaAf", "name": "Ollama account"}}}, {"parameters": {"chunkSize": 400, "options": {}}, "type": "@n8n/n8n-nodes-langchain.textSplitterRecursiveCharacterTextSplitter", "typeVersion": 1, "position": [900, 1260], "id": "18da3757-40dc-4950-9f0f-6ba353b5f098", "name": "Recursive Character Text Splitter"}, {"parameters": {"model": {"__rl": true, "value": "qwen2.5:14b-8k", "mode": "list", "cachedResultName": "qwen2.5:14b-8k"}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1.2, "position": [-1540, 80], "id": "6f7d3e55-75a9-4f7c-ab35-40bd28e7861e", "name": "Ollama (Change Base URL)", "credentials": {"openAiApi": {"id": "yjDbkWOXNXC959ei", "name": "OpenAi account"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "DO $$\nBEGIN\n    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'documents_pg') THEN\n        EXECUTE 'DELETE FROM documents_pg WHERE metadata->>''file_id'' LIKE ''%' || $1 || '%''';\n    END IF;\nEND\n$$;", "options": {"queryReplacement": "={{ $json.file_id }}"}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.5, "position": [-1140, 680], "id": "1bae62f9-3f56-4210-9c7b-ffc1e3f37084", "name": "Delete Old Doc Records", "credentials": {"postgres": {"id": "UaTmh0frrACTMPxG", "name": "Postgres account"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "DELETE FROM document_rows\nWHERE dataset_id LIKE '%' || $1 || '%';", "options": {"queryReplacement": "={{ $('Set File ID').item.json.file_id }}"}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.5, "position": [-980, 820], "id": "9c314119-7bfc-4f8b-8e32-59e1c2417bff", "name": "Delete Old Data Records", "credentials": {"postgres": {"id": "UaTmh0frrACTMPxG", "name": "Postgres account"}}}, {"parameters": {"mode": "insert", "tableName": "documents_pg", "options": {}}, "type": "@n8n/n8n-nodes-langchain.vectorStorePGVector", "typeVersion": 1, "position": [880, 920], "id": "bb036109-62dc-4a08-bc28-790dbe5351a8", "name": "Postgres PGVector Store", "credentials": {"postgres": {"id": "UaTmh0frrACTMPxG", "name": "Postgres account"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "-- Create a table to store your documents\ncreate table memories (\n  id bigserial primary key,\n  text text, -- corresponds to Document.pageContent\n  metadata jsonb, -- corresponds to Document.metadata\n  embedding vector(768) -- 768 works for nomic-embed-text embeddings, change if needed\n);\n  \n-- Create a function to search for documents\ncreate function match_memories (\n  query_embedding vector(768),\n  match_count int default null,\n  filter jsonb DEFAULT '{}'\n) returns table (\n  id bigint,\n  text text,\n  metadata jsonb,\n  similarity float\n)\nlanguage plpgsql\nas $$\n#variable_conflict use_column\nbegin\n  return query\n  select\n    id,\n    text,\n    metadata,\n    1 - (memories.embedding <=> query_embedding) as similarity\n  from memories\n  where metadata @> filter\n  order by memories.embedding <=> query_embedding\n  limit match_count;\nend;\n$$;", "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.5, "position": [-1300, -580], "id": "fe816080-ce01-43da-84db-1b85ae00d887", "name": "Create Memories Table and Match Function", "credentials": {"postgres": {"id": "UaTmh0frrACTMPxG", "name": "Postgres account"}}}, {"parameters": {"mode": "retrieve-as-tool", "toolName": "memories", "toolDescription": "Use this tool to fetch memories from previous conversations if needed to answer a question for the user or continue the conversation.", "tableName": "memories", "options": {}}, "type": "@n8n/n8n-nodes-langchain.vectorStorePGVector", "typeVersion": 1, "position": [-1000, 220], "id": "87d6edd2-3708-4c16-902c-e97a1579f983", "name": "Retrieve Memories Tool", "credentials": {"postgres": {"id": "UaTmh0frrACTMPxG", "name": "Postgres account"}}}, {"parameters": {"model": "nomic-embed-text:latest"}, "type": "@n8n/n8n-nodes-langchain.embeddingsOllama", "typeVersion": 1, "position": [-900, 360], "id": "3820ca78-9c8f-48f4-aa14-4d5838fb86d0", "name": "Embeddings Ollama2", "credentials": {"ollamaApi": {"id": "GwjiKiEsG5HnTaAf", "name": "Ollama account"}}}, {"parameters": {"mode": "retrieve-as-tool", "toolName": "documents", "toolDescription": "Use RAG to look up information in the knowledgebase.", "tableName": "documents_pg", "options": {}}, "type": "@n8n/n8n-nodes-langchain.vectorStorePGVector", "typeVersion": 1, "position": [-400, 200], "id": "72e8f2ca-3d67-4bb7-bb60-00340aefa51f", "name": "Document RAG Tool", "credentials": {"postgres": {"id": "UaTmh0frrACTMPxG", "name": "Postgres account"}}}, {"parameters": {"content": "## Workflow Agent Tools", "height": 380, "width": 1660, "color": 4}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-680, -720], "id": "e7414528-c181-459a-990e-5312124b035a", "name": "Sticky Note7"}, {"parameters": {"rules": {"values": [{"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"leftValue": "={{ $json.tool_type }}", "rightValue": "image_analysis", "operator": {"type": "string", "operation": "equals"}}], "combinator": "and"}}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "a50a5a5a-cc87-4654-97ea-05090efdb416", "leftValue": "={{ $json.tool_type }}", "rightValue": "web_search", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}}]}, "options": {"fallbackOutput": "none"}}, "type": "n8n-nodes-base.switch", "typeVersion": 3.2, "position": [-380, -600], "id": "b7ab1ae6-6433-4ce2-9c8c-7e846c4671c9", "name": "Determine Tool Type"}, {"parameters": {"workflowInputs": {"values": [{"name": "tool_type"}, {"name": "query"}, {"name": "image_path"}]}}, "type": "n8n-nodes-base.executeWorkflowTrigger", "typeVersion": 1.1, "position": [-600, -600], "id": "9fcc5dfa-75b8-4314-b225-2eb0bc398b9b", "name": "Tool Start"}, {"parameters": {"name": "web_search", "description": "Call this tool to do an advanced web search based on a query you define.\n\nThis tool will return the contents of the 3 most relevant web pages from the search.", "workflowId": {"__rl": true, "value": "={{ $workflow.id }}", "mode": "id"}, "workflowInputs": {"mappingMode": "defineBelow", "value": {"query": "={{ $fromAI('query') }}", "tool_type": "web_search"}, "matchingColumns": [], "schema": [{"id": "tool_type", "displayName": "tool_type", "required": false, "defaultMatch": false, "display": true, "canBeUsedToMatch": true, "type": "string", "removed": false}, {"id": "query", "displayName": "query", "required": false, "defaultMatch": false, "display": true, "canBeUsedToMatch": true, "type": "string", "removed": false}, {"id": "image_path", "displayName": "image_path", "required": false, "defaultMatch": false, "display": true, "canBeUsedToMatch": true, "type": "string", "removed": true}], "attemptToConvertTypes": false, "convertFieldsToString": false}}, "type": "@n8n/n8n-nodes-langchain.toolWorkflow", "typeVersion": 2, "position": [-1360, 280], "id": "a8555e05-8412-4f3b-9bf3-8529b3269d9a", "name": "Web Search Tool"}, {"parameters": {"name": "image_analysis", "description": "Call this tool to analyze an image based on an image path that you supply as image_path. Call the \"List Documents\" tool to get a list of files to get the path to the image to analyze. Also supply query, which is the prompt to the LLM to extract information from the image.", "workflowId": {"__rl": true, "value": "={{ $workflow.id }}", "mode": "id"}, "workflowInputs": {"mappingMode": "defineBelow", "value": {"query": "={{ $fromAI('query') }}", "tool_type": "image_analysis", "image_path": "={{ $fromAI('file_path') }}"}, "matchingColumns": [], "schema": [{"id": "tool_type", "displayName": "tool_type", "required": false, "defaultMatch": false, "display": true, "canBeUsedToMatch": true, "type": "string", "removed": false}, {"id": "query", "displayName": "query", "required": false, "defaultMatch": false, "display": true, "canBeUsedToMatch": true, "type": "string", "removed": false}, {"id": "image_path", "displayName": "image_path", "required": false, "defaultMatch": false, "display": true, "canBeUsedToMatch": true, "type": "string", "removed": false}], "attemptToConvertTypes": false, "convertFieldsToString": false}}, "type": "@n8n/n8n-nodes-langchain.toolWorkflow", "typeVersion": 2, "position": [-1220, 180], "id": "35fd27ea-e7ae-43c2-b72d-e918145004c6", "name": "Image Analysis Tool"}, {"parameters": {"name": "execute_code", "description": "Call this tool to execute JavaScript code that you create with parameters that you specify too. All code must be a single line and must be JavaScript code.", "jsCode": "/**\n * Execute arbitrary JavaScript code and capture its console output\n * @param {string} codeString - JavaScript code to execute\n * @returns {string} The captured console output\n */\nfunction executeCode(codeString) {\n  // Save the original console.log function\n  const originalLog = console.log;\n  \n  // Create an array to store the output\n  const outputLines = [];\n  \n  // Override console.log to capture output\n  console.log = function(...args) {\n    // Convert all arguments to strings and join them\n    const output = args.map(arg => \n      typeof arg === 'object' ? JSON.stringify(arg) : String(arg)\n    ).join(' ');\n    \n    // Add to our captured output\n    outputLines.push(output);\n  };\n  \n  // Variable to store the result\n  let result;\n  \n  try {\n    // Execute the code\n    result = eval(codeString);\n  } catch (error) {\n    // Restore the original console.log\n    console.log = originalLog;\n    return `Error executing code: ${error.message}`;\n  } finally {\n    // Restore the original console.log\n    console.log = originalLog;\n  }\n  \n  // Join all captured output lines\n  const output = outputLines.join('\\n');\n  \n  // If there's a result but no console output, return the result\n  if (output === '' && result !== undefined) {\n    return String(result);\n  }\n  \n  return output;\n}\n\nreturn executeCode(query.code_to_execute);", "specifyInputSchema": true, "jsonSchemaExample": "{\n\t\"code_to_execute\": \"console.log('test')\"\n}"}, "type": "@n8n/n8n-nodes-langchain.toolCode", "typeVersion": 1.1, "position": [-1100, 300], "id": "8d6ba6ed-867f-4dc6-9eb4-564ed4c73bce", "name": "Code Tool"}, {"parameters": {"jsonSchemaExample": "{\n\t\"memories\": [\"Memory 1\", \"Memory 2\", \"Memory 3\"]\n}"}, "type": "@n8n/n8n-nodes-langchain.outputParserStructured", "typeVersion": 1.2, "position": [-400, -40], "id": "35d6cbde-704f-4ce5-804d-38df64507601", "name": "Structured Output Parser"}, {"parameters": {"assignments": {"assignments": [{"id": "636ddfda-13fd-4f21-ad30-fa21472ed9e7", "name": "output", "value": "={{ $('RAG AI Agent').item.json.output }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [760, -80], "id": "8f2f7c09-7dc7-4406-ad6a-02c5cbd60d74", "name": "Edit Fields1", "executeOnce": true}, {"parameters": {"promptType": "define", "text": "=You are an expert at maintaining a memory database for a user, making sure that there aren't duplicate or contradictory memories.\n\nThe current memories being added to the database are:\n\n{{ $json.output.memories }}\n\nYour job is to:\n- Search the memory database to find any duplicate or contradictory memories. The tool will return memories from the search but you have to decide if they are duplicates or contradictory of the memories being added or not.\n- If you find any duplicate/contradictory memories, output a list of memories to delete (the content of the memory). Otherwise, if you find no memories to delete, output an empty list. You determine based on all the memories returned from the search if they are duplicates/contradictory. The memories you output have to match EXACTLY with the content received.", "hasOutputParser": true, "options": {}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.7, "position": [-120, -220], "id": "93c41517-0e2b-4495-b9be-a12492b635ab", "name": "Memory Agent"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "cb436122-f430-4ea5-98d8-b99e8c52eeaa", "leftValue": "={{ $json.output.memories }}", "rightValue": "", "operator": {"type": "array", "operation": "notEmpty", "singleValue": true}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [-300, -220], "id": "dbf36d01-0887-442d-ac3b-690fd5489b0a", "name": "If"}, {"parameters": {"promptType": "define", "text": "=You are an expert at extract key information from conversations to store as long term memories for RAG.\n\nThe user said:\n\n{{ $('Edit Fields').item.json.chatInput }}\n\nYour goal is to output a list of key memories to extract from the conversation.\n\nJust output a empty list if there is nothing worth saving, like if the user is just asking a basic question without providing additional context.", "hasOutputParser": true}, "type": "@n8n/n8n-nodes-langchain.chainLlm", "typeVersion": 1.5, "position": [-640, -220], "id": "c821de89-d2e3-4c0c-8703-b9bca22a843f", "name": "Basic LLM Chain", "onError": "continueRegularOutput"}, {"parameters": {"operation": "execute<PERSON>uery", "query": "DELETE FROM memories\nWHERE text IN ({{ $json.output.concat($('Basic LLM Chain').item.json.output.memories).map(item => `'${item.replace(/'/g, \"''\")}'`).join(', ') || '__IMPOSSIBLE_VALUE__' }})", "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.5, "position": [220, -220], "id": "d0207f1f-7006-46ff-96d7-e27c4572ead0", "name": "Postgres", "credentials": {"postgres": {"id": "UaTmh0frrACTMPxG", "name": "Postgres account"}}}, {"parameters": {"content": "## Save Long Term Memories", "height": 420, "width": 1660, "color": 6}, "type": "n8n-nodes-base.stickyNote", "position": [-680, -320], "typeVersion": 1, "id": "e40215e0-bff2-4ea0-af82-b47d548b68f4", "name": "Sticky Note5"}, {"parameters": {"model": "qwen2.5:14b-8k", "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOllama", "typeVersion": 1, "position": [-580, -40], "id": "3edc1b2f-5010-456d-949f-96c666093721", "name": "<PERSON><PERSON><PERSON> Chat Model", "credentials": {"ollamaApi": {"id": "GwjiKiEsG5HnTaAf", "name": "Ollama account"}}}, {"parameters": {"mode": "insert", "tableName": "memories", "options": {}}, "type": "@n8n/n8n-nodes-langchain.vectorStorePGVector", "typeVersion": 1, "position": [420, -220], "id": "17d15b8b-0d78-438e-8934-9b5554261e2a", "name": "Postgres PGVector Store1", "credentials": {"postgres": {"id": "UaTmh0frrACTMPxG", "name": "Postgres account"}}}, {"parameters": {"jsonMode": "expressionData", "jsonData": "={{ $('Basic LLM Chain').item.json.output.memories }}", "options": {"metadata": {"metadataValues": [{"name": "timestamp", "value": "={{ $now }}"}]}}}, "type": "@n8n/n8n-nodes-langchain.documentDefaultDataLoader", "typeVersion": 1, "position": [640, 220], "id": "20bf401c-82ad-4924-9762-0c94279737e8", "name": "Default Data Loader1"}, {"parameters": {"chunkSize": 400}, "type": "@n8n/n8n-nodes-langchain.textSplitterCharacterTextSplitter", "typeVersion": 1, "position": [740, 380], "id": "9fdb749a-56fb-498f-9cdd-85b545714de9", "name": "Character Text Splitter"}, {"parameters": {"content": "## Memory Searcher", "height": 400, "width": 520, "color": 6}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-80, 120], "id": "6b9ff929-37bf-4d65-9e79-c8c1d2f77142", "name": "Sticky Note8"}, {"parameters": {"content": "## Memory Chunking & Embedding", "height": 400, "width": 520, "color": 6}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [460, 120], "id": "fc83dca5-1823-4f96-97af-b49f3ea5bb06", "name": "Sticky Note6"}, {"parameters": {"mode": "retrieve-as-tool", "toolName": "memories", "toolDescription": "Use this tool to retrieve memories from the database. You specifically want to use this to find similar memories to what you are planning on adding so that you can do what it takes to remove duplicates.", "tableName": "memories", "topK": 8, "options": {}}, "type": "@n8n/n8n-nodes-langchain.vectorStorePGVector", "typeVersion": 1, "position": [120, 240], "id": "6682ad81-cf06-400b-add9-ffbff5a3dadc", "name": "Postgres PGVector Store2", "credentials": {"postgres": {"id": "UaTmh0frrACTMPxG", "name": "Postgres account"}}}, {"parameters": {"model": "nomic-embed-text:latest"}, "type": "@n8n/n8n-nodes-langchain.embeddingsOllama", "typeVersion": 1, "position": [240, 360], "id": "a4c9ef60-2d03-4210-aad4-040dac9229ab", "name": "Embeddings Ollama3", "credentials": {"ollamaApi": {"id": "GwjiKiEsG5HnTaAf", "name": "Ollama account"}}}, {"parameters": {"model": "nomic-embed-text:latest"}, "type": "@n8n/n8n-nodes-langchain.embeddingsOllama", "typeVersion": 1, "position": [520, 340], "id": "b2b3127e-2eb2-46a0-870e-19ac712f9f1a", "name": "Embeddings Ollama4", "credentials": {"ollamaApi": {"id": "GwjiKiEsG5HnTaAf", "name": "Ollama account"}}}, {"parameters": {"model": {"__rl": true, "value": "qwen2.5:14b-8k", "mode": "list", "cachedResultName": "qwen2.5:14b-8k"}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1.2, "position": [-20, 340], "id": "051e97ab-eef3-4217-ad4e-8be637fa5644", "name": "Ollama (Change Base URL)1", "credentials": {"openAiApi": {"id": "yjDbkWOXNXC959ei", "name": "OpenAi account"}}}, {"parameters": {"jsonSchemaExample": "[\"Memory 1\", \"Memory 2\", \"Memory 3\"]"}, "type": "@n8n/n8n-nodes-langchain.outputParserStructured", "typeVersion": 1.2, "position": [200, -40], "id": "1241ee3e-fa8a-4204-b029-db82021edaae", "name": "Structured Output Parser1"}, {"parameters": {"fileSelector": "={{ $json.image_path }}", "options": {}}, "type": "n8n-nodes-base.readWriteFile", "typeVersion": 1, "position": [-140, -680], "id": "27321ac2-83c0-4196-a380-6a94de556eaa", "name": "Read/Write Files from Disk1"}, {"parameters": {"model": "llava:7b", "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOllama", "typeVersion": 1, "position": [440, -660], "id": "ca3e117a-a267-4726-8d63-0442b35d64ad", "name": "Ollama Chat Model1", "credentials": {"ollamaApi": {"id": "GwjiKiEsG5HnTaAf", "name": "Ollama account"}}}, {"parameters": {"promptType": "define", "text": "={{ $('Determine Tool Type').item.json.query }}", "messages": {"messageValues": [{"type": "HumanMessagePromptTemplate", "messageType": "imageBinary"}]}}, "type": "@n8n/n8n-nodes-langchain.chainLlm", "typeVersion": 1.5, "position": [60, -680], "id": "24a7d4bf-11ed-43e9-875b-389e31edbbd2", "name": "Image Analysis"}, {"parameters": {"url": "=http://searxng:8080/search?q={{ $json.query }}&format=json", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [-140, -500], "id": "209413df-0789-421b-8110-a2caf61facd1", "name": "SearXNG"}, {"parameters": {"fieldToSplitOut": "results", "options": {}}, "type": "n8n-nodes-base.splitOut", "typeVersion": 1, "position": [20, -500], "id": "85f9a6ba-b532-481d-8873-3a5df1bcd01f", "name": "Split Out"}, {"parameters": {"assignments": {"assignments": [{"id": "169ce734-0077-4c34-b7f1-40a35184fad6", "name": "url", "value": "={{ $json.url }}", "type": "string"}, {"id": "310e45f1-904e-4350-971f-a8519a49ab91", "name": "title", "value": "={{ $json.title }}", "type": "string"}, {"id": "f6ac5cd2-4504-4f37-a766-33bc6ef09d47", "name": "content", "value": "={{ $json.content }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [180, -500], "id": "2b265fab-80d8-4962-92f4-84edce5a4e88", "name": "Edit Fields2"}, {"parameters": {"maxItems": 3}, "type": "n8n-nodes-base.limit", "typeVersion": 1, "position": [340, -500], "id": "9d41a49f-6d31-4468-b8a3-10cba28d14b8", "name": "Limit"}, {"parameters": {"aggregate": "aggregateAllItemData", "destinationFieldName": "search_results", "options": {}}, "type": "n8n-nodes-base.aggregate", "typeVersion": 1, "position": [800, -500], "id": "e1721deb-6042-4191-8701-bf757d415caf", "name": "Aggregate1"}, {"parameters": {"url": "={{ $json.url }}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [500, -500], "id": "2a7f494a-d229-440d-b721-19adaa91faa8", "name": "HTTP Request", "alwaysOutputData": true, "onError": "continueRegularOutput"}, {"parameters": {"operation": "extractHtmlContent", "extractionValues": {"values": [{"key": "site_html", "cssSelector": "body"}]}, "options": {}}, "type": "n8n-nodes-base.html", "typeVersion": 1.2, "position": [640, -500], "id": "d9a2a190-9749-451e-ab6c-6558d2d930ca", "name": "HTML", "alwaysOutputData": true, "onError": "continueRegularOutput"}, {"parameters": {"content": "# Dynamous AI Agent Mastery - Local n8n Agent\n\nThis workflow implements a powerful 100% local AI agent using n8n. Use this as a template to build AI agents that runs entirely on your machine and have the core set of tools used in most agents!\n\n## Features\n\n- **Local LLM Integration**: Uses Ollama models for both conversations and embeddings\n- **Agentic RAG**: Local file processing and vector storage for knowledge retrieval\n- **Long-term Memory**: Persistent conversation memory stored in local Supabase\n- **Local Web Search**: Uses SearXNG for web queries without external API keys\n- **Code Execution**: Generates and runs JavaScript code\n- **Image Analysis**: Processes images with vision-capable local LLMs\n- **Self-contained Architecture**: All components run locally for privacy and control\n\n## Setup Requirements\n\n1. **Local Infrastructure**:\n   - [Local AI package](https://github.com/coleam00/local-ai-packaged) (recommended)\n   - OR manually installed Supabase/Postgres, Ollama, and SearXNG\n\n2. **Environment Configuration**:\n   - Use 'ollama pull' in your Ollama container to download the primary LLM for your agent and a vision capable LLM (they can be the same if you wish) if you don't have them already. Example: 'ollama pull mistral-small3.1'\n   - Use 'ollama pull' to download the embedding model if you don't have it already. Example: 'ollama pull nomic-embed-text'\n   - Set up credentials in n8n for Postgres (Supabase) and Ollama (no API key needed)  \n\n\n3. **Database Setup**:\n   - Run the nodes in the top left to set up the database tables (just have to do this once)\n   - Be sure to modify vector dimensions based on your emebdding model (768 for nomic-embed-text)\n\n## Workflow Structure\n\nThe RAG pipeline (bottom part of the workflow in blue) is responsible for syncing a local folder with the Supabase (Postgres) knowledge base. Make sure the workflow is toggled to active in the top right for this pipeline to work!\n\nThe primary agent is in the yellow box in the middle of the workflow. All of the tools are connected either directly to the agent (in the same box or agentic RAG in the middle green box) or as \"sub-workflows\" which are in the green top box.\n\nThe process of creating long term memories is in the purple boxes.\n\n## Key Differentiators for the Local Implementation (Compared to Cloud)\n\n- All processing happens on your machine - no data leaves your environment\n- Lower latency for certain operations due to local processing\n- No usage limits or API costs\n- Complete control over your data and agent behavior\n- Requires more computational resources on your machine", "height": 1280, "width": 800, "color": 5}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-2560, -320], "id": "f9f7b38c-ac07-43b0-8b50-2b6d1336a291", "name": "Sticky Note4"}], "pinData": {"Tool Start": [{"json": {"tool_type": "web_search", "query": "Best AI Agent Frameworks", "tool_type 2": "image_analysis", "query 2": "Describe this image", "image_path": "/data/shared/ArchonMCPThumbnail.jpg"}}]}, "connections": {"Extract Document Text": {"main": [[{"node": "Postgres PGVector Store", "type": "main", "index": 0}]]}, "Default Data Loader": {"ai_document": [[{"node": "Postgres PGVector Store", "type": "ai_document", "index": 0}]]}, "Postgres Chat Memory": {"ai_memory": [[{"node": "RAG AI Agent", "type": "ai_memory", "index": 0}]]}, "Set File ID": {"main": [[{"node": "Delete Old Doc Records", "type": "main", "index": 0}]]}, "Edit Fields": {"main": [[{"node": "RAG AI Agent", "type": "main", "index": 0}]]}, "When chat message received": {"main": [[{"node": "<PERSON>", "type": "main", "index": 0}]]}, "Webhook": {"main": [[{"node": "<PERSON>", "type": "main", "index": 0}]]}, "Extract PDF Text": {"main": [[{"node": "Postgres PGVector Store", "type": "main", "index": 0}]]}, "Aggregate": {"main": [[{"node": "Summarize", "type": "main", "index": 0}]]}, "Summarize": {"main": [[{"node": "<PERSON>", "type": "main", "index": 0}, {"node": "Postgres PGVector Store", "type": "main", "index": 0}]]}, "RAG AI Agent": {"main": [[{"node": "Respond to Webhook", "type": "main", "index": 0}]]}, "Switch": {"main": [[{"node": "Extract PDF Text", "type": "main", "index": 0}], [{"node": "Extract from Excel", "type": "main", "index": 0}], [{"node": "Extract from CSV", "type": "main", "index": 0}], [{"node": "Extract Document Text", "type": "main", "index": 0}], [{"node": "Extract Document Text", "type": "main", "index": 0}]]}, "Extract from Excel": {"main": [[{"node": "Aggregate", "type": "main", "index": 0}, {"node": "Insert Table Rows", "type": "main", "index": 0}]]}, "Set Schema": {"main": [[{"node": "Update <PERSON><PERSON>a for Document Metadata", "type": "main", "index": 0}]]}, "Extract from CSV": {"main": [[{"node": "Aggregate", "type": "main", "index": 0}, {"node": "Insert Table Rows", "type": "main", "index": 0}]]}, "Create Document Metadata Table": {"main": [[]]}, "List Documents": {"ai_tool": [[{"node": "RAG AI Agent", "type": "ai_tool", "index": 0}]]}, "Get File Contents": {"ai_tool": [[{"node": "RAG AI Agent", "type": "ai_tool", "index": 0}]]}, "Query Document Rows": {"ai_tool": [[{"node": "RAG AI Agent", "type": "ai_tool", "index": 0}]]}, "Loop Over Items": {"main": [[], [{"node": "Set File ID", "type": "main", "index": 0}]]}, "Insert Document Metadata": {"main": [[{"node": "Read/Write Files from Disk", "type": "main", "index": 0}]]}, "Local File Trigger": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}]]}, "Read/Write Files from Disk": {"main": [[{"node": "Switch", "type": "main", "index": 0}]]}, "Embeddings Ollama": {"ai_embedding": [[{"node": "Postgres PGVector Store", "type": "ai_embedding", "index": 0}]]}, "Embeddings Ollama1": {"ai_embedding": [[{"node": "Document RAG Tool", "type": "ai_embedding", "index": 0}]]}, "Recursive Character Text Splitter": {"ai_textSplitter": [[{"node": "Default Data Loader", "type": "ai_textSplitter", "index": 0}]]}, "Ollama (Change Base URL)": {"ai_languageModel": [[{"node": "RAG AI Agent", "type": "ai_languageModel", "index": 0}]]}, "Delete Old Doc Records": {"main": [[{"node": "Delete Old Data Records", "type": "main", "index": 0}]]}, "Delete Old Data Records": {"main": [[{"node": "Insert Document Metadata", "type": "main", "index": 0}]]}, "Postgres PGVector Store": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}]]}, "Retrieve Memories Tool": {"ai_tool": [[{"node": "RAG AI Agent", "type": "ai_tool", "index": 0}]]}, "Embeddings Ollama2": {"ai_embedding": [[{"node": "Retrieve Memories Tool", "type": "ai_embedding", "index": 0}]]}, "Document RAG Tool": {"ai_tool": [[{"node": "RAG AI Agent", "type": "ai_tool", "index": 0}]]}, "Determine Tool Type": {"main": [[{"node": "Read/Write Files from Disk1", "type": "main", "index": 0}], [{"node": "SearXNG", "type": "main", "index": 0}]]}, "Tool Start": {"main": [[{"node": "Determine Tool Type", "type": "main", "index": 0}]]}, "Web Search Tool": {"ai_tool": [[{"node": "RAG AI Agent", "type": "ai_tool", "index": 0}]]}, "Image Analysis Tool": {"ai_tool": [[{"node": "RAG AI Agent", "type": "ai_tool", "index": 0}]]}, "Code Tool": {"ai_tool": [[{"node": "RAG AI Agent", "type": "ai_tool", "index": 0}]]}, "Structured Output Parser": {"ai_outputParser": [[{"node": "Basic LLM Chain", "type": "ai_outputParser", "index": 0}]]}, "Memory Agent": {"main": [[{"node": "Postgres", "type": "main", "index": 0}]]}, "If": {"main": [[{"node": "Memory Agent", "type": "main", "index": 0}], [{"node": "Edit Fields1", "type": "main", "index": 0}]]}, "Basic LLM Chain": {"main": [[{"node": "If", "type": "main", "index": 0}]]}, "Postgres": {"main": [[{"node": "Postgres PGVector Store1", "type": "main", "index": 0}]]}, "Respond to Webhook": {"main": [[{"node": "Basic LLM Chain", "type": "main", "index": 0}]]}, "Ollama Chat Model": {"ai_languageModel": [[{"node": "Basic LLM Chain", "type": "ai_languageModel", "index": 0}]]}, "Postgres PGVector Store1": {"main": [[{"node": "Edit Fields1", "type": "main", "index": 0}]]}, "Character Text Splitter": {"ai_textSplitter": [[{"node": "Default Data Loader1", "type": "ai_textSplitter", "index": 0}]]}, "Postgres PGVector Store2": {"ai_tool": [[{"node": "Memory Agent", "type": "ai_tool", "index": 0}]]}, "Embeddings Ollama3": {"ai_embedding": [[{"node": "Postgres PGVector Store2", "type": "ai_embedding", "index": 0}]]}, "Embeddings Ollama4": {"ai_embedding": [[{"node": "Postgres PGVector Store1", "type": "ai_embedding", "index": 0}]]}, "Default Data Loader1": {"ai_document": [[{"node": "Postgres PGVector Store1", "type": "ai_document", "index": 0}]]}, "Ollama (Change Base URL)1": {"ai_languageModel": [[{"node": "Memory Agent", "type": "ai_languageModel", "index": 0}]]}, "Structured Output Parser1": {"ai_outputParser": [[{"node": "Memory Agent", "type": "ai_outputParser", "index": 0}]]}, "Read/Write Files from Disk1": {"main": [[{"node": "Image Analysis", "type": "main", "index": 0}]]}, "Ollama Chat Model1": {"ai_languageModel": [[{"node": "Image Analysis", "type": "ai_languageModel", "index": 0}]]}, "SearXNG": {"main": [[{"node": "Split Out", "type": "main", "index": 0}]]}, "Split Out": {"main": [[{"node": "Edit Fields2", "type": "main", "index": 0}]]}, "Edit Fields2": {"main": [[{"node": "Limit", "type": "main", "index": 0}]]}, "Limit": {"main": [[{"node": "HTTP Request", "type": "main", "index": 0}]]}, "HTTP Request": {"main": [[{"node": "HTML", "type": "main", "index": 0}]]}, "HTML": {"main": [[{"node": "Aggregate1", "type": "main", "index": 0}]]}}, "active": true, "settings": {"executionOrder": "v1"}, "versionId": "0748e7c2-7904-41d9-bc6c-ef9a54fbe138", "meta": {"templateCredsSetupCompleted": true, "instanceId": "73cb7a3e883df514bb47e8d1b34526d30e2abb8f56cd99f10d5948a1e11b25aa"}, "id": "9YU5Li20H8gv8wMY", "tags": []}