# ---------------------------------------------------------------------------
# AGENT API SERVICE ENVIRONMENT VARIABLES
# ---------------------------------------------------------------------------
# These variables are used by the FastAPI agent service.
# Copy these values to your Render agent-api service environment settings.

# ---------------------------------------------------------------------------
# LLM CONFIGURATION
# ---------------------------------------------------------------------------

# The provider for your LLM
# Set this to either openai, openrouter, or ollama
# This is needed on top of the base URL for Mem0 (long term memory)
LLM_PROVIDER=openai

# Base URL for the OpenAI compatible instance (default is https://api.openai.com/v1)
# OpenAI: https://api.openai.com/v1
# Ollama (example): http://localhost:11434/v1
# OpenRouter: https://openrouter.ai/api/v1
LLM_BASE_URL=https://api.openai.com/v1

# OpenAI: https://help.openai.com/en/articles/4936850-where-do-i-find-my-openai-api-key
# Open Router: Get your API Key here after registering: https://openrouter.ai/keys
# Ollama: No need to set this unless you specifically configured an API key
LLM_API_KEY=YOUR_OPENAI_API_KEY

# The LLM you want to use for the agents. Make sure this LLM supports tools (especially important if using Ollama)!
# OpenAI example: gpt-4o-mini
# OpenRouter example: anthropic/claude-3.7-sonnet
# Ollama example: qwen2.5:14b-instruct-8k
LLM_CHOICE=gpt-4o-mini

# The LLM you want to use for image analysis.
# Make sure this LLM supports vision (especially important if using Ollama or OpenRouter)!
# OpenAI example: gpt-4o-mini
# OpenRouter example: anthropic/claude-3.7-sonnet
# Ollama example: llava:7b
VISION_LLM_CHOICE=gpt-4o-mini

# ---------------------------------------------------------------------------
# EMBEDDING CONFIGURATION  
# ---------------------------------------------------------------------------

# The provider for your embedding model
# Set this to either openai, or ollama (openrouter doesn't have embedding models)
# This is needed on top of the base URL for Mem0 (long term memory)
EMBEDDING_PROVIDER=openai

# Base URL for the OpenAI compatible instance that has embedding models (default is https://api.openai.com/v1)
# OpenAI: https://api.openai.com/v1
# Ollama (example): http://localhost:11434/v1
EMBEDDING_BASE_URL=https://api.openai.com/v1

# OpenAI: https://help.openai.com/en/articles/4936850-where-do-i-find-my-openai-api-key
# Ollama: No need to set this unless you specifically configured an API key
EMBEDDING_API_KEY=YOUR_OPENAI_API_KEY

# The embedding model you want to use for RAG.
# Make sure the embeddings column in your database has the same dimensions as this embedding model!
# OpenAI example: text-embedding-3-small
# Ollama example: nomic-embed-text
EMBEDDING_MODEL_CHOICE=text-embedding-3-small

# ---------------------------------------------------------------------------
# DATABASE CONFIGURATION
# ---------------------------------------------------------------------------

# Your Supabase project URL
# Get this from: Supabase Dashboard -> Project Settings -> API
# https://supabase.com/dashboard/project/<project-id>/settings/api
# Format: https://YOUR_SUPABASE_PROJECT_ID.supabase.co
# Example: https://abcd1234xyz.supabase.co
SUPABASE_URL=https://YOUR_SUPABASE_PROJECT_ID.supabase.co

# Your Supabase service role key (secret key)
# Get this from: Supabase Dashboard -> Project Settings -> API
# https://supabase.com/dashboard/project/<project-id>/settings/api
# WARNING: Keep this secret! This key has full database access.
SUPABASE_SERVICE_KEY=YOUR_SUPABASE_SERVICE_KEY

# Postgres DB URL used for Mem0
# For Supabase Postgres connection, you can find this in "Connect" (top middle of Supabase dashboard) -> Transaction pooler
# Format: postgresql://postgres.YOUR_SUPABASE_PROJECT_ID:<EMAIL>:6543/postgres
# Example: postgresql://postgres.abcd1234xyz:<EMAIL>:6543/postgres
DATABASE_URL=postgresql://postgres.YOUR_SUPABASE_PROJECT_ID:<EMAIL>:6543/postgres

# ---------------------------------------------------------------------------
# ENVIRONMENT
# ---------------------------------------------------------------------------

# Set to production for cloud deployment
ENVIRONMENT=production

# ---------------------------------------------------------------------------
# WEB SEARCH CONFIGURATION
# ---------------------------------------------------------------------------

# Set your Brave API key if using Brave for agent web search (leave empty if using SearXNG)
# Get your key by going to the following link after signing up for Brave:
# https://api.search.brave.com/app/keys
BRAVE_API_KEY=YOUR_BRAVE_API_KEY

# Set the SearXNG endpoint if using SearXNG for agent web search (leave empty if using Brave)
# For the local AI package - this will be:
#    http://localhost:8080 if your agent is running outside of Docker
#    http://searxng:8080 if your agent is running in a container in the local-ai network
SEARXNG_BASE_URL=

# ---------------------------------------------------------------------------
# AGENT OBSERVABILITY CONFIGURATION (Optional)
# ---------------------------------------------------------------------------

# LangFuse configuration for agent observability (optional - leave empty to disable)
# Provides detailed insights into agent conversations, performance metrics, and debugging
# Get your keys from https://cloud.langfuse.com/ after creating a project
# Langfuse host will be http://localhost:3000 for the Local AI Package and https://cloud.langfuse.com for cloud
LANGFUSE_PUBLIC_KEY=
LANGFUSE_SECRET_KEY=
LANGFUSE_HOST=https://cloud.langfuse.com