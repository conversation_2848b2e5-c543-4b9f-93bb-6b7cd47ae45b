{"timestamp": "2025-07-13 04:35:30", "system_status": {"docker_available": false, "containers_running": 0, "total_containers_expected": 10}, "error_summary": {"container_errors": 9, "application_errors": 10, "development_errors": 0}, "log_locations": {"containers": "/mnt/e/Projects/lonors/loni/data/logs/containers/", "applications": "/mnt/e/Projects/lonors/loni/data/logs/applications/", "development": "/mnt/e/Projects/lonors/loni/data/logs/development/", "system": "/mnt/e/Projects/lonors/loni/data/logs/system/"}, "ai_recommendations": ["Check container logs for startup issues", "Review development tool outputs for code quality", "Monitor application logs for runtime errors", "Verify system resources and Docker health"]}