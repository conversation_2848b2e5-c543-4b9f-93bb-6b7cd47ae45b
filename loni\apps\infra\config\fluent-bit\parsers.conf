# Fluent Bit parsers configuration for LONI Platform

[PARSER]
    Name        json
    Format      json
    Time_Key    time
    Time_Format %Y-%m-%d %H:%M:%S
    Time_Keep   On

[PARSER]
    Name        loni_app
    Format      regex
    Regex       ^\[(?<time>[^\]]*)\] (?<level>\w+): (?<message>.*)$
    Time_Key    time
    Time_Format %Y-%m-%d %H:%M:%S

[PARSER]
    Name        docker
    Format      json
    Time_Key    time
    Time_Format %Y-%m-%dT%H:%M:%S.%L
    Time_Keep   On
    Decode_Field_As escaped_utf8 log do_next
    Decode_Field_As json log

[PARSER]
    Name        multiline_start
    Format      regex
    Regex       ^\d{4}-\d{2}-\d{2}|\[.*\]|ERROR|FATAL|CRITICAL

[PARSER]
    Name        ai_structured
    Format      regex
    Regex       ^(?<timestamp>\d{4}-\d{2}-\d{2}[T ]\d{2}:\d{2}:\d{2}(?:\.\d+)?(?:Z|[+-]\d{2}:\d{2})?)\s*(?<level>ERROR|FATAL|CRITICAL|error|fatal|critical|Error|Fatal|Critical)?\s*(?<service>\w+)?\s*(?<container_name>[\w-]+)?\s*:?\s*(?<message>.*)$
    Time_Key    timestamp
    Time_Format %Y-%m-%dT%H:%M:%S.%L
    Time_Keep   On

[PARSER]
    Name        development_tools
    Format      regex
    Regex       ^\[(?<timestamp>[^\]]+)\]\s+(?<level>ERROR|FATAL|CRITICAL|error|fatal|critical|Error|Fatal|Critical)\s+(?<tool>\w+):\s+(?<file>[^:]+):(?<line>\d+):(?<column>\d+)?\s*-?\s*(?<message>.*)$
    Time_Key    timestamp
    Time_Format %Y-%m-%d %H:%M:%S
    Time_Keep   On