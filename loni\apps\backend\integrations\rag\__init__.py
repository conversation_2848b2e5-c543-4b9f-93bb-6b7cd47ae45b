"""
RAG (Retrieval-Augmented Generation) integration package.

This package provides RAG functionality with vector database integration
for semantic search and context retrieval.
"""

from .client import QdrantClient
from .service import RAGService
from .models import Document, DocumentChunk, SearchResult, EmbeddingModel
from .processor import DocumentProcessor

__all__ = [
    "QdrantClient",
    "RAGService",
    "Document",
    "DocumentChunk", 
    "SearchResult",
    "EmbeddingModel",
    "DocumentProcessor",
]
