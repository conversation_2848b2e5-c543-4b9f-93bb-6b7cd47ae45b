"""
Base database models and abstractions.

This module provides base classes for database models following
the Single Responsibility Principle.
"""

from datetime import datetime
from typing import Any, Dict, Optional
from uuid import UUID, uuid4

from sqlalchemy import DateTime, String, Text, func
from sqlalchemy.dialects.postgresql import <PERSON><PERSON><PERSON>, UUID as PGUUID
from sqlalchemy.ext.declarative import declared_attr
from sqlalchemy.orm import DeclarativeBase, Mapped, mapped_column


class BaseModel(DeclarativeBase):
    """
    Base model class for all database models.
    
    Provides common fields and functionality for all models.
    """
    
    # Use UUID as primary key type
    type_annotation_map = {
        UUID: PGUUID(as_uuid=True),
        Dict[str, Any]: JSON,
    }
    
    @declared_attr
    def __tablename__(cls) -> str:
        """Generate table name from class name."""
        return cls.__name__.lower()
    
    # Primary key
    id: Mapped[UUID] = mapped_column(
        PGUUID(as_uuid=True),
        primary_key=True,
        default=uuid4,
        index=True
    )
    
    # Timestamps
    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        server_default=func.now(),
        nullable=False
    )
    
    updated_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        server_default=func.now(),
        onupdate=func.now(),
        nullable=False
    )
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert model instance to dictionary."""
        return {
            column.name: getattr(self, column.name)
            for column in self.__table__.columns
        }
    
    def update_from_dict(self, data: Dict[str, Any]) -> None:
        """Update model instance from dictionary."""
        for key, value in data.items():
            if hasattr(self, key):
                setattr(self, key, value)
    
    def __repr__(self) -> str:
        """String representation of model instance."""
        return f"<{self.__class__.__name__}(id={self.id})>"


class TimestampedModel(BaseModel):
    """
    Abstract base class for models that only need timestamps.
    
    Useful for models that don't need the full BaseModel functionality.
    """
    
    __abstract__ = True


class SoftDeleteModel(BaseModel):
    """
    Base model class with soft delete functionality.
    
    Provides deleted_at field for soft deletion patterns.
    """
    
    __abstract__ = True
    
    deleted_at: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True),
        nullable=True,
        default=None
    )
    
    def soft_delete(self) -> None:
        """Mark the record as deleted."""
        self.deleted_at = datetime.utcnow()
    
    def restore(self) -> None:
        """Restore a soft-deleted record."""
        self.deleted_at = None
    
    @property
    def is_deleted(self) -> bool:
        """Check if the record is soft-deleted."""
        return self.deleted_at is not None


class AuditableModel(BaseModel):
    """
    Base model class with audit trail functionality.
    
    Tracks who created and modified records.
    """
    
    __abstract__ = True
    
    created_by: Mapped[Optional[UUID]] = mapped_column(
        PGUUID(as_uuid=True),
        nullable=True
    )
    
    updated_by: Mapped[Optional[UUID]] = mapped_column(
        PGUUID(as_uuid=True),
        nullable=True
    )
    
    def set_audit_fields(self, user_id: UUID, is_create: bool = False) -> None:
        """Set audit fields for create/update operations."""
        if is_create:
            self.created_by = user_id
        self.updated_by = user_id


class VersionedModel(BaseModel):
    """
    Base model class with optimistic locking.
    
    Provides version field for optimistic concurrency control.
    """
    
    __abstract__ = True
    
    version: Mapped[int] = mapped_column(
        default=1,
        nullable=False
    )
    
    def increment_version(self) -> None:
        """Increment the version number."""
        self.version += 1