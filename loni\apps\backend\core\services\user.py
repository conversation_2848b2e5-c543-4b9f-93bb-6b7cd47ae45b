"""
User service for business logic operations.
"""
from typing import Optional, List
from uuid import UUID

from fastapi_users.exceptions import UserAlreadyExists
from sqlalchemy.ext.asyncio import AsyncSession

from ..container import Container
from ..models.user import User
from ..repositories.user import UserRepository
from .base import BaseService


class UserService(BaseService):
    """Service for user-related business operations."""
    
    def __init__(self, session: AsyncSession, container: Container):
        super().__init__(session, container)
        self.user_repo = UserRepository(session)
    
    async def get_user(self, user_id: UUID) -> Optional[User]:
        """Get user by ID."""
        return await self.user_repo.get_by_id(user_id)
    
    async def get_user_by_email(self, email: str) -> Optional[User]:
        """Get user by email address."""
        return await self.user_repo.get_by_email(email)
    
    async def get_user_by_username(self, username: str) -> Optional[User]:
        """Get user by username."""
        return await self.user_repo.get_by_username(username)
    
    async def create_user(
        self,
        email: str,
        name: str,
        hashed_password: str,
        is_superuser: bool = False,
        is_verified: bool = False,
        api_quota_limit: int = 100000
    ) -> User:
        """Create a new user."""
        # Check if user already exists
        existing_user = await self.user_repo.get_by_email(email)
        if existing_user:
            raise UserAlreadyExists()
        
        existing_username = await self.user_repo.get_by_username(name)
        if existing_username:
            raise ValueError(f"Username '{name}' already exists")
        
        return await self.user_repo.create(
            email=email,
            name=name,
            hashed_password=hashed_password,
            is_superuser=is_superuser,
            is_verified=is_verified,
            api_quota_limit=api_quota_limit,
            api_quota_used=0
        )
    
    async def update_user_profile(
        self,
        user_id: UUID,
        name: Optional[str] = None,
        **kwargs
    ) -> Optional[User]:
        """Update user profile information."""
        user = await self.user_repo.get_by_id(user_id)
        if not user:
            return None
        
        update_data = {}
        if name is not None:
            # Check if username is already taken
            existing = await self.user_repo.get_by_username(name)
            if existing and existing.id != user_id:
                raise ValueError(f"Username '{name}' already exists")
            update_data['name'] = name
        
        update_data.update(kwargs)
        
        if update_data:
            return await self.user_repo.update(user_id, **update_data)
        
        return user
    
    async def check_quota_available(self, user_id: UUID, tokens_needed: int) -> bool:
        """Check if user has sufficient API quota."""
        return await self.user_repo.check_quota_available(user_id, tokens_needed)
    
    async def consume_quota(self, user_id: UUID, tokens_used: int) -> User:
        """Consume user's API quota."""
        # Check quota availability first
        if not await self.check_quota_available(user_id, tokens_used):
            raise ValueError("Insufficient API quota")
        
        return await self.user_repo.update_quota_usage(user_id, tokens_used)
    
    async def reset_quota(self, user_id: UUID) -> User:
        """Reset user's API quota (admin operation)."""
        return await self.user_repo.reset_quota_usage(user_id)
    
    async def get_quota_status(self, user_id: UUID) -> dict:
        """Get user's quota usage status."""
        user = await self.user_repo.get_by_id(user_id)
        if not user:
            raise ValueError(f"User with id {user_id} not found")
        
        return {
            'used': user.api_quota_used,
            'limit': user.api_quota_limit,
            'remaining': user.api_quota_limit - user.api_quota_used,
            'percentage_used': (user.api_quota_used / user.api_quota_limit) * 100
        }
    
    async def list_users(
        self,
        limit: int = 50,
        offset: int = 0,
        search: Optional[str] = None
    ) -> List[User]:
        """List users with optional search."""
        if search:
            return await self.user_repo.search(
                search_term=search,
                search_fields=['name', 'email'],
                limit=limit,
                offset=offset
            )
        
        return await self.user_repo.get_all(limit=limit, offset=offset)
    
    async def delete_user(self, user_id: UUID, soft_delete: bool = True) -> bool:
        """Delete user (soft delete by default)."""
        if soft_delete:
            return await self.user_repo.soft_delete(user_id)
        else:
            return await self.user_repo.delete(user_id)