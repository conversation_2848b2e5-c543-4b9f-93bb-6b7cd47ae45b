"""
Metrics middleware.

This middleware provides application metrics collection
following the Single Responsibility Principle.
"""

import time
from typing import Dict, Optional

from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware


class MetricsMiddleware(BaseHTTPMiddleware):
    """
    Metrics middleware.
    
    Collects application metrics such as request counts,
    response times, and error rates.
    """
    
    def __init__(self, app):
        """Initialize the metrics middleware."""
        super().__init__(app)
        self._request_count: Dict[str, int] = {}
        self._response_times: Dict[str, list] = {}
        self._error_count: Dict[str, int] = {}
        self._active_requests: int = 0
    
    async def dispatch(self, request: Request, call_next) -> Response:
        """
        Process request with metrics collection.
        
        Args:
            request: The incoming request
            call_next: The next middleware or endpoint
            
        Returns:
            The response with metrics collected
        """
        # Track active requests
        self._active_requests += 1
        
        # Create metric key
        metric_key = f"{request.method}:{request.url.path}"
        
        # Track request count
        self._increment_request_count(metric_key)
        
        # Start timing
        start_time = time.time()
        
        try:
            # Process request
            response = await call_next(request)
            
            # Calculate response time
            response_time = time.time() - start_time
            self._record_response_time(metric_key, response_time)
            
            # Track errors
            if response.status_code >= 400:
                self._increment_error_count(metric_key)
            
            # Add metrics headers
            response.headers["X-Response-Time"] = f"{response_time:.4f}"
            response.headers["X-Active-Requests"] = str(self._active_requests)
            
            return response
            
        except Exception as exc:
            # Track errors
            self._increment_error_count(metric_key)
            raise
        
        finally:
            # Decrease active requests
            self._active_requests -= 1
    
    def _increment_request_count(self, metric_key: str) -> None:
        """
        Increment request count for the given metric key.
        
        Args:
            metric_key: The metric key (method:path)
        """
        self._request_count[metric_key] = self._request_count.get(metric_key, 0) + 1
    
    def _record_response_time(self, metric_key: str, response_time: float) -> None:
        """
        Record response time for the given metric key.
        
        Args:
            metric_key: The metric key (method:path)
            response_time: The response time in seconds
        """
        if metric_key not in self._response_times:
            self._response_times[metric_key] = []
        
        # Keep only the last 100 response times to prevent memory bloat
        times = self._response_times[metric_key]
        times.append(response_time)
        if len(times) > 100:
            times.pop(0)
    
    def _increment_error_count(self, metric_key: str) -> None:
        """
        Increment error count for the given metric key.
        
        Args:
            metric_key: The metric key (method:path)
        """
        self._error_count[metric_key] = self._error_count.get(metric_key, 0) + 1
    
    def get_metrics(self) -> Dict[str, any]:
        """
        Get current metrics summary.
        
        Returns:
            Dictionary containing current metrics
        """
        metrics = {
            "active_requests": self._active_requests,
            "endpoints": {}
        }
        
        # Aggregate metrics by endpoint
        all_keys = set(self._request_count.keys()) | set(self._response_times.keys()) | set(self._error_count.keys())
        
        for key in all_keys:
            request_count = self._request_count.get(key, 0)
            error_count = self._error_count.get(key, 0)
            response_times = self._response_times.get(key, [])
            
            endpoint_metrics = {
                "request_count": request_count,
                "error_count": error_count,
                "error_rate": error_count / request_count if request_count > 0 else 0,
            }
            
            if response_times:
                endpoint_metrics.update({
                    "avg_response_time": sum(response_times) / len(response_times),
                    "min_response_time": min(response_times),
                    "max_response_time": max(response_times),
                })
            
            metrics["endpoints"][key] = endpoint_metrics
        
        return metrics
    
    def reset_metrics(self) -> None:
        """Reset all metrics to zero."""
        self._request_count.clear()
        self._response_times.clear()
        self._error_count.clear()
        self._active_requests = 0