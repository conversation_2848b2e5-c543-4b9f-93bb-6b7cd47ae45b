"""
Rate limiting middleware and utilities.

This module provides rate limiting functionality to prevent abuse
and ensure fair usage of the API.
"""

import time
from typing import Dict, <PERSON><PERSON>, <PERSON><PERSON>
from collections import defaultdict, deque

from fastapi import HTT<PERSON><PERSON>xception, Request, Response
from fastapi.responses import J<PERSON>NResponse
from starlette.middleware.base import BaseHTTPMiddleware
from loguru import logger


class RateLimiter:
    """
    In-memory rate limiter using sliding window algorithm.
    
    In production, this should be replaced with Redis-based implementation.
    """
    
    def __init__(self):
        """Initialize rate limiter."""
        self._windows: Dict[str, deque] = defaultdict(deque)
        self._last_cleanup = time.time()
        self._cleanup_interval = 300  # 5 minutes
    
    def is_allowed(
        self, 
        key: str, 
        limit: int, 
        window_seconds: int = 3600
    ) -> Tuple[bool, Dict[str, int]]:
        """
        Check if request is allowed under rate limit.
        
        Args:
            key: Unique identifier for rate limiting
            limit: Maximum requests allowed
            window_seconds: Time window in seconds
            
        Returns:
            Tuple of (is_allowed, rate_limit_info)
        """
        current_time = time.time()
        window_start = current_time - window_seconds
        
        # Cleanup old entries periodically
        if current_time - self._last_cleanup > self._cleanup_interval:
            self._cleanup_old_entries()
            self._last_cleanup = current_time
        
        # Get or create window for this key
        window = self._windows[key]
        
        # Remove expired entries
        while window and window[0] <= window_start:
            window.popleft()
        
        # Check if limit exceeded
        current_count = len(window)
        is_allowed = current_count < limit
        
        # Add current request if allowed
        if is_allowed:
            window.append(current_time)
        
        # Calculate reset time
        reset_time = int(window[0] + window_seconds) if window else int(current_time + window_seconds)
        
        rate_limit_info = {
            "limit": limit,
            "remaining": max(0, limit - current_count - (1 if is_allowed else 0)),
            "reset": reset_time,
            "retry_after": max(0, reset_time - int(current_time)) if not is_allowed else 0
        }
        
        return is_allowed, rate_limit_info
    
    def _cleanup_old_entries(self) -> None:
        """Clean up old entries to prevent memory leaks."""
        current_time = time.time()
        max_age = 3600  # 1 hour
        
        keys_to_remove = []
        for key, window in self._windows.items():
            # Remove entries older than max_age
            while window and window[0] <= current_time - max_age:
                window.popleft()
            
            # Remove empty windows
            if not window:
                keys_to_remove.append(key)
        
        for key in keys_to_remove:
            del self._windows[key]


class RateLimitMiddleware(BaseHTTPMiddleware):
    """
    Rate limiting middleware for FastAPI.
    
    Applies different rate limits based on endpoint and user type.
    """
    
    def __init__(self, app, rate_limiter: Optional[RateLimiter] = None):
        """
        Initialize rate limit middleware.
        
        Args:
            app: FastAPI application
            rate_limiter: Rate limiter instance
        """
        super().__init__(app)
        self.rate_limiter = rate_limiter or RateLimiter()
        
        # Rate limit configurations
        self.rate_limits = {
            # General API limits
            "default": {"limit": 100, "window": 3600},  # 100 requests per hour
            
            # Authentication endpoints
            "auth": {"limit": 10, "window": 900},  # 10 requests per 15 minutes
            
            # Chat endpoints
            "chat": {"limit": 50, "window": 3600},  # 50 chat requests per hour
            
            # Document upload
            "upload": {"limit": 20, "window": 3600},  # 20 uploads per hour
            
            # Model management
            "models": {"limit": 30, "window": 3600},  # 30 model requests per hour
            
            # Admin endpoints
            "admin": {"limit": 200, "window": 3600},  # 200 admin requests per hour
        }
        
        # Endpoint patterns
        self.endpoint_patterns = {
            r"/auth/.*": "auth",
            r"/chat/.*": "chat",
            r"/documents/upload": "upload",
            r"/models/.*": "models",
            r"/admin/.*": "admin",
        }
    
    async def dispatch(self, request: Request, call_next):
        """
        Process request with rate limiting.
        
        Args:
            request: HTTP request
            call_next: Next middleware/handler
            
        Returns:
            HTTP response
        """
        # Skip rate limiting for health checks and static files
        if self._should_skip_rate_limiting(request):
            return await call_next(request)
        
        # Determine rate limit category
        category = self._get_rate_limit_category(request)
        rate_config = self.rate_limits.get(category, self.rate_limits["default"])
        
        # Get rate limiting key
        rate_key = self._get_rate_limit_key(request, category)
        
        # Check rate limit
        is_allowed, rate_info = self.rate_limiter.is_allowed(
            key=rate_key,
            limit=rate_config["limit"],
            window_seconds=rate_config["window"]
        )
        
        # Log rate limit check
        logger.debug(
            f"Rate limit check: {rate_key} - {category} - "
            f"allowed: {is_allowed} - remaining: {rate_info['remaining']}"
        )
        
        if not is_allowed:
            # Rate limit exceeded
            logger.warning(
                f"Rate limit exceeded for {rate_key} on {request.url.path}"
            )
            
            return JSONResponse(
                status_code=429,
                content={
                    "error": "Rate limit exceeded",
                    "message": f"Too many requests. Limit: {rate_info['limit']} per {rate_config['window']} seconds",
                    "retry_after": rate_info["retry_after"]
                },
                headers={
                    "X-RateLimit-Limit": str(rate_info["limit"]),
                    "X-RateLimit-Remaining": str(rate_info["remaining"]),
                    "X-RateLimit-Reset": str(rate_info["reset"]),
                    "Retry-After": str(rate_info["retry_after"])
                }
            )
        
        # Process request
        response = await call_next(request)
        
        # Add rate limit headers to response
        response.headers["X-RateLimit-Limit"] = str(rate_info["limit"])
        response.headers["X-RateLimit-Remaining"] = str(rate_info["remaining"])
        response.headers["X-RateLimit-Reset"] = str(rate_info["reset"])
        
        return response
    
    def _should_skip_rate_limiting(self, request: Request) -> bool:
        """Check if request should skip rate limiting."""
        skip_paths = ["/health", "/docs", "/redoc", "/openapi.json"]
        return any(request.url.path.startswith(path) for path in skip_paths)
    
    def _get_rate_limit_category(self, request: Request) -> str:
        """Determine rate limit category for request."""
        import re
        
        path = request.url.path
        
        for pattern, category in self.endpoint_patterns.items():
            if re.match(pattern, path):
                return category
        
        return "default"
    
    def _get_rate_limit_key(self, request: Request, category: str) -> str:
        """Generate rate limiting key for request."""
        # Try to get user ID from authentication
        user_id = self._get_user_id_from_request(request)
        if user_id:
            return f"user:{user_id}:{category}"
        
        # Fall back to IP address
        client_ip = self._get_client_ip(request)
        return f"ip:{client_ip}:{category}"
    
    def _get_user_id_from_request(self, request: Request) -> Optional[str]:
        """Extract user ID from request (if authenticated)."""
        # This would integrate with your authentication system
        # For now, return None (use IP-based rate limiting)
        auth_header = request.headers.get("Authorization")
        if auth_header and auth_header.startswith("Bearer "):
            # In a real implementation, decode JWT token here
            # For now, extract from fake token format
            token = auth_header[7:]
            if token.startswith("fake-token-"):
                return token[11:]  # Extract user ID from fake token
        
        return None
    
    def _get_client_ip(self, request: Request) -> str:
        """Get client IP address from request."""
        # Check for forwarded headers (when behind proxy)
        forwarded_for = request.headers.get("X-Forwarded-For")
        if forwarded_for:
            return forwarded_for.split(",")[0].strip()
        
        real_ip = request.headers.get("X-Real-IP")
        if real_ip:
            return real_ip
        
        # Fall back to direct client IP
        return request.client.host if request.client else "unknown"


# Rate limiting decorators for specific endpoints
def rate_limit(limit: int, window: int = 3600):
    """
    Decorator for applying rate limits to specific endpoints.
    
    Args:
        limit: Maximum requests allowed
        window: Time window in seconds
    """
    def decorator(func):
        func._rate_limit = {"limit": limit, "window": window}
        return func
    return decorator


# Utility functions
def get_rate_limit_info(request: Request, rate_limiter: RateLimiter) -> Dict[str, int]:
    """
    Get current rate limit information for a request.
    
    Args:
        request: HTTP request
        rate_limiter: Rate limiter instance
        
    Returns:
        Rate limit information
    """
    # This would be used by endpoints to check their current rate limit status
    middleware = RateLimitMiddleware(None, rate_limiter)
    category = middleware._get_rate_limit_category(request)
    rate_key = middleware._get_rate_limit_key(request, category)
    rate_config = middleware.rate_limits.get(category, middleware.rate_limits["default"])
    
    _, rate_info = rate_limiter.is_allowed(
        key=rate_key,
        limit=rate_config["limit"],
        window_seconds=rate_config["window"]
    )
    
    return rate_info


def create_rate_limit_response(rate_info: Dict[str, int]) -> JSONResponse:
    """
    Create a rate limit exceeded response.
    
    Args:
        rate_info: Rate limit information
        
    Returns:
        JSON response with rate limit error
    """
    return JSONResponse(
        status_code=429,
        content={
            "error": "Rate limit exceeded",
            "message": f"Too many requests. Try again in {rate_info['retry_after']} seconds.",
            "retry_after": rate_info["retry_after"]
        },
        headers={
            "X-RateLimit-Limit": str(rate_info["limit"]),
            "X-RateLimit-Remaining": str(rate_info["remaining"]),
            "X-RateLimit-Reset": str(rate_info["reset"]),
            "Retry-After": str(rate_info["retry_after"])
        }
    )
