"""
Interactive template selection interface.

Provides a rich, interactive checkbox-based interface for template selection
with real-time dependency validation and conflict detection.
"""

from typing import Set, List, Dict, Any, Optional
from rich.console import Console
from rich.panel import Panel
from rich.table import Table
from rich.text import Text
from rich.prompt import Confirm, Prompt
from rich.columns import Columns
from rich.tree import Tree
from rich.live import Live
from rich.layout import Layout
from rich.align import Align
import keyboard
import sys

from ..core.services import TemplateOrchestrator
from ..core.models import TemplateMetadata, TemplateCategory, ValidationResult


class InteractiveTemplateSelector:
    """
    Interactive template selection interface with checkbox functionality.
    
    Provides real-time validation, dependency resolution, and conflict detection
    while allowing users to select templates through an intuitive interface.
    """
    
    def __init__(self, orchestrator: TemplateOrchestrator, console: Console):
        """Initialize interactive selector."""
        self.orchestrator = orchestrator
        self.console = console
        self.templates = orchestrator.get_available_templates()
        self.selected_templates: Set[str] = set()
        self.current_index = 0
        self.category_filter: Optional[str] = None
        self.search_filter: Optional[str] = None
        
        # Group templates by category
        self.categories = self._group_templates_by_category()
        self.filtered_templates = self.templates.copy()
    
    def select_templates(self) -> Set[str]:
        """
        Main template selection interface.
        
        Returns:
            Set of selected template IDs
        """
        self.console.clear()
        self._show_instructions()
        
        # Use simpler interface for better compatibility
        return self._simple_selection_interface()
    
    def _simple_selection_interface(self) -> Set[str]:
        """Simple selection interface using prompts."""
        while True:
            self.console.clear()
            self._display_template_table()
            
            # Show current selection
            if self.selected_templates:
                self._display_current_selection()
            
            # Show menu options
            self._show_menu()
            
            choice = Prompt.ask(
                "\nSelect option",
                choices=["add", "remove", "list", "validate", "preview", "done", "quit"],
                default="done" if self.selected_templates else "add"
            )
            
            if choice == "add":
                self._add_templates()
            elif choice == "remove":
                self._remove_templates()
            elif choice == "list":
                self._show_template_details()
            elif choice == "validate":
                self._validate_current_selection()
            elif choice == "preview":
                self._preview_current_selection()
            elif choice == "done":
                if self.selected_templates:
                    return self.selected_templates
                else:
                    self.console.print("[yellow]No templates selected![/yellow]")
            elif choice == "quit":
                return set()
    
    def _display_template_table(self):
        """Display templates in a table format."""
        # Filter templates
        display_templates = self._get_filtered_templates()
        
        if not display_templates:
            self.console.print("[red]No templates match current filters[/red]")
            return
        
        # Group by category
        categories = {}
        for template in display_templates:
            cat = template.category.value
            if cat not in categories:
                categories[cat] = []
            categories[cat].append(template)
        
        # Display each category
        for category, templates_in_cat in sorted(categories.items()):
            self.console.print(f"\n[bold blue]{category.upper()}[/bold blue]")
            
            table = Table(show_header=True, header_style="bold magenta")
            table.add_column("✓", width=3)
            table.add_column("ID", style="cyan", no_wrap=True)
            table.add_column("Name", style="green")
            table.add_column("Description", max_width=50)
            table.add_column("Dependencies", style="yellow")
            
            for template in sorted(templates_in_cat, key=lambda t: t.id):
                selected_mark = "✓" if template.id in self.selected_templates else " "
                deps = ", ".join(template.dependencies[:3])  # Show first 3 deps
                if len(template.dependencies) > 3:
                    deps += "..."
                
                # Highlight conflicts
                style = None
                if self._has_conflicts(template.id):
                    style = "red"
                elif template.id in self.selected_templates:
                    style = "green"
                
                table.add_row(
                    selected_mark,
                    template.id,
                    template.name,
                    template.description[:47] + "..." if len(template.description) > 50 else template.description,
                    deps or "-",
                    style=style
                )
            
            self.console.print(table)
    
    def _display_current_selection(self):
        """Display currently selected templates."""
        if not self.selected_templates:
            return
        
        self.console.print(f"\n[bold green]Selected Templates ({len(self.selected_templates)}):[/bold green]")
        
        # Validate current selection
        validation_result = self.orchestrator.select_templates(self.selected_templates)
        resolved_templates = self.orchestrator.get_current_selection()
        
        # Show selected templates
        selected_list = []
        for template_id in sorted(self.selected_templates):
            template = self._get_template_by_id(template_id)
            if template:
                selected_list.append(f"[green]✓ {template.name}[/green] ({template_id})")
        
        # Show auto-added dependencies
        auto_added = resolved_templates - self.selected_templates
        if auto_added:
            selected_list.append("\n[blue]Auto-added dependencies:[/blue]")
            for template_id in sorted(auto_added):
                template = self._get_template_by_id(template_id)
                if template:
                    selected_list.append(f"[blue]+ {template.name}[/blue] ({template_id})")
        
        self.console.print("\n".join(selected_list))
        
        # Show validation issues
        if not validation_result.is_valid:
            self.console.print("\n[red]⚠ Issues detected:[/red]")
            for issue in validation_result.issues:
                self.console.print(f"  • {issue.message}")
    
    def _show_menu(self):
        """Show menu options."""
        menu_items = [
            "[cyan]add[/cyan] - Add templates",
            "[yellow]remove[/yellow] - Remove templates", 
            "[blue]list[/blue] - Show template details",
            "[magenta]validate[/magenta] - Validate selection",
            "[green]preview[/green] - Preview generation",
            "[bold green]done[/bold green] - Finish selection",
            "[red]quit[/red] - Exit without selection"
        ]
        
        self.console.print(f"\n[bold]Options:[/bold] {' | '.join(menu_items)}")
    
    def _add_templates(self):
        """Add templates to selection."""
        available_templates = [t for t in self.filtered_templates if t.id not in self.selected_templates]
        
        if not available_templates:
            self.console.print("[yellow]No templates available to add[/yellow]")
            return
        
        # Show categories for selection
        categories = set(t.category.value for t in available_templates)
        
        if len(categories) > 1:
            category_choice = Prompt.ask(
                "Select category (or 'all' for all categories)",
                choices=list(sorted(categories)) + ["all"],
                default="all"
            )
            
            if category_choice != "all":
                available_templates = [t for t in available_templates if t.category.value == category_choice]
        
        # Show templates for selection
        self.console.print("\n[bold]Available templates:[/bold]")
        for i, template in enumerate(available_templates, 1):
            deps_info = f" (deps: {', '.join(template.dependencies)})" if template.dependencies else ""
            self.console.print(f"{i:2}. [cyan]{template.id}[/cyan] - {template.name}{deps_info}")
        
        # Get selection
        selection = Prompt.ask(
            "\nEnter template numbers (comma-separated) or template IDs",
            default=""
        )
        
        if not selection:
            return
        
        # Parse selection
        selected_ids = self._parse_template_selection(selection, available_templates)
        
        # Add to selection
        for template_id in selected_ids:
            self.selected_templates.add(template_id)
            template = self._get_template_by_id(template_id)
            if template:
                self.console.print(f"[green]✓ Added {template.name}[/green]")
        
        if selected_ids:
            self.console.print(f"\n[green]Added {len(selected_ids)} template(s)[/green]")
            Prompt.ask("Press Enter to continue", default="")
    
    def _remove_templates(self):
        """Remove templates from selection."""
        if not self.selected_templates:
            self.console.print("[yellow]No templates to remove[/yellow]")
            return
        
        # Show current selection
        self.console.print("\n[bold]Currently selected:[/bold]")
        selected_list = list(self.selected_templates)
        for i, template_id in enumerate(selected_list, 1):
            template = self._get_template_by_id(template_id)
            name = template.name if template else template_id
            self.console.print(f"{i:2}. [yellow]{template_id}[/yellow] - {name}")
        
        # Get removal selection
        selection = Prompt.ask(
            "\nEnter template numbers (comma-separated) or template IDs to remove",
            default=""
        )
        
        if not selection:
            return
        
        # Parse selection
        to_remove = self._parse_template_selection(selection, [self._get_template_by_id(tid) for tid in selected_list])
        
        # Remove from selection
        for template_id in to_remove:
            if template_id in self.selected_templates:
                self.selected_templates.remove(template_id)
                template = self._get_template_by_id(template_id)
                if template:
                    self.console.print(f"[red]✗ Removed {template.name}[/red]")
        
        if to_remove:
            self.console.print(f"\n[red]Removed {len(to_remove)} template(s)[/red]")
            Prompt.ask("Press Enter to continue", default="")
    
    def _show_template_details(self):
        """Show detailed information about a template."""
        template_id = Prompt.ask("Enter template ID to view details")
        
        template = self._get_template_by_id(template_id)
        if not template:
            self.console.print(f"[red]Template '{template_id}' not found[/red]")
            return
        
        # Show detailed info
        info_lines = [
            f"[bold]ID:[/bold] {template.id}",
            f"[bold]Name:[/bold] {template.name}",
            f"[bold]Description:[/bold] {template.description}",
            f"[bold]Category:[/bold] {template.category.value}",
            f"[bold]Version:[/bold] {template.version}",
        ]
        
        if template.dependencies:
            info_lines.append(f"[bold]Dependencies:[/bold] {', '.join(template.dependencies)}")
        
        if template.conflicts:
            info_lines.append(f"[bold]Conflicts:[/bold] {', '.join(template.conflicts)}")
        
        if template.required_ports:
            info_lines.append(f"[bold]Required Ports:[/bold] {', '.join(map(str, template.required_ports))}")
        
        if template.docker_services:
            info_lines.append(f"[bold]Docker Services:[/bold] {', '.join(template.docker_services.keys())}")
        
        panel = Panel(
            "\n".join(info_lines),
            title=f"Template Details: {template_id}",
            border_style="blue"
        )
        
        self.console.print(panel)
        Prompt.ask("Press Enter to continue", default="")
    
    def _validate_current_selection(self):
        """Validate current template selection."""
        if not self.selected_templates:
            self.console.print("[yellow]No templates selected to validate[/yellow]")
            return
        
        validation_result = self.orchestrator.select_templates(self.selected_templates)
        
        if validation_result.is_valid:
            self.console.print("[green]✓ Selection is valid![/green]")
            
            # Show resolved dependencies
            resolved_templates = self.orchestrator.get_current_selection()
            auto_added = resolved_templates - self.selected_templates
            if auto_added:
                self.console.print(f"[blue]Auto-added dependencies: {', '.join(auto_added)}[/blue]")
        else:
            self.console.print("[red]✗ Validation issues found:[/red]")
            for issue in validation_result.issues:
                self.console.print(f"  • [red]{issue.message}[/red]")
                if issue.suggestion:
                    self.console.print(f"    [dim]Suggestion: {issue.suggestion}[/dim]")
        
        Prompt.ask("Press Enter to continue", default="")
    
    def _preview_current_selection(self):
        """Preview what would be generated."""
        if not self.selected_templates:
            self.console.print("[yellow]No templates selected to preview[/yellow]")
            return
        
        preview = self.orchestrator.preview_generation(self.selected_templates)
        
        if not preview.get("valid", False):
            self.console.print("[red]Invalid selection - cannot generate preview[/red]")
            return
        
        # Show preview
        tree = Tree("📦 Generation Preview")
        
        # Templates
        templates_node = tree.add("🧩 Templates")
        for template_id in preview.get("resolved_templates", []):
            style = "green" if template_id in self.selected_templates else "blue"
            templates_node.add(f"[{style}]{template_id}[/{style}]")
        
        # Docker services
        if preview.get("docker_services"):
            services_node = tree.add("🐳 Docker Services")
            for service_name in preview["docker_services"].keys():
                services_node.add(f"[blue]{service_name}[/blue]")
        
        # Environment variables
        if preview.get("environment_variables"):
            env_count = len(preview["environment_variables"])
            tree.add(f"🔧 Environment Variables: [yellow]{env_count}[/yellow]")
        
        # Ports
        if preview.get("required_ports"):
            ports_node = tree.add("🌐 Required Ports")
            for port in sorted(preview["required_ports"]):
                ports_node.add(f"[cyan]{port}[/cyan]")
        
        self.console.print(tree)
        Prompt.ask("Press Enter to continue", default="")
    
    def _group_templates_by_category(self) -> Dict[str, List[TemplateMetadata]]:
        """Group templates by category."""
        categories = {}
        for template in self.templates:
            cat = template.category.value
            if cat not in categories:
                categories[cat] = []
            categories[cat].append(template)
        return categories
    
    def _get_filtered_templates(self) -> List[TemplateMetadata]:
        """Get templates based on current filters."""
        filtered = self.templates
        
        if self.category_filter:
            filtered = [t for t in filtered if t.category.value == self.category_filter]
        
        if self.search_filter:
            search_lower = self.search_filter.lower()
            filtered = [
                t for t in filtered 
                if (search_lower in t.name.lower() or 
                    search_lower in t.description.lower() or
                    search_lower in t.id.lower())
            ]
        
        return filtered
    
    def _has_conflicts(self, template_id: str) -> bool:
        """Check if template has conflicts with current selection."""
        template = self._get_template_by_id(template_id)
        if not template:
            return False
        
        for conflict in template.conflicts:
            if conflict in self.selected_templates:
                return True
        
        return False
    
    def _get_template_by_id(self, template_id: str) -> Optional[TemplateMetadata]:
        """Get template metadata by ID."""
        for template in self.templates:
            if template.id == template_id:
                return template
        return None
    
    def _parse_template_selection(self, selection: str, available_templates: List[TemplateMetadata]) -> List[str]:
        """Parse user selection input."""
        selected_ids = []
        
        for item in selection.split(','):
            item = item.strip()
            
            # Try as number (1-based index)
            try:
                index = int(item) - 1
                if 0 <= index < len(available_templates):
                    template = available_templates[index]
                    if template:
                        selected_ids.append(template.id)
                continue
            except ValueError:
                pass
            
            # Try as template ID
            if any(t.id == item for t in available_templates):
                selected_ids.append(item)
        
        return selected_ids
    
    def _show_instructions(self):
        """Show usage instructions."""
        instructions = [
            "🚀 Interactive Template Selection",
            "",
            "Use the menu options to:",
            "• Add templates to your project",
            "• Remove unwanted templates", 
            "• View detailed template information",
            "• Validate your selection",
            "• Preview what will be generated",
            "",
            "Templates with dependencies will be automatically included.",
            "Conflicting templates will be highlighted in red.",
        ]
        
        panel = Panel(
            "\n".join(instructions),
            title="Instructions",
            border_style="green"
        )
        
        self.console.print(panel)
        Prompt.ask("Press Enter to start", default="")