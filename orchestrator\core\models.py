"""
Data models for the template orchestrator system.

These models use Pydantic for validation and serialization,
following the Single Responsibility Principle.
"""

from enum import Enum
from typing import List, Dict, Set, Optional, Any, Union
from pathlib import Path
from dataclasses import dataclass, field
from datetime import datetime

from pydantic import BaseModel, Field, validator, ConfigDict


class TemplateCategory(str, Enum):
    """Categories for organizing templates."""
    BACKEND = "backend"
    FRONTEND = "frontend" 
    DATABASE = "database"
    INFRASTRUCTURE = "infrastructure"
    AI_ML = "ai_ml"
    MONITORING = "monitoring"
    SECURITY = "security"
    DEVELOPMENT = "development"
    UI_LIBRARY = "ui_library"
    BUILD_TOOL = "build_tool"
    PACKAGE_MANAGER = "package_manager"
    DESKTOP_FRAMEWORK = "desktop_framework"


class ConflictType(str, Enum):
    """Types of template conflicts."""
    MUTUALLY_EXCLUSIVE = "mutually_exclusive"  # Only one can be selected
    INCOMPATIBLE_VERSIONS = "incompatible_versions"  # Version conflicts
    RESOURCE_CONFLICT = "resource_conflict"  # Port/volume conflicts
    DEPENDENCY_CONFLICT = "dependency_conflict"  # Conflicting dependencies


class ValidationSeverity(str, Enum):
    """Severity levels for validation issues."""
    ERROR = "error"
    WARNING = "warning"
    INFO = "info"


class TemplateMetadata(BaseModel):
    """Comprehensive metadata for a template."""
    model_config = ConfigDict(frozen=True, extra='forbid')
    
    id: str = Field(..., description="Unique template identifier")
    name: str = Field(..., description="Human-readable template name")
    description: str = Field(..., description="Template description")
    version: str = Field(default="1.0.0", description="Template version")
    category: TemplateCategory = Field(..., description="Template category")
    
    # Dependencies and conflicts
    dependencies: List[str] = Field(default_factory=list, description="Required template dependencies")
    optional_dependencies: List[str] = Field(default_factory=list, description="Optional template dependencies") 
    conflicts: List[str] = Field(default_factory=list, description="Conflicting templates")
    conflict_types: Dict[str, ConflictType] = Field(default_factory=dict, description="Types of conflicts")
    
    # Docker and environment configuration
    docker_services: Dict[str, Dict[str, Any]] = Field(default_factory=dict, description="Docker service definitions")
    environment_variables: Dict[str, str] = Field(default_factory=dict, description="Required environment variables")
    required_ports: List[int] = Field(default_factory=list, description="Required network ports")
    volumes: List[str] = Field(default_factory=list, description="Required Docker volumes")
    
    # File system requirements
    required_files: List[str] = Field(default_factory=list, description="Required files/directories")
    generated_files: List[str] = Field(default_factory=list, description="Files generated by this template")
    
    # Metadata
    tags: List[str] = Field(default_factory=list, description="Template tags for search/filtering")
    author: Optional[str] = Field(default=None, description="Template author")
    license: Optional[str] = Field(default=None, description="Template license")
    documentation_url: Optional[str] = Field(default=None, description="Template documentation URL")
    repository_url: Optional[str] = Field(default=None, description="Template repository URL")
    
    @validator('id')
    def validate_id(cls, v):
        """Validate template ID format."""
        if not v.replace('_', '').replace('-', '').isalnum():
            raise ValueError('Template ID must contain only alphanumeric characters, hyphens, and underscores')
        return v.lower()
    
    @validator('required_ports')
    def validate_ports(cls, v):
        """Validate port numbers are in valid range."""
        for port in v:
            if not (1 <= port <= 65535):
                raise ValueError(f'Port {port} is not in valid range (1-65535)')
        return v


class ValidationIssue(BaseModel):
    """Individual validation issue."""
    severity: ValidationSeverity
    message: str
    template_id: Optional[str] = None
    suggestion: Optional[str] = None


class ValidationResult(BaseModel):
    """Result of validation operations."""
    is_valid: bool = Field(..., description="Whether validation passed")
    issues: List[ValidationIssue] = Field(default_factory=list, description="Validation issues")
    warnings: List[str] = Field(default_factory=list, description="Validation warnings")
    suggestions: List[str] = Field(default_factory=list, description="Improvement suggestions")
    
    @property
    def has_errors(self) -> bool:
        """Check if there are any error-level issues."""
        return any(issue.severity == ValidationSeverity.ERROR for issue in self.issues)
    
    @property
    def has_warnings(self) -> bool:
        """Check if there are any warning-level issues."""
        return any(issue.severity == ValidationSeverity.WARNING for issue in self.issues)
    
    def add_error(self, message: str, template_id: Optional[str] = None, suggestion: Optional[str] = None):
        """Add an error to the validation result."""
        self.issues.append(ValidationIssue(
            severity=ValidationSeverity.ERROR,
            message=message,
            template_id=template_id,
            suggestion=suggestion
        ))
        self.is_valid = False
    
    def add_warning(self, message: str, template_id: Optional[str] = None, suggestion: Optional[str] = None):
        """Add a warning to the validation result."""
        self.issues.append(ValidationIssue(
            severity=ValidationSeverity.WARNING,
            message=message,
            template_id=template_id,
            suggestion=suggestion
        ))


class DependencyResult(BaseModel):
    """Result of dependency resolution."""
    resolved_templates: List[str] = Field(..., description="Templates in dependency order")
    missing_dependencies: List[str] = Field(default_factory=list, description="Missing required dependencies")
    conflicts: List[tuple[str, str]] = Field(default_factory=list, description="Conflicting template pairs")
    circular_dependencies: List[List[str]] = Field(default_factory=list, description="Circular dependency chains")
    optional_suggestions: List[str] = Field(default_factory=list, description="Suggested optional dependencies")
    
    @property
    def is_valid(self) -> bool:
        """Check if dependency resolution is valid."""
        return len(self.missing_dependencies) == 0 and len(self.conflicts) == 0 and len(self.circular_dependencies) == 0


class GeneratedFile(BaseModel):
    """Information about a generated file."""
    path: str = Field(..., description="File path relative to project root")
    content_hash: Optional[str] = Field(default=None, description="SHA256 hash of file content")
    size_bytes: Optional[int] = Field(default=None, description="File size in bytes")
    generated_by: str = Field(..., description="Template or generator that created this file")
    is_executable: bool = Field(default=False, description="Whether file is executable")


class GenerationResult(BaseModel):
    """Result of project generation."""
    success: bool = Field(..., description="Whether generation was successful")
    project_path: str = Field(..., description="Path to generated project")
    generated_files: List[GeneratedFile] = Field(default_factory=list, description="List of generated files")
    errors: List[str] = Field(default_factory=list, description="Generation errors")
    warnings: List[str] = Field(default_factory=list, description="Generation warnings")
    generation_time_seconds: float = Field(..., description="Time taken to generate project")
    next_steps: List[str] = Field(default_factory=list, description="Recommended next steps")
    
    def add_generated_file(self, path: str, generated_by: str, **kwargs):
        """Add a generated file to the result."""
        self.generated_files.append(GeneratedFile(
            path=path,
            generated_by=generated_by,
            **kwargs
        ))


class ProjectConfig(BaseModel):
    """Configuration for project generation."""
    project_name: str = Field(..., description="Project name")
    project_path: str = Field(..., description="Project root directory path")
    selected_templates: Set[str] = Field(..., description="Selected template IDs")
    environment_variables: Dict[str, str] = Field(default_factory=dict, description="Custom environment variables")
    custom_configuration: Dict[str, Any] = Field(default_factory=dict, description="Custom configuration options")
    overwrite_existing: bool = Field(default=False, description="Whether to overwrite existing files")
    
    # Generation options
    generate_docker_compose: bool = Field(default=True, description="Generate Docker Compose file")
    generate_env_files: bool = Field(default=True, description="Generate environment files")
    generate_readme: bool = Field(default=True, description="Generate README file")
    generate_gitignore: bool = Field(default=True, description="Generate .gitignore file")
    
    @validator('project_name')
    def validate_project_name(cls, v):
        """Validate project name format."""
        if not v.replace('_', '').replace('-', '').isalnum():
            raise ValueError('Project name must contain only alphanumeric characters, hyphens, and underscores')
        return v


@dataclass
class TemplateConflict:
    """Represents a conflict between two templates."""
    template1: str
    template2: str
    conflict_type: ConflictType
    description: str
    resolution_suggestion: Optional[str] = None


@dataclass
class DependencyGraph:
    """Represents template dependency relationships."""
    nodes: Set[str] = field(default_factory=set)
    edges: Dict[str, Set[str]] = field(default_factory=dict)  # template -> dependencies
    reverse_edges: Dict[str, Set[str]] = field(default_factory=dict)  # template -> dependents
    
    def add_dependency(self, template: str, dependency: str):
        """Add a dependency relationship."""
        self.nodes.add(template)
        self.nodes.add(dependency)
        
        if template not in self.edges:
            self.edges[template] = set()
        self.edges[template].add(dependency)
        
        if dependency not in self.reverse_edges:
            self.reverse_edges[dependency] = set()
        self.reverse_edges[dependency].add(template)
    
    def get_dependencies(self, template: str) -> Set[str]:
        """Get direct dependencies of a template."""
        return self.edges.get(template, set())
    
    def get_dependents(self, template: str) -> Set[str]:
        """Get templates that depend on this template."""
        return self.reverse_edges.get(template, set())


@dataclass
class GenerationContext:
    """Context information for template generation."""
    project_config: ProjectConfig
    dependency_result: DependencyResult
    template_registry: Any  # Avoid circular import
    generation_timestamp: datetime = field(default_factory=datetime.now)
    user_preferences: Dict[str, Any] = field(default_factory=dict)
    
    def get_template_order(self) -> List[str]:
        """Get templates in generation order."""
        return self.dependency_result.resolved_templates


class TemplateStats(BaseModel):
    """Statistics about template usage and performance."""
    template_id: str
    usage_count: int = 0
    success_rate: float = 0.0
    average_generation_time: float = 0.0
    last_used: Optional[datetime] = None
    user_rating: Optional[float] = None
    
    def update_usage(self, success: bool, generation_time: float):
        """Update usage statistics."""
        self.usage_count += 1
        self.last_used = datetime.now()
        
        # Update success rate
        if self.usage_count == 1:
            self.success_rate = 1.0 if success else 0.0
        else:
            current_successes = self.success_rate * (self.usage_count - 1)
            if success:
                current_successes += 1
            self.success_rate = current_successes / self.usage_count
        
        # Update average generation time
        if self.usage_count == 1:
            self.average_generation_time = generation_time
        else:
            total_time = self.average_generation_time * (self.usage_count - 1) + generation_time
            self.average_generation_time = total_time / self.usage_count


class SystemConfiguration(BaseModel):
    """System-wide configuration for the orchestrator."""
    default_project_directory: str = Field(default="./projects", description="Default project creation directory")
    template_cache_size: int = Field(default=100, description="Maximum number of cached templates")
    max_concurrent_generations: int = Field(default=5, description="Maximum concurrent project generations")
    enable_telemetry: bool = Field(default=False, description="Enable usage telemetry")
    auto_update_templates: bool = Field(default=True, description="Automatically update template definitions")
    validation_strictness: str = Field(default="normal", description="Validation strictness level")
    
    # Security settings
    allow_custom_templates: bool = Field(default=True, description="Allow loading custom templates")
    require_template_signatures: bool = Field(default=False, description="Require cryptographic signatures for templates")
    sandbox_template_execution: bool = Field(default=True, description="Sandbox template execution")
    
    # Performance settings
    parallel_generation: bool = Field(default=True, description="Enable parallel file generation")
    cache_generated_content: bool = Field(default=True, description="Cache generated content for reuse")
    optimize_docker_layers: bool = Field(default=True, description="Optimize Docker layer generation")