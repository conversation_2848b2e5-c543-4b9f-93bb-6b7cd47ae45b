name: Python Unit Tests

on:
  workflow_call:
    inputs:
      python-version:
        description: 'Python version to use'
        required: false
        default: '3.12'
        type: string
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]
  workflow_dispatch:

jobs:
  test-agent-api:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Python ${{ inputs.python-version || '3.12' }}
      uses: actions/setup-python@v4
      with:
        python-version: ${{ inputs.python-version || '3.12' }}
        
    - name: Install Agent API dependencies
      run: |
        python -m pip install --upgrade pip
        cd 6_Agent_Deployment/backend_agent_api
        pip install -r requirements.txt
        
    - name: Run Agent API tests
      run: |
        cd 6_Agent_Deployment/backend_agent_api
        python -m pytest tests/ -v --tb=short
        
  test-rag-pipeline:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Python ${{ inputs.python-version || '3.12' }}
      uses: actions/setup-python@v4
      with:
        python-version: ${{ inputs.python-version || '3.12' }}
        
    - name: Install RAG Pipeline dependencies
      run: |
        python -m pip install --upgrade pip
        cd 6_Agent_Deployment/backend_rag_pipeline
        pip install -r requirements.txt
        
    - name: Run RAG Pipeline core tests
      run: |
        cd 6_Agent_Deployment/backend_rag_pipeline
        python -m pytest tests/ -v --tb=short
        
    - name: Run Google Drive tests
      run: |
        cd 6_Agent_Deployment/backend_rag_pipeline
        python -m pytest Google_Drive/tests/ -v --tb=short
        
    - name: Run Local Files tests
      run: |
        cd 6_Agent_Deployment/backend_rag_pipeline
        python -m pytest Local_Files/tests/ -v --tb=short