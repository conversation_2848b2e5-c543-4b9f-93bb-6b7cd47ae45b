"""
Error handling middleware.

This middleware provides centralized error handling and logging
following the Single Responsibility Principle.
"""

import traceback
from typing import Any, Dict

from fastapi import Request, Response, status
from fastapi.responses import JSONResponse
from loguru import logger
from starlette.middleware.base import BaseHTTPMiddleware


class ErrorHandlerMiddleware(BaseHTTPMiddleware):
    """
    Error handling middleware.
    
    Handles all uncaught exceptions and returns appropriate error responses
    while logging the errors for debugging.
    """
    
    async def dispatch(self, request: Request, call_next) -> Response:
        """
        Process request and handle any exceptions.
        
        Args:
            request: The incoming request
            call_next: The next middleware or endpoint
            
        Returns:
            The response, with error handling if an exception occurred
        """
        try:
            response = await call_next(request)
            return response
            
        except Exception as exc:
            return await self._handle_exception(request, exc)
    
    async def _handle_exception(self, request: Request, exc: Exception) -> JSONResponse:
        """
        Handle an exception and return appropriate error response.
        
        Args:
            request: The incoming request
            exc: The exception that occurred
            
        Returns:
            JSON error response
        """
        # Log the error with context
        error_id = self._generate_error_id()
        logger.error(
            f"Error {error_id} in {request.method} {request.url}: {str(exc)}",
            extra={
                "error_id": error_id,
                "method": request.method,
                "url": str(request.url),
                "client_host": request.client.host if request.client else None,
                "user_agent": request.headers.get("user-agent"),
                "exception_type": type(exc).__name__,
                "traceback": traceback.format_exc(),
            }
        )
        
        # Determine error response based on exception type
        if isinstance(exc, ValueError):
            return self._create_error_response(
                error_id=error_id,
                status_code=status.HTTP_400_BAD_REQUEST,
                error_type="validation_error",
                message=str(exc)
            )
        
        elif isinstance(exc, PermissionError):
            return self._create_error_response(
                error_id=error_id,
                status_code=status.HTTP_403_FORBIDDEN,
                error_type="permission_error", 
                message="Insufficient permissions"
            )
        
        elif isinstance(exc, FileNotFoundError):
            return self._create_error_response(
                error_id=error_id,
                status_code=status.HTTP_404_NOT_FOUND,
                error_type="not_found_error",
                message="Resource not found"
            )
        
        else:
            # Generic server error
            return self._create_error_response(
                error_id=error_id,
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                error_type="internal_server_error",
                message="An internal server error occurred"
            )
    
    def _create_error_response(
        self,
        error_id: str,
        status_code: int,
        error_type: str,
        message: str
    ) -> JSONResponse:
        """
        Create a standardized error response.
        
        Args:
            error_id: Unique error identifier
            status_code: HTTP status code
            error_type: Type of error
            message: Error message
            
        Returns:
            JSON error response
        """
        error_response: Dict[str, Any] = {
            "error": {
                "id": error_id,
                "type": error_type,
                "message": message,
                "timestamp": self._get_timestamp(),
            }
        }
        
        return JSONResponse(
            status_code=status_code,
            content=error_response
        )
    
    def _generate_error_id(self) -> str:
        """Generate a unique error ID for tracking."""
        import uuid
        return str(uuid.uuid4())[:8]
    
    def _get_timestamp(self) -> str:
        """Get current timestamp in ISO format."""
        from datetime import datetime
        return datetime.utcnow().isoformat() + "Z"