"""
Dependency injection container.

This module provides a centralized dependency injection container
following the Dependency Inversion Principle.
"""

import asyncio
from typing import Any, Dict, Type, TypeVar, Optional

from loguru import logger

from .config.application import get_settings
from .database import DatabaseManager

# Import integrations
from ..integrations.mcp.server import MCPServer
from ..integrations.ollama.client import OllamaClient
from ..integrations.rag.service import RAGService

T = TypeVar('T')


class Container:
    """
    Dependency injection container.

    Manages the lifecycle and dependencies of all application services
    following the Dependency Inversion Principle.
    """

    def __init__(self):
        """Initialize the container."""
        self._services: Dict[str, Any] = {}
        self._integrations: Dict[str, Any] = {}
        self._initialized = False
        self.settings = get_settings()
    
    async def initialize(self) -> None:
        """
        Initialize all services and their dependencies.
        
        Services are initialized in the correct order to respect dependencies.
        """
        if self._initialized:
            logger.warning("Container already initialized")
            return
        
        logger.info("Initializing dependency injection container...")
        
        try:
            # Initialize core infrastructure
            await self._initialize_database()
            await self._initialize_integrations()

            self._initialized = True
            logger.info("Dependency injection container initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize container: {e}")
            await self.cleanup()
            raise
    
    async def cleanup(self) -> None:
        """
        Cleanup all services and close connections.
        
        Services are cleaned up in reverse order of initialization.
        """
        logger.info("Cleaning up dependency injection container...")
        
        # Cleanup services
        for service_name, service in self._services.items():
            if hasattr(service, 'cleanup'):
                try:
                    await service.cleanup()
                    logger.debug(f"Cleaned up service: {service_name}")
                except Exception as e:
                    logger.error(f"Error cleaning up service {service_name}: {e}")
        
        # Cleanup integrations
        for integration_name, integration in self._integrations.items():
            if hasattr(integration, 'close'):
                try:
                    await integration.close()
                    logger.debug(f"Cleaned up integration: {integration_name}")
                except Exception as e:
                    logger.error(f"Error cleaning up integration {integration_name}: {e}")

        # Clear containers
        self._services.clear()
        self._integrations.clear()
        self._initialized = False
        
        logger.info("Dependency injection container cleanup complete")
    
    def get_service(self, service_type: Type[T]) -> T:
        """
        Get a service instance by type.
        
        Args:
            service_type: The type of service to retrieve
            
        Returns:
            The service instance
            
        Raises:
            ValueError: If the service is not found or container not initialized
        """
        if not self._initialized:
            raise ValueError("Container not initialized. Call initialize() first.")
        
        service_name = service_type.__name__
        service = self._services.get(service_name)
        
        if service is None:
            raise ValueError(f"Service {service_name} not found in container")
        
        return service
    
    def get_integration(self, integration_type: Type[T]) -> T:
        """
        Get an integration instance by type.

        Args:
            integration_type: The type of integration to retrieve

        Returns:
            The integration instance

        Raises:
            ValueError: If the integration is not found or container not initialized
        """
        if not self._initialized:
            raise ValueError("Container not initialized. Call initialize() first.")

        integration_name = integration_type.__name__
        integration = self._integrations.get(integration_name)

        if integration is None:
            raise ValueError(f"Integration {integration_name} not found in container")

        return integration
    
    async def _initialize_database(self) -> None:
        """Initialize database manager."""
        logger.debug("Initializing database manager...")
        database_manager = DatabaseManager()
        await database_manager.initialize()
        self._services["DatabaseManager"] = database_manager
    
    async def _initialize_integrations(self) -> None:
        """Initialize all integrations."""
        logger.debug("Initializing integrations...")

        # Initialize MCP Server
        try:
            mcp_server = MCPServer("loni-mcp-server", "1.0.0")
            self._integrations["MCPServer"] = mcp_server
            logger.debug("Initialized MCP Server")
        except Exception as e:
            logger.error(f"Failed to initialize MCP Server: {e}")
            # Don't raise - MCP is optional

        # Initialize Ollama Client
        try:
            ollama_client = OllamaClient(
                base_url=self.settings.ollama.host + ":" + str(self.settings.ollama.port)
            )
            # Test connection
            is_healthy = await ollama_client.health_check()
            if is_healthy:
                self._integrations["OllamaClient"] = ollama_client
                logger.debug("Initialized Ollama Client")
            else:
                logger.warning("Ollama server not available - client not initialized")
        except Exception as e:
            logger.error(f"Failed to initialize Ollama Client: {e}")
            # Don't raise - Ollama is optional

        # Initialize RAG Service
        try:
            # RAG service will be implemented later
            # rag_service = RAGService()
            # self._integrations["RAGService"] = rag_service
            logger.debug("RAG Service initialization skipped (not implemented)")
        except Exception as e:
            logger.error(f"Failed to initialize RAG Service: {e}")
            # Don't raise - RAG is optional
    
    @property
    def is_initialized(self) -> bool:
        """Check if the container is initialized."""
        return self._initialized


# Global container instance
_container: Optional[Container] = None


async def get_container() -> Container:
    """
    Get the global container instance.

    Returns:
        The initialized container instance

    Raises:
        ValueError: If container is not initialized
    """
    global _container
    if _container is None or not _container.is_initialized:
        raise ValueError("Container not initialized. Initialize in application startup.")
    return _container


def set_container(container: Container) -> None:
    """
    Set the global container instance.

    Args:
        container: The container instance to set
    """
    global _container
    _container = container