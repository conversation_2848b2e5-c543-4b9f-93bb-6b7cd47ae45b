/**
 * Chat message component with role-based styling
 */
'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Card } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarFallback } from '@/components/ui/avatar'
import { 
  Co<PERSON>, 
  Refresh<PERSON>w, 
  User, 
  Bot, 
  Check,
  MoreHorizontal,
  ThumbsUp,
  ThumbsDown
} from 'lucide-react'
import { formatRelativeTime } from '@/lib/utils'
import type { Message } from '@/types'

interface ChatMessageProps {
  message: Message
  isGenerating?: boolean
  streamingContent?: string
  onRegenerate?: () => void
  onCopy?: () => void
  onFeedback?: (messageId: string, feedback: 'up' | 'down') => void
  className?: string
}

export function ChatMessage({
  message,
  isGenerating = false,
  streamingContent,
  onRegenerate,
  onCopy,
  onFeedback,
  className
}: ChatMessageProps) {
  const [copied, setCopied] = useState(false)
  const [showActions, setShowActions] = useState(false)

  const isUser = message.role === 'user'
  const isAssistant = message.role === 'assistant'
  const isSystem = message.role === 'system'

  const displayContent = streamingContent || message.content

  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(displayContent)
      setCopied(true)
      setTimeout(() => setCopied(false), 2000)
      onCopy?.()
    } catch (error) {
      console.error('Failed to copy text:', error)
    }
  }

  const handleFeedback = (feedback: 'up' | 'down') => {
    onFeedback?.(message.id, feedback)
  }

  return (
    <div
      className={`group relative mb-6 ${className}`}
      onMouseEnter={() => setShowActions(true)}
      onMouseLeave={() => setShowActions(false)}
    >
      <div className={`flex gap-4 ${isUser ? 'justify-end' : 'justify-start'}`}>
        {/* Avatar for non-user messages */}
        {!isUser && (
          <div className="flex-shrink-0">
            <Avatar className="w-8 h-8">
              <AvatarFallback className={`
                ${isAssistant ? 'bg-ai-primary text-white' : 'bg-gray-500 text-white'}
              `}>
                {isAssistant ? <Bot className="w-4 h-4" /> : 'S'}
              </AvatarFallback>
            </Avatar>
          </div>
        )}

        {/* Message Content */}
        <div className={`
          flex-1 max-w-[80%] 
          ${isUser ? 'flex justify-end' : ''}
        `}>
          <Card className={`
            p-4 transition-all duration-200
            ${isUser 
              ? 'bg-loni-primary text-white ml-auto' 
              : isAssistant 
                ? 'bg-white border border-gray-200 hover:border-ai-primary/30' 
                : 'bg-gray-50 border border-gray-200'
            }
            ${isGenerating && streamingContent ? 'animate-pulse' : ''}
          `}>
            {/* Message Header */}
            <div className="flex items-center justify-between mb-2">
              <div className="flex items-center gap-2">
                <span className={`
                  text-sm font-medium
                  ${isUser ? 'text-white/90' : 'text-gray-900'}
                `}>
                  {isUser ? 'You' : isAssistant ? 'Assistant' : 'System'}
                </span>
                
                {message.model_name && isAssistant && (
                  <Badge variant="ai-secondary" className="text-xs py-0 px-2">
                    {message.model_name}
                  </Badge>
                )}
                
                {message.tokens_used && (
                  <Badge variant="outline" className="text-xs py-0 px-2">
                    {message.tokens_used} tokens
                  </Badge>
                )}
              </div>

              <span className={`
                text-xs
                ${isUser ? 'text-white/70' : 'text-gray-500'}
              `}>
                {formatRelativeTime(message.created_at)}
              </span>
            </div>

            {/* Message Content */}
            <div className={`
              prose prose-sm max-w-none
              ${isUser ? 'prose-invert' : ''}
            `}>
              <div className="whitespace-pre-wrap break-words">
                {displayContent}
              </div>
              
              {/* Streaming cursor */}
              {isGenerating && streamingContent && (
                <span className="inline-block w-2 h-4 bg-current animate-pulse ml-1" />
              )}
            </div>

            {/* Message Actions */}
            {showActions && !isGenerating && (
              <div className="flex items-center justify-end gap-1 mt-3 pt-2 border-t border-current/10">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleCopy}
                  className={`
                    h-7 px-2 text-xs
                    ${isUser 
                      ? 'text-white/70 hover:text-white hover:bg-white/10' 
                      : 'text-gray-500 hover:text-gray-700 hover:bg-gray-100'
                    }
                  `}
                >
                  {copied ? (
                    <>
                      <Check className="w-3 h-3 mr-1" />
                      Copied
                    </>
                  ) : (
                    <>
                      <Copy className="w-3 h-3 mr-1" />
                      Copy
                    </>
                  )}
                </Button>

                {isAssistant && onRegenerate && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={onRegenerate}
                    className="h-7 px-2 text-xs text-gray-500 hover:text-gray-700 hover:bg-gray-100"
                  >
                    <RefreshCw className="w-3 h-3 mr-1" />
                    Regenerate
                  </Button>
                )}

                {isAssistant && onFeedback && (
                  <>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleFeedback('up')}
                      className="h-7 px-2 text-gray-500 hover:text-green-600 hover:bg-green-50"
                    >
                      <ThumbsUp className="w-3 h-3" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleFeedback('down')}
                      className="h-7 px-2 text-gray-500 hover:text-red-600 hover:bg-red-50"
                    >
                      <ThumbsDown className="w-3 h-3" />
                    </Button>
                  </>
                )}
              </div>
            )}
          </Card>
        </div>

        {/* Avatar for user messages */}
        {isUser && (
          <div className="flex-shrink-0">
            <Avatar className="w-8 h-8">
              <AvatarFallback className="bg-loni-primary text-white">
                <User className="w-4 h-4" />
              </AvatarFallback>
            </Avatar>
          </div>
        )}
      </div>
    </div>
  )
}