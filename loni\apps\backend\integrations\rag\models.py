"""
RAG data models and structures.

This module defines the data models used in RAG operations.
"""

from datetime import datetime
from typing import Any, Dict, List, Optional
from uuid import UUID, uuid4
from enum import Enum

from pydantic import BaseModel, Field


class DocumentType(str, Enum):
    """Document type enumeration."""
    TEXT = "text"
    PDF = "pdf"
    DOCX = "docx"
    MARKDOWN = "markdown"
    HTML = "html"
    CODE = "code"


class EmbeddingModel(str, Enum):
    """Embedding model enumeration."""
    OPENAI_ADA_002 = "text-embedding-ada-002"
    OPENAI_3_SMALL = "text-embedding-3-small"
    OPENAI_3_LARGE = "text-embedding-3-large"
    NOMIC_EMBED = "nomic-embed-text"
    SENTENCE_TRANSFORMERS = "all-MiniLM-L6-v2"


class Document(BaseModel):
    """Document model for RAG system."""
    
    id: UUID = Field(default_factory=uuid4, description="Document ID")
    title: str = Field(..., description="Document title")
    content: str = Field(..., description="Document content")
    document_type: DocumentType = Field(..., description="Document type")
    source_url: Optional[str] = Field(default=None, description="Source URL")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Additional metadata")
    user_id: Optional[UUID] = Field(default=None, description="Owner user ID")
    created_at: datetime = Field(default_factory=datetime.utcnow, description="Creation timestamp")
    updated_at: datetime = Field(default_factory=datetime.utcnow, description="Update timestamp")
    
    class Config:
        json_schema_extra = {
            "example": {
                "title": "AI Research Paper",
                "content": "This paper discusses the latest advances in artificial intelligence...",
                "document_type": "pdf",
                "source_url": "https://arxiv.org/abs/2301.00001",
                "metadata": {
                    "authors": ["John Doe", "Jane Smith"],
                    "publication_date": "2023-01-01",
                    "tags": ["AI", "machine learning"]
                }
            }
        }


class DocumentChunk(BaseModel):
    """Document chunk for vector storage."""
    
    id: UUID = Field(default_factory=uuid4, description="Chunk ID")
    document_id: UUID = Field(..., description="Parent document ID")
    content: str = Field(..., description="Chunk content")
    chunk_index: int = Field(..., description="Chunk position in document")
    start_char: int = Field(..., description="Start character position")
    end_char: int = Field(..., description="End character position")
    embedding: Optional[List[float]] = Field(default=None, description="Text embedding vector")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Chunk metadata")
    
    @property
    def length(self) -> int:
        """Get chunk content length."""
        return len(self.content)


class SearchResult(BaseModel):
    """Search result from vector database."""
    
    document_id: UUID = Field(..., description="Document ID")
    chunk_id: UUID = Field(..., description="Chunk ID")
    content: str = Field(..., description="Matching content")
    score: float = Field(..., description="Similarity score")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Result metadata")
    
    class Config:
        json_schema_extra = {
            "example": {
                "document_id": "123e4567-e89b-12d3-a456-************",
                "chunk_id": "987fcdeb-51a2-43d1-b789-123456789abc",
                "content": "Artificial intelligence is transforming how we work...",
                "score": 0.95,
                "metadata": {
                    "title": "AI Research Paper",
                    "chunk_index": 5
                }
            }
        }


class SearchQuery(BaseModel):
    """Search query parameters."""
    
    query: str = Field(..., description="Search query text")
    limit: int = Field(default=5, description="Maximum results")
    score_threshold: float = Field(default=0.7, description="Minimum similarity score")
    user_id: Optional[UUID] = Field(default=None, description="Filter by user ID")
    document_types: Optional[List[DocumentType]] = Field(default=None, description="Filter by document types")
    metadata_filters: Optional[Dict[str, Any]] = Field(default=None, description="Metadata filters")
    
    class Config:
        json_schema_extra = {
            "example": {
                "query": "machine learning algorithms",
                "limit": 10,
                "score_threshold": 0.8,
                "document_types": ["pdf", "markdown"],
                "metadata_filters": {"tags": ["AI", "research"]}
            }
        }


class SearchResponse(BaseModel):
    """Search response with results."""
    
    query: str = Field(..., description="Original query")
    results: List[SearchResult] = Field(..., description="Search results")
    total_results: int = Field(..., description="Total number of results")
    search_time_ms: float = Field(..., description="Search time in milliseconds")
    
    @property
    def has_results(self) -> bool:
        """Check if search returned results."""
        return len(self.results) > 0


class EmbeddingRequest(BaseModel):
    """Embedding generation request."""
    
    texts: List[str] = Field(..., description="Texts to embed")
    model: EmbeddingModel = Field(default=EmbeddingModel.OPENAI_ADA_002, description="Embedding model")
    
    class Config:
        json_schema_extra = {
            "example": {
                "texts": ["Hello world", "How are you?"],
                "model": "text-embedding-ada-002"
            }
        }


class EmbeddingResponse(BaseModel):
    """Embedding generation response."""
    
    embeddings: List[List[float]] = Field(..., description="Generated embeddings")
    model: str = Field(..., description="Model used")
    dimensions: int = Field(..., description="Embedding dimensions")
    
    @property
    def count(self) -> int:
        """Get number of embeddings."""
        return len(self.embeddings)


class ChunkingConfig(BaseModel):
    """Document chunking configuration."""
    
    chunk_size: int = Field(default=512, description="Maximum chunk size in characters")
    chunk_overlap: int = Field(default=50, description="Overlap between chunks")
    preserve_sentences: bool = Field(default=True, description="Preserve sentence boundaries")
    min_chunk_size: int = Field(default=100, description="Minimum chunk size")
    
    class Config:
        json_schema_extra = {
            "example": {
                "chunk_size": 1000,
                "chunk_overlap": 100,
                "preserve_sentences": True,
                "min_chunk_size": 200
            }
        }


class ProcessingResult(BaseModel):
    """Document processing result."""
    
    document_id: UUID = Field(..., description="Processed document ID")
    chunks_created: int = Field(..., description="Number of chunks created")
    embeddings_generated: int = Field(..., description="Number of embeddings generated")
    processing_time_ms: float = Field(..., description="Processing time in milliseconds")
    success: bool = Field(..., description="Processing success status")
    error_message: Optional[str] = Field(default=None, description="Error message if failed")


class VectorPoint(BaseModel):
    """Vector database point."""
    
    id: str = Field(..., description="Point ID")
    vector: List[float] = Field(..., description="Embedding vector")
    payload: Dict[str, Any] = Field(default_factory=dict, description="Point metadata")


class CollectionInfo(BaseModel):
    """Vector collection information."""
    
    name: str = Field(..., description="Collection name")
    vectors_count: int = Field(..., description="Number of vectors")
    indexed_vectors_count: int = Field(..., description="Number of indexed vectors")
    points_count: int = Field(..., description="Number of points")
    segments_count: int = Field(..., description="Number of segments")
    config: Dict[str, Any] = Field(default_factory=dict, description="Collection configuration")
    status: str = Field(..., description="Collection status")


class RAGContext(BaseModel):
    """RAG context for AI generation."""
    
    query: str = Field(..., description="Original query")
    context_chunks: List[str] = Field(..., description="Retrieved context chunks")
    sources: List[SearchResult] = Field(..., description="Source search results")
    total_context_length: int = Field(..., description="Total context length")
    
    @property
    def context_text(self) -> str:
        """Get combined context text."""
        return "\n\n".join(self.context_chunks)
    
    @property
    def source_count(self) -> int:
        """Get number of sources."""
        return len(self.sources)


# Default embedding dimensions for different models
EMBEDDING_DIMENSIONS = {
    EmbeddingModel.OPENAI_ADA_002: 1536,
    EmbeddingModel.OPENAI_3_SMALL: 1536,
    EmbeddingModel.OPENAI_3_LARGE: 3072,
    EmbeddingModel.NOMIC_EMBED: 768,
    EmbeddingModel.SENTENCE_TRANSFORMERS: 384,
}


# Default chunking configurations for different document types
DEFAULT_CHUNKING_CONFIGS = {
    DocumentType.TEXT: ChunkingConfig(chunk_size=512, chunk_overlap=50),
    DocumentType.PDF: ChunkingConfig(chunk_size=1000, chunk_overlap=100),
    DocumentType.DOCX: ChunkingConfig(chunk_size=800, chunk_overlap=80),
    DocumentType.MARKDOWN: ChunkingConfig(chunk_size=600, chunk_overlap=60),
    DocumentType.HTML: ChunkingConfig(chunk_size=800, chunk_overlap=80),
    DocumentType.CODE: ChunkingConfig(chunk_size=400, chunk_overlap=40, preserve_sentences=False),
}
