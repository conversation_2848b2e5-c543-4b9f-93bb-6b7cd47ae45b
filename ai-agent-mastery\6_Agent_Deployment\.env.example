# =============================================================================
# DYNAMOUS AI AGENT DEPLOYMENT CONFIGURATION
# =============================================================================

# Determines how environment variables are loaded
# Set this to either production or development
ENVIRONMENT=

# =============================================================================
# LLM CONFIGURATION
# =============================================================================

# The provider for your LLM
# Set this to either openai, openrouter, or ollama
# This is needed on top of the base URL for Mem0 (long term memory)
LLM_PROVIDER=openai

# Base URL for the OpenAI compatible instance (default is https://api.openai.com/v1)
# OpenAI: https://api.openai.com/v1
# Ollama (example): http://localhost:11434/v1
# OpenRouter: https://openrouter.ai/api/v1
LLM_BASE_URL=https://api.openai.com/v1

# OpenAI: https://help.openai.com/en/articles/4936850-where-do-i-find-my-openai-api-key
# Open Router: Get your API Key here after registering: https://openrouter.ai/keys
# Ollama: No need to set this unless you specifically configured an API key
LLM_API_KEY=your_openai_api_key_here

# The LLM you want to use for the agents. Make sure this LLM supports tools (especially important if using Ollama)!
# OpenAI example: gpt-4o-mini
# OpenRouter example: anthropic/claude-3.7-sonnet
# Ollama example: qwen2.5:14b-instruct-8k
LLM_CHOICE=gpt-4o-mini

# The LLM you want to use for image analysis.
# Make sure this LLM supports vision (especially important if using Ollama or OpenRouter)!
# OpenAI example: gpt-4o-mini
# OpenRouter example: anthropic/claude-3.7-sonnet
# Ollama example: llava:7b
VISION_LLM_CHOICE=gpt-4o-mini

# =============================================================================
# EMBEDDING CONFIGURATION  
# =============================================================================

# The provider for your embedding model
# Set this to either openai, or ollama (openrouter doesn't have embedding models)
# This is needed on top of the base URL for Mem0 (long term memory)
EMBEDDING_PROVIDER=openai

# Base URL for the OpenAI compatible instance that has embedding models (default is https://api.openai.com/v1)
# OpenAI: https://api.openai.com/v1
# Ollama (example): http://localhost:11434/v1
EMBEDDING_BASE_URL=https://api.openai.com/v1

# OpenAI: https://help.openai.com/en/articles/4936850-where-do-i-find-my-openai-api-key
# Ollama: No need to set this unless you specifically configured an API key
EMBEDDING_API_KEY=your_openai_api_key_here

# The embedding model you want to use for RAG.
# Make sure the embeddings column in your database has the same dimensions as this embedding model!
# OpenAI example: text-embedding-3-small
# Ollama example: nomic-embed-text
EMBEDDING_MODEL_CHOICE=text-embedding-3-small

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================

# Postgres DB URL used for Mem0
# Format: postgresql://[user]:[password]@[host]:[port]/[database_name]
# Example: postgresql://postgres:mypassword@localhost:5432/mydb
# For Supabase Postgres connection, you can find this in "Connect" (top middle of Supabase dashboard) -> Transaction pooler
# For Local AI in the container: ****************************************/postgres
DATABASE_URL=postgresql://postgres:password@localhost:5432/postgres

# Supabase configuration
# Get these from your Supabase project settings -> API
# https://supabase.com/dashboard/project/<your project ID>/settings/api
# Set the Supabase URL to http://kong:8000 if using the Local AI Package and the agent is in the Docker Compose localai network
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_SERVICE_KEY=your_supabase_service_key_here

# =============================================================================
# WEB SEARCH CONFIGURATION
# =============================================================================

# Set your Brave API key if using Brave for agent web search (leave empty if using SearXNG)
# Get your key by going to the following link after signing up for Brave:
# https://api.search.brave.com/app/keys
BRAVE_API_KEY=your_brave_api_key_here

# Set the SearXNG endpoint if using SearXNG for agent web search (leave empty if using Brave)
# For the local AI package - this will be:
#    http://localhost:8080 if your agent is running outside of Docker
#    http://searxng:8080 if your agent is running in a container in the local-ai network
SEARXNG_BASE_URL=

# =============================================================================
# AGENT OBSERVABILITY CONFIGURATION (Optional)
# =============================================================================

# LangFuse configuration for agent observability (optional - leave empty to disable)
# Provides detailed insights into agent conversations, performance metrics, and debugging
# Get your keys from https://cloud.langfuse.com/ after creating a project
# Langfuse host will be http://langfuse-web:3000 (or your domain) for the Local AI Package and https://cloud.langfuse.com for cloud
LANGFUSE_PUBLIC_KEY=
LANGFUSE_SECRET_KEY=
LANGFUSE_HOST=https://cloud.langfuse.com

# =============================================================================
# RAG PIPELINE CONFIGURATION
# =============================================================================

# Controls which pipeline to run:
# - "local": Watch local files in mounted volume
# - "google_drive": Watch Google Drive files via API
RAG_PIPELINE_TYPE=local

# Controls how the RAG pipeline runs:
# - "continuous": Runs continuously, checking for changes at regular intervals
# - "single": Performs one check for changes and exits (for cron jobs, cloud schedulers)
RUN_MODE=continuous

# Interval in seconds between checks (continuous mode only)
CHECK_INTERVAL=60

# Unique identifier for this pipeline instance (required for single-run mode)
# Used for database state management to track last_check_time and known_files
# Examples: "prod-drive-pipeline", "dev-local-pipeline", "staging-pipeline"
RAG_PIPELINE_ID=

# =============================================================================
# GOOGLE DRIVE CONFIGURATION (for RAG Pipeline)
# =============================================================================

# Google Drive service account credentials (JSON format as string)
# Required for serverless/cloud deployment without interactive OAuth2
# Get this from Google Cloud Console -> Service Accounts -> Create Key (JSON)
# Example: GOOGLE_DRIVE_CREDENTIALS_JSON={"type":"service_account","project_id":"your-project",...}
# For docker, do not wrap your credentials in quotes or single quotes!
GOOGLE_DRIVE_CREDENTIALS_JSON=

# Full steps for the service account credentials:
# Go to console.cloud.google.com
# Click on the organization/project in the top left
# Click on the three dots in the top right -> IAM and Permissions
# Edit/add your user and add the role Organization Policy Administrator
# Go to Organization Policies with the left side navigation
# Search for “Disable service account key creation”
# Edit the policy -> “Override parent's policy” -> Make sure there is one rule that says new rule and enforcement is “Off”
# Save
# Go back to your project within the organization you just edited
# Go to APIs and Services
# Credentials -> Manage Service Accounts -> Create New Service Account
# Enter in a name and that’s in, then select JSON and download those credentials
# Refresh the page then copy the email address of this service account and add them to the folder to watch for RAG
# Enter the JSON for the credentials into your .env, but get rid of the newlines for the JSON


# Override the Google Drive folder ID to watch (optional)
# If not set, uses the folder_id from config.json or watches entire Drive
# Get folder ID from Google Drive URL: https://drive.google.com/drive/folders/FOLDER_ID_HERE
RAG_WATCH_FOLDER_ID=

# =============================================================================
# LOCAL FILES CONFIGURATION (for RAG Pipeline)
# =============================================================================

# Override the local directory to watch for files (optional)
# If not set, uses the watch_directory from config.json (relative to script location)
# IMPORTANT: This must be the path INSIDE the Docker container, not the host path (if running with Docker)
# Default container path: /app/Local_Files/data (mounted from ./rag-documents on host)
# Examples: "/app/Local_Files/data", "/app/mounted-docs", "/mnt/shared/documents"
RAG_WATCH_DIRECTORY=/app/Local_Files/data

# =============================================================================
# FRONTEND CONFIGURATION
# =============================================================================

# Frontend build-time environment variables
# These are baked into the frontend build and cannot be changed at runtime
VITE_SUPABASE_URL=https://your-project.supabase.co
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key_here
VITE_AGENT_ENDPOINT=http://localhost:8001/api/pydantic-agent
VITE_ENABLE_STREAMING=true

# Optional: LangFuse integration for admin dashboard
# Enables clickable links to view conversations in LangFuse
# Format: http://your-langfuse-host/project/your-project-id
# Example: http://localhost:3000/project/cm9n7mcx60006ph071x425gar
VITE_LANGFUSE_HOST_WITH_PROJECT=

# =============================================================================
# REVERSE PROXY CONFIGURATION (Caddy)
# =============================================================================

# Hostnames for Caddy reverse proxy routes (CLOUD DEPLOYMENT ONLY)
# Only required for cloud deployment with custom domains
# For local AI deployment, edit lines 2 and 21 in caddy-addon.conf and copy to the caddy-addon/ folder in the Local AI Package
# Examples: agent.yourdomain.com, chat.yourdomain.com
# Replace internal by your email (require to create a Let's Encrypt certificate)
# AGENT_API_HOSTNAME=agent.yourdomain.com
# FRONTEND_HOSTNAME=chat.yourdomain.com
# LETSENCRYPT_EMAIL=internal


# =============================================================================
# DEPLOYMENT EXAMPLES
# =============================================================================

# Example 1: Local Development with Local Files
# RAG_PIPELINE_TYPE=local
# RUN_MODE=continuous
# RAG_PIPELINE_ID=dev-local-pipeline
# RAG_WATCH_DIRECTORY=/app/Local_Files/data
# CHECK_INTERVAL=30

# Example 2: Production with Google Drive
# RAG_PIPELINE_TYPE=google_drive
# RUN_MODE=continuous
# RAG_PIPELINE_ID=prod-drive-pipeline
# GOOGLE_DRIVE_CREDENTIALS_JSON='{"type":"service_account",...}'
# RAG_WATCH_FOLDER_ID=1A2B3C4D5E6F7G8H9I0J
# CHECK_INTERVAL=60

# Example 3: Scheduled Processing (Single-Run Mode)
# RAG_PIPELINE_TYPE=local
# RUN_MODE=single
# RAG_PIPELINE_ID=cron-local-pipeline
# RAG_WATCH_DIRECTORY=/mnt/shared/documents

# =============================================================================
# USAGE INSTRUCTIONS
# =============================================================================

# 1. CONTINUOUS MODE (Default for Docker Compose):
#    - Set RAG_PIPELINE_TYPE=local or google_drive
#    - Set RUN_MODE=continuous (or leave unset)
#    - Pipeline runs indefinitely, checking for changes at CHECK_INTERVAL
#    - Suitable for: Docker containers, VMs, cloud services

# 2. SINGLE-RUN MODE (For Schedulers):
#    - Set RAG_PIPELINE_TYPE=local or google_drive
#    - Set RUN_MODE=single
#    - Set RAG_PIPELINE_ID to unique identifier
#    - Pipeline runs once and exits with status codes:
#      * 0: Success
#      * 1: Retry needed (temporary errors)
#      * 2: Configuration error (don't retry)
#      * 3: Authentication error (don't retry)

# 3. DATABASE STATE MANAGEMENT:
#    - When RAG_PIPELINE_ID is set, state is stored in Supabase
#    - Tracks last_check_time and known_files in JSONB format
#    - Enables proper change detection across multiple runs
#    - Falls back to config.json files when RAG_PIPELINE_ID is unset

# 4. DOCKER VOLUMES:
#    - Local files: Place documents in ./rag-documents/ directory (maps to /app/Local_Files/data in container)
#    - Use RAG_WATCH_DIRECTORY to override the container path (not the host path)
#    - Google Drive: Place OAuth2 credentials.json in ./google-credentials/ directory
#    - Service account: Use GOOGLE_DRIVE_CREDENTIALS_JSON environment variable

# =============================================================================
# TROUBLESHOOTING
# =============================================================================

# Common Issues:
# 1. "RAG Pipeline container keeps restarting"
#    - Check SUPABASE_URL and SUPABASE_SERVICE_KEY are correct
#    - Ensure embedding model dimensions match database schema
#    - Check logs: docker logs dynamous-rag-pipeline

# 2. "Google Drive authentication failed"
#    - Verify GOOGLE_DRIVE_CREDENTIALS_JSON is valid JSON
#    - Ensure service account has Google Drive API access
#    - Check folder permissions for service account

# 3. "Local files not being processed"
#    - Verify ./rag-documents directory exists and has files
#    - Check file permissions in Docker volume
#    - Ensure supported file types (PDF, TXT, CSV, etc.)
#    - Remember RAG_WATCH_DIRECTORY must be the container path, not host path

# 4. "Frontend not connecting to agent"
#    - Verify VITE_AGENT_ENDPOINT points to correct agent API URL
#    - Check that agent-api service is running and healthy
#    - For external access, update VITE_AGENT_ENDPOINT to public URL

# 5. "Database connection failed"
#    - Verify SUPABASE_URL and SUPABASE_SERVICE_KEY
#    - Ensure all required SQL schema files have been run
#    - Check Supabase project settings and API access