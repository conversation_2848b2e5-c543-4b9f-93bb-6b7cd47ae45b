[project]
name = "loni-backend"
version = "1.0.0"
description = "LONI AI Platform Backend API"
authors = [
    {name = "LONI Team", email = "<EMAIL>"}
]
readme = "README.md"
license = {text = "MIT"}
requires-python = ">=3.11"

dependencies = [
    # Core FastAPI stack
    "fastapi>=0.104.0",
    "uvicorn[standard]>=0.24.0",
    "gunicorn>=21.2.0",
    
    # Database and ORM
    "sqlalchemy>=2.0.0",
    "asyncpg>=0.29.0",
    "alembic>=1.12.0",
    
    # AI and ML
    "openai>=1.3.0",
    "anthropic>=0.7.0",
    
    # Vector database and embeddings
    "qdrant-client>=1.6.0",
    "sentence-transformers>=2.2.2",
    "numpy>=1.24.0",
    
    # Document processing
    "pypdf2>=3.0.0",
    "python-docx>=0.8.11",
    "beautifulsoup4>=4.12.0",
    "markdown>=3.5.0",
    
    # Data validation and settings
    "pydantic>=2.5.0",
    "pydantic-settings>=2.1.0",
    
    # Authentication and security
    "fastapi-users[sqlalchemy]>=12.1.0",
    "python-jose[cryptography]>=3.3.0",
    "passlib[bcrypt]>=1.7.4",
    "python-multipart>=0.0.6",
    
    # Utilities
    "httpx>=0.25.0",
    "aiofiles>=23.2.1",
    "python-dotenv>=1.0.0",
    "loguru>=0.7.2",
    
    # Monitoring and metrics
    "prometheus-client>=0.19.0",
    "structlog>=23.2.0",
    
    # Task queue and caching
    "celery>=5.3.0",
    "redis>=5.0.0",
    
    # File processing
    "pypdf>=3.17.0",
    "python-docx>=1.1.0",
    "python-magic>=0.4.27",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.4.0",
    "pytest-asyncio>=0.21.0",
    "pytest-cov>=4.1.0",
    "pytest-mock>=3.12.0",
    "httpx>=0.25.0",  # For testing
    "faker>=20.1.0",
    "factory-boy>=3.3.0",
]

lint = [
    "ruff>=0.1.0",
    "mypy>=1.7.0",
    "black>=23.11.0",
    "isort>=5.12.0",
]

[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[tool.ruff]
target-version = "py311"
line-length = 88
select = [
    "E",  # pycodestyle errors
    "W",  # pycodestyle warnings
    "F",  # pyflakes
    "I",  # isort
    "B",  # flake8-bugbear
    "C4", # flake8-comprehensions
    "UP", # pyupgrade
]
ignore = [
    "E501",  # line too long, handled by black
    "B008",  # do not perform function calls in argument defaults
]

[tool.ruff.per-file-ignores]
"__init__.py" = ["F401"]
"tests/**/*" = ["S101", "ARG", "FBT"]

[tool.mypy]
python_version = "3.11"
check_untyped_defs = true
disallow_any_generics = true
disallow_incomplete_defs = true
disallow_untyped_defs = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_return_any = true
strict_equality = true

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = [
    "--strict-markers",
    "--strict-config",
    "--cov=.",
    "--cov-report=term-missing",
    "--cov-report=html",
    "--cov-fail-under=90",
]
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
    "unit: marks tests as unit tests",
]

[tool.coverage.run]
source = ["."]
omit = [
    "tests/*",
    "venv/*",
    ".venv/*",
    "*/migrations/*",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]