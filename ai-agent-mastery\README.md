## AI Agent Mastery Course

This repository contains all code and resources for the [AI Agent Mastery course](https://community.dynamous.ai/s/ai-mastery-course/). This comprehensive course guides you through the full lifecycle of building AI agents—from initial planning to deployment and monetization!

### Current Available Content

Currently, the repository includes:

#### Module 3: Prototyping with No-Code (n8n)
- Complete n8n workflows for rapid AI agent prototyping
- JSON workflow files ready to import into your n8n instance
- Example configurations for different use cases
- Step-by-step setup instructions
- Local AI and "Cloud" AI implementations

#### Module 4: Building & Coding the Agent
- Pydantic AI code examples for structured AI agent development
- Complete Python implementations of agent components
- Configuration scaffolding to use your agents with any LLMs (local and not local)
- RAG (Retrieval Augmented Generation) implementation patterns
- Best practices for agent long term memory
- Examples for using your AI agents in a frontend (built with Streamlit)

#### Module 5: Agent Application (Full UI)
- Full-stack application with React frontend and FastAPI backend
- Modern UI with Shadcn components and real-time streaming responses
- Conversation history management and storage
- User session management
- Integration with the Pydantic AI agent from Module 4
- Optional n8n backend integration with the Module 3 agents

#### Module 6: Agent Deployment & Production
- Modular containerization architecture with Docker
- Production-ready deployment configurations for multiple cloud platforms
- Complete CI/CD workflows with GitHub Actions
- Multiple deployment strategies: DigitalOcean, Render, and Google Cloud Platform with Terraform
- Agent observability and monitoring with Langfuse integration

#### Mock Data for RAG

If you want a collection of documents (Markdown files) for a fake company generated by Claude like I use in the course and on YouTube, feel free to download `Mock_Data_For_RAG.zip` and bring that into your file source for your RAG pipeline.

### How to Use the Course Materials

1. **Select** the module you're currently working on (each folder starts with the module number)
2. **Review** the README in each module folder for specific setup instructions
3. **Follow along** with the corresponding course videos while exploring the code or workflows

### Getting Started

To get started with the course materials:

```bash
# Clone the repository (if you haven't already)
git clone https://github.com/dynamous-community/ai-agent-mastery.git

# Navigate to the course directory
cd ai-agent-mastery

# Explore the available modules
ls (or dir on windows)
```

### Coming Soon

Additional modules will be added to the repository as they are released (schedule is finalized!):
- Module 7: Advanced Agent Architecture (Multi-Agent, Guardrails, etc.)
- Module 8: Agent Testing and Evaluation
- Module 9: Monetizing AI Agents
- Module 10: Next Steps & Bonus Resources

## Support & Questions

If you have questions about the code/workflows or encounter any issues:
- Check the dedicated discussions in the [Dynamous community](https://community.dynamous.ai)
- Join live workshops where we'll be covering many topics to dive deeper into building specific components of AI agents
- Join our live Q&A sessions for direct support

## LICENSE

As stated in the main README for the Dynamous Community Organization, all code, resources, workflows, and templates are governed by the [proprietary Dynamous LICENSE](LICENSE).
