"""
Ollama model definitions and data structures.

This module defines the data models used for Ollama integration.
"""

from datetime import datetime
from typing import Any, Dict, List, Optional
from enum import Enum

from pydantic import BaseModel, Field


class ModelStatus(str, Enum):
    """Model status enumeration."""
    AVAILABLE = "available"
    DOWNLOADING = "downloading"
    DOWNLOADED = "downloaded"
    ERROR = "error"
    NOT_FOUND = "not_found"


class ModelType(str, Enum):
    """Model type enumeration."""
    CHAT = "chat"
    EMBEDDING = "embedding"
    VISION = "vision"
    CODE = "code"
    MULTIMODAL = "multimodal"


class OllamaModel(BaseModel):
    """Ollama model information."""
    
    name: str = Field(..., description="Model name")
    tag: str = Field(default="latest", description="Model tag/version")
    size: Optional[int] = Field(default=None, description="Model size in bytes")
    digest: Optional[str] = Field(default=None, description="Model digest/hash")
    modified_at: Optional[datetime] = Field(default=None, description="Last modified time")
    
    @property
    def full_name(self) -> str:
        """Get full model name with tag."""
        return f"{self.name}:{self.tag}"
    
    @property
    def size_gb(self) -> Optional[float]:
        """Get model size in GB."""
        if self.size:
            return round(self.size / (1024**3), 2)
        return None


class ModelInfo(BaseModel):
    """Extended model information with capabilities."""
    
    name: str = Field(..., description="Model name")
    tag: str = Field(default="latest", description="Model tag")
    model_type: ModelType = Field(..., description="Model type")
    capabilities: List[str] = Field(default_factory=list, description="Model capabilities")
    context_length: Optional[int] = Field(default=None, description="Context window size")
    parameter_count: Optional[str] = Field(default=None, description="Parameter count (e.g., '7B', '13B')")
    vision_support: bool = Field(default=False, description="Supports vision/image input")
    embedding_support: bool = Field(default=False, description="Supports embeddings")
    is_downloaded: bool = Field(default=False, description="Is model downloaded locally")
    size_gb: Optional[float] = Field(default=None, description="Model size in GB")
    description: Optional[str] = Field(default=None, description="Model description")
    provider: str = Field(default="ollama", description="Model provider")
    
    @property
    def full_name(self) -> str:
        """Get full model name with tag."""
        return f"{self.name}:{self.tag}"


class ModelDownloadProgress(BaseModel):
    """Model download progress information."""
    
    model_name: str = Field(..., description="Model being downloaded")
    status: str = Field(..., description="Download status")
    completed: int = Field(default=0, description="Bytes completed")
    total: int = Field(default=0, description="Total bytes")
    percent: float = Field(default=0.0, description="Completion percentage")
    digest: Optional[str] = Field(default=None, description="Current digest")
    
    @property
    def is_complete(self) -> bool:
        """Check if download is complete."""
        return self.percent >= 100.0 or self.status == "success"


class ChatRequest(BaseModel):
    """Ollama chat request."""
    
    model: str = Field(..., description="Model name")
    messages: List[Dict[str, str]] = Field(..., description="Chat messages")
    stream: bool = Field(default=False, description="Stream response")
    temperature: Optional[float] = Field(default=None, description="Response randomness")
    top_p: Optional[float] = Field(default=None, description="Nucleus sampling")
    top_k: Optional[int] = Field(default=None, description="Top-k sampling")
    max_tokens: Optional[int] = Field(default=None, description="Maximum tokens")
    stop: Optional[List[str]] = Field(default=None, description="Stop sequences")
    
    class Config:
        json_schema_extra = {
            "example": {
                "model": "llama3.2:latest",
                "messages": [
                    {"role": "user", "content": "Hello, how are you?"}
                ],
                "stream": False,
                "temperature": 0.7
            }
        }


class ChatResponse(BaseModel):
    """Ollama chat response."""
    
    model: str = Field(..., description="Model used")
    message: Dict[str, str] = Field(..., description="Response message")
    done: bool = Field(..., description="Response complete")
    total_duration: Optional[int] = Field(default=None, description="Total duration in nanoseconds")
    load_duration: Optional[int] = Field(default=None, description="Load duration in nanoseconds")
    prompt_eval_count: Optional[int] = Field(default=None, description="Prompt evaluation token count")
    prompt_eval_duration: Optional[int] = Field(default=None, description="Prompt evaluation duration")
    eval_count: Optional[int] = Field(default=None, description="Response token count")
    eval_duration: Optional[int] = Field(default=None, description="Response generation duration")


class EmbeddingRequest(BaseModel):
    """Ollama embedding request."""
    
    model: str = Field(..., description="Embedding model name")
    prompt: str = Field(..., description="Text to embed")
    
    class Config:
        json_schema_extra = {
            "example": {
                "model": "nomic-embed-text:latest",
                "prompt": "The quick brown fox jumps over the lazy dog"
            }
        }


class EmbeddingResponse(BaseModel):
    """Ollama embedding response."""
    
    embedding: List[float] = Field(..., description="Text embedding vector")
    model: str = Field(..., description="Model used")
    prompt: str = Field(..., description="Input text")


class GenerateRequest(BaseModel):
    """Ollama generate request."""
    
    model: str = Field(..., description="Model name")
    prompt: str = Field(..., description="Input prompt")
    stream: bool = Field(default=False, description="Stream response")
    temperature: Optional[float] = Field(default=None, description="Response randomness")
    top_p: Optional[float] = Field(default=None, description="Nucleus sampling")
    top_k: Optional[int] = Field(default=None, description="Top-k sampling")
    max_tokens: Optional[int] = Field(default=None, description="Maximum tokens")
    stop: Optional[List[str]] = Field(default=None, description="Stop sequences")


class GenerateResponse(BaseModel):
    """Ollama generate response."""
    
    model: str = Field(..., description="Model used")
    response: str = Field(..., description="Generated text")
    done: bool = Field(..., description="Generation complete")
    context: Optional[List[int]] = Field(default=None, description="Context tokens")
    total_duration: Optional[int] = Field(default=None, description="Total duration")
    load_duration: Optional[int] = Field(default=None, description="Load duration")
    prompt_eval_count: Optional[int] = Field(default=None, description="Prompt token count")
    prompt_eval_duration: Optional[int] = Field(default=None, description="Prompt evaluation duration")
    eval_count: Optional[int] = Field(default=None, description="Response token count")
    eval_duration: Optional[int] = Field(default=None, description="Response generation duration")


class ModelPullRequest(BaseModel):
    """Model pull/download request."""
    
    name: str = Field(..., description="Model name to pull")
    insecure: bool = Field(default=False, description="Allow insecure connections")
    stream: bool = Field(default=True, description="Stream download progress")


class ModelPushRequest(BaseModel):
    """Model push/upload request."""
    
    name: str = Field(..., description="Model name to push")
    insecure: bool = Field(default=False, description="Allow insecure connections")
    stream: bool = Field(default=True, description="Stream upload progress")


class ModelDeleteRequest(BaseModel):
    """Model delete request."""
    
    name: str = Field(..., description="Model name to delete")


class OllamaError(BaseModel):
    """Ollama error response."""
    
    error: str = Field(..., description="Error message")
    code: Optional[int] = Field(default=None, description="Error code")


# Model metadata registry
MODEL_REGISTRY = {
    "llama3.2:latest": ModelInfo(
        name="llama3.2",
        tag="latest",
        model_type=ModelType.CHAT,
        capabilities=["text_generation", "conversation", "reasoning"],
        context_length=128000,
        parameter_count="8B",
        vision_support=False,
        embedding_support=False,
        description="Meta's Llama 3.2 model for general conversation and reasoning"
    ),
    "llama3.2-vision:latest": ModelInfo(
        name="llama3.2-vision",
        tag="latest", 
        model_type=ModelType.VISION,
        capabilities=["text_generation", "image_analysis", "conversation"],
        context_length=128000,
        parameter_count="11B",
        vision_support=True,
        embedding_support=False,
        description="Meta's Llama 3.2 Vision model with image understanding capabilities"
    ),
    "nomic-embed-text:latest": ModelInfo(
        name="nomic-embed-text",
        tag="latest",
        model_type=ModelType.EMBEDDING,
        capabilities=["text_embedding", "semantic_search"],
        context_length=8192,
        parameter_count="137M",
        vision_support=False,
        embedding_support=True,
        description="Nomic's text embedding model for semantic search and similarity"
    ),
    "codellama:latest": ModelInfo(
        name="codellama",
        tag="latest",
        model_type=ModelType.CODE,
        capabilities=["code_generation", "code_completion", "code_explanation"],
        context_length=16384,
        parameter_count="7B",
        vision_support=False,
        embedding_support=False,
        description="Meta's Code Llama model specialized for programming tasks"
    ),
    "mistral:latest": ModelInfo(
        name="mistral",
        tag="latest",
        model_type=ModelType.CHAT,
        capabilities=["text_generation", "conversation", "reasoning"],
        context_length=32768,
        parameter_count="7B",
        vision_support=False,
        embedding_support=False,
        description="Mistral AI's efficient language model for general tasks"
    )
}
