[tool:pytest]
# Pytest configuration for LONI backend

# Test discovery
testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*

# Minimum version
minversion = 7.0

# Add options
addopts = 
    --strict-markers
    --strict-config
    --verbose
    --tb=short
    --cov=core
    --cov-report=term-missing
    --cov-report=html:htmlcov
    --cov-report=xml
    --cov-fail-under=80
    --asyncio-mode=auto

# Markers
markers =
    unit: Unit tests
    integration: Integration tests
    e2e: End-to-end tests
    slow: Slow running tests
    auth: Authentication related tests
    ai: AI service tests
    rag: RAG functionality tests
    mcp: MCP protocol tests
    ollama: Ollama integration tests
    database: Database related tests
    api: API endpoint tests
    security: Security related tests

# Test timeout
timeout = 300

# Asyncio configuration
asyncio_mode = auto

# Warnings
filterwarnings =
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
    ignore::UserWarning:pydantic.*

# Environment variables for testing
env =
    TESTING = true
    DATABASE_URL = sqlite+aiosqlite:///:memory:
    LOG_LEVEL = WARNING
