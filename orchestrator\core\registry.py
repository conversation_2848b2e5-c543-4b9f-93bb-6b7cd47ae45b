"""
Template registry for discovering and managing templates.

The registry implements the Repository pattern to provide a centralized
location for template management and discovery.
"""

import json
import yaml
from typing import Dict, List, Set, Optional, Any
from pathlib import Path
from dataclasses import dataclass
import importlib.util
from abc import ABC, abstractmethod

from .interfaces import ITemplateRegistry, ITemplate, ITemplatePlugin
from .models import TemplateMetadata, TemplateCategory, ValidationResult


class TemplateRegistry(ITemplateRegistry):
    """
    Central registry for managing templates and plugins.
    
    Provides template discovery, registration, and retrieval capabilities
    while supporting extensibility through plugins.
    """
    
    def __init__(self):
        """Initialize empty template registry."""
        self._templates: Dict[str, ITemplate] = {}
        self._plugins: Dict[str, ITemplatePlugin] = {}
        self._metadata_cache: Dict[str, TemplateMetadata] = {}
        self._categories: Dict[str, List[str]] = {}
    
    def register_template(self, template: ITemplate) -> None:
        """Register a new template in the registry."""
        metadata = template.get_metadata()
        template_id = metadata.id
        
        if template_id in self._templates:
            raise ValueError(f"Template '{template_id}' is already registered")
        
        self._templates[template_id] = template
        self._metadata_cache[template_id] = metadata
        
        # Update category index
        category = metadata.category.value
        if category not in self._categories:
            self._categories[category] = []
        self._categories[category].append(template_id)
    
    def get_template(self, template_id: str) -> Optional[ITemplate]:
        """Retrieve a template by its identifier."""
        return self._templates.get(template_id)
    
    def get_all_templates(self) -> List[ITemplate]:
        """Get all registered templates."""
        return list(self._templates.values())
    
    def get_templates_by_category(self, category: str) -> List[ITemplate]:
        """Get all templates in a specific category."""
        template_ids = self._categories.get(category, [])
        return [self._templates[tid] for tid in template_ids if tid in self._templates]
    
    def discover_templates(self, directory: Path) -> List[ITemplate]:
        """Automatically discover templates from a directory."""
        discovered = []
        
        if not directory.exists() or not directory.is_dir():
            return discovered
        
        # Look for template definition files
        for template_dir in directory.iterdir():
            if template_dir.is_dir():
                template = self._load_template_from_directory(template_dir)
                if template:
                    discovered.append(template)
                    self.register_template(template)
        
        return discovered
    
    def register_plugin(self, plugin: ITemplatePlugin) -> None:
        """Register a template plugin."""
        plugin_name = plugin.get_plugin_name()
        
        # Validate plugin dependencies
        validation_result = plugin.validate_plugin_dependencies()
        if not validation_result.is_valid:
            raise ValueError(f"Plugin '{plugin_name}' validation failed: {validation_result.issues}")
        
        self._plugins[plugin_name] = plugin
        
        # Register templates from plugin
        for template_id in plugin.get_supported_templates():
            try:
                template = plugin.create_template(template_id)
                self.register_template(template)
            except Exception as e:
                print(f"Warning: Failed to load template '{template_id}' from plugin '{plugin_name}': {e}")
    
    def get_template_metadata(self, template_id: str) -> Optional[TemplateMetadata]:
        """Get template metadata without loading the full template."""
        return self._metadata_cache.get(template_id)
    
    def search_templates(self, query: str, category: Optional[str] = None) -> List[ITemplate]:
        """Search templates by name, description, or tags."""
        results = []
        query_lower = query.lower()
        
        for template in self._templates.values():
            metadata = template.get_metadata()
            
            # Filter by category if specified
            if category and metadata.category.value != category:
                continue
            
            # Search in name, description, and tags
            if (query_lower in metadata.name.lower() or 
                query_lower in metadata.description.lower() or
                any(query_lower in tag.lower() for tag in metadata.tags)):
                results.append(template)
        
        return results
    
    def get_categories(self) -> List[str]:
        """Get all available template categories."""
        return list(self._categories.keys())
    
    def get_template_count(self) -> int:
        """Get total number of registered templates."""
        return len(self._templates)
    
    def validate_registry(self) -> ValidationResult:
        """Validate the entire template registry for consistency."""
        result = ValidationResult(is_valid=True)
        
        # Check for dependency cycles
        self._check_dependency_cycles(result)
        
        # Validate all templates
        for template_id, template in self._templates.items():
            try:
                metadata = template.get_metadata()
                self._validate_template_metadata(metadata, result)
            except Exception as e:
                result.add_error(f"Template '{template_id}' validation failed: {str(e)}")
        
        return result
    
    def _load_template_from_directory(self, template_dir: Path) -> Optional[ITemplate]:
        """Load a template from a directory structure."""
        # Look for template.yaml or template.json
        yaml_file = template_dir / "template.yaml"
        json_file = template_dir / "template.json"
        
        if yaml_file.exists():
            return self._load_template_from_yaml(yaml_file)
        elif json_file.exists():
            return self._load_template_from_json(json_file)
        
        return None
    
    def _load_template_from_yaml(self, yaml_file: Path) -> Optional[ITemplate]:
        """Load template from YAML file."""
        try:
            with open(yaml_file, 'r') as f:
                data = yaml.safe_load(f)
            return self._create_template_from_data(data, yaml_file.parent)
        except Exception as e:
            print(f"Error loading template from {yaml_file}: {e}")
            return None
    
    def _load_template_from_json(self, json_file: Path) -> Optional[ITemplate]:
        """Load template from JSON file."""
        try:
            with open(json_file, 'r') as f:
                data = json.load(f)
            return self._create_template_from_data(data, json_file.parent)
        except Exception as e:
            print(f"Error loading template from {json_file}: {e}")
            return None
    
    def _create_template_from_data(self, data: Dict[str, Any], template_dir: Path) -> ITemplate:
        """Create template instance from loaded data."""
        return FileBasedTemplate(data, template_dir)
    
    def _check_dependency_cycles(self, result: ValidationResult):
        """Check for circular dependencies in templates."""
        visited = set()
        rec_stack = set()
        
        def has_cycle(template_id: str) -> bool:
            if template_id in rec_stack:
                return True
            if template_id in visited:
                return False
            
            visited.add(template_id)
            rec_stack.add(template_id)
            
            template = self._templates.get(template_id)
            if template:
                metadata = template.get_metadata()
                for dep in metadata.dependencies:
                    if has_cycle(dep):
                        return True
            
            rec_stack.remove(template_id)
            return False
        
        for template_id in self._templates:
            if template_id not in visited:
                if has_cycle(template_id):
                    result.add_error(f"Circular dependency detected involving template '{template_id}'")
    
    def _validate_template_metadata(self, metadata: TemplateMetadata, result: ValidationResult):
        """Validate individual template metadata."""
        # Check that dependencies exist
        for dep in metadata.dependencies:
            if dep not in self._templates:
                result.add_error(f"Template '{metadata.id}' depends on unknown template '{dep}'")
        
        # Check that conflicts exist
        for conflict in metadata.conflicts:
            if conflict not in self._templates:
                result.add_warning(f"Template '{metadata.id}' conflicts with unknown template '{conflict}'")


class FileBasedTemplate(ITemplate):
    """Template implementation based on file system structure."""
    
    def __init__(self, data: Dict[str, Any], template_dir: Path):
        """Initialize template from data and directory."""
        self.template_dir = template_dir
        self._metadata = self._parse_metadata(data)
    
    def get_metadata(self) -> TemplateMetadata:
        """Return template metadata."""
        return self._metadata
    
    def validate_compatibility(self, other_templates: Set[str]) -> ValidationResult:
        """Validate compatibility with other selected templates."""
        result = ValidationResult(is_valid=True)
        
        # Check for conflicts
        for conflict in self._metadata.conflicts:
            if conflict in other_templates:
                result.add_error(f"Template '{self._metadata.id}' conflicts with '{conflict}'")
        
        return result
    
    def get_environment_variables(self) -> Dict[str, str]:
        """Return required environment variables for this template."""
        return self._metadata.environment_variables.copy()
    
    def get_docker_services(self) -> Dict[str, Dict[str, Any]]:
        """Return Docker Compose service definitions."""
        return self._metadata.docker_services.copy()
    
    def _parse_metadata(self, data: Dict[str, Any]) -> TemplateMetadata:
        """Parse template metadata from data."""
        # Set defaults and parse data
        metadata_dict = {
            'id': data.get('id', self.template_dir.name),
            'name': data.get('name', self.template_dir.name.replace('_', ' ').title()),
            'description': data.get('description', ''),
            'version': data.get('version', '1.0.0'),
            'category': data.get('category', 'infrastructure'),
            'dependencies': data.get('dependencies', []),
            'optional_dependencies': data.get('optional_dependencies', []),
            'conflicts': data.get('conflicts', []),
            'docker_services': data.get('docker_services', {}),
            'environment_variables': data.get('environment_variables', {}),
            'required_ports': data.get('required_ports', []),
            'volumes': data.get('volumes', []),
            'required_files': data.get('required_files', []),
            'generated_files': data.get('generated_files', []),
            'tags': data.get('tags', []),
            'author': data.get('author'),
            'license': data.get('license'),
            'documentation_url': data.get('documentation_url'),
            'repository_url': data.get('repository_url'),
        }
        
        # Convert category string to enum
        if isinstance(metadata_dict['category'], str):
            try:
                metadata_dict['category'] = TemplateCategory(metadata_dict['category'])
            except ValueError:
                metadata_dict['category'] = TemplateCategory.INFRASTRUCTURE
        
        return TemplateMetadata(**metadata_dict)


class BuiltinTemplatePlugin(ITemplatePlugin):
    """Plugin for built-in templates based on the existing codebase analysis."""
    
    def __init__(self):
        """Initialize built-in template plugin."""
        self._templates = self._define_builtin_templates()
    
    def get_plugin_name(self) -> str:
        """Return the plugin name."""
        return "builtin"
    
    def get_supported_templates(self) -> List[str]:
        """Return list of template IDs this plugin supports."""
        return list(self._templates.keys())
    
    def create_template(self, template_id: str) -> ITemplate:
        """Create a template instance for the given ID."""
        if template_id not in self._templates:
            raise ValueError(f"Unknown template: {template_id}")
        
        return BuiltinTemplate(template_id, self._templates[template_id])
    
    def validate_plugin_dependencies(self) -> ValidationResult:
        """Validate that plugin dependencies are satisfied."""
        return ValidationResult(is_valid=True)
    
    def _define_builtin_templates(self) -> Dict[str, Dict[str, Any]]:
        """Define all built-in templates based on codebase analysis."""
        return {
            # Backend Services
            'postgresql': {
                'name': 'PostgreSQL Database',
                'description': 'PostgreSQL relational database with pgvector extension',
                'category': 'database',
                'docker_services': {
                    'postgres': {
                        'image': 'postgres:15',
                        'environment': {
                            'POSTGRES_PASSWORD': '${POSTGRES_PASSWORD}',
                            'POSTGRES_DB': '${POSTGRES_DB:-app}'
                        },
                        'ports': ['5432:5432'],
                        'volumes': ['postgres_data:/var/lib/postgresql/data']
                    }
                },
                'environment_variables': {
                    'POSTGRES_PASSWORD': 'your_secure_password',
                    'POSTGRES_DB': 'app'
                },
                'required_ports': [5432],
                'volumes': ['postgres_data']
            },
            
            'supabase': {
                'name': 'Supabase Backend',
                'description': 'Complete Supabase backend-as-a-service stack',
                'category': 'backend',
                'dependencies': ['postgresql'],
                'docker_services': {
                    'supabase': {
                        'image': 'supabase/postgres:**********',
                        'environment': {
                            'POSTGRES_PASSWORD': '${POSTGRES_PASSWORD}',
                            'SUPABASE_URL': '${SUPABASE_URL}',
                            'SUPABASE_ANON_KEY': '${SUPABASE_ANON_KEY}'
                        },
                        'ports': ['8000:8000']
                    }
                },
                'environment_variables': {
                    'SUPABASE_URL': 'http://localhost:8000',
                    'SUPABASE_ANON_KEY': 'your_anon_key'
                },
                'required_ports': [8000]
            },
            
            # Frontend Frameworks
            'react': {
                'name': 'React 18',
                'description': 'Modern React frontend framework with TypeScript',
                'category': 'frontend',
                'dependencies': ['nodejs'],
                'environment_variables': {
                    'NODE_ENV': 'development'
                },
                'generated_files': ['src/', 'public/', 'package.json', 'tsconfig.json']
            },
            
            'nextjs': {
                'name': 'Next.js',
                'description': 'React framework with SSR and static generation',
                'category': 'frontend',
                'dependencies': ['nodejs', 'react'],
                'generated_files': ['pages/', 'components/', 'next.config.js']
            },
            
            # UI Libraries
            'shadcn': {
                'name': 'Shadcn UI',
                'description': 'Modern React component library built on Radix UI',
                'category': 'ui_library',
                'dependencies': ['react', 'tailwindcss'],
                'generated_files': ['components/ui/', 'lib/utils.ts']
            },
            
            'tailwindcss': {
                'name': 'Tailwind CSS',
                'description': 'Utility-first CSS framework',
                'category': 'ui_library',
                'generated_files': ['tailwind.config.ts', 'postcss.config.js']
            },
            
            # Package Managers (Mutually Exclusive)
            'npm': {
                'name': 'npm',
                'description': 'Node.js package manager',
                'category': 'package_manager',
                'conflicts': ['pnpm', 'bun', 'yarn'],
                'generated_files': ['package-lock.json']
            },
            
            'pnpm': {
                'name': 'pnpm',
                'description': 'Fast, disk space efficient package manager',
                'category': 'package_manager',
                'conflicts': ['npm', 'bun', 'yarn'],
                'generated_files': ['pnpm-lock.yaml']
            },
            
            'bun': {
                'name': 'Bun',
                'description': 'Fast all-in-one JavaScript runtime and package manager',
                'category': 'package_manager',
                'conflicts': ['npm', 'pnpm', 'yarn'],
                'generated_files': ['bun.lockb']
            },
            
            # Desktop Frameworks (Mutually Exclusive)
            'tauri': {
                'name': 'Tauri',
                'description': 'Rust-based desktop application framework',
                'category': 'desktop_framework',
                'conflicts': ['electron'],
                'dependencies': ['rust'],
                'generated_files': ['src-tauri/', 'tauri.conf.json']
            },
            
            'electron': {
                'name': 'Electron',
                'description': 'Cross-platform desktop apps with web technologies',
                'category': 'desktop_framework',
                'conflicts': ['tauri'],
                'dependencies': ['nodejs'],
                'generated_files': ['main.js', 'preload.js']
            },
            
            # Vector Databases
            'qdrant': {
                'name': 'Qdrant',
                'description': 'High-performance vector database for AI applications',
                'category': 'database',
                'docker_services': {
                    'qdrant': {
                        'image': 'qdrant/qdrant:latest',
                        'ports': ['6333:6333'],
                        'volumes': ['qdrant_data:/qdrant/storage']
                    }
                },
                'required_ports': [6333],
                'volumes': ['qdrant_data']
            },
            
            'neo4j': {
                'name': 'Neo4j',
                'description': 'Graph database for complex relationship modeling',
                'category': 'database',
                'docker_services': {
                    'neo4j': {
                        'image': 'neo4j:latest',
                        'environment': {
                            'NEO4J_AUTH': 'neo4j/${NEO4J_PASSWORD}'
                        },
                        'ports': ['7474:7474', '7687:7687'],
                        'volumes': ['neo4j_data:/data']
                    }
                },
                'environment_variables': {
                    'NEO4J_PASSWORD': 'your_secure_password'
                },
                'required_ports': [7474, 7687],
                'volumes': ['neo4j_data']
            },
            
            # AI/ML Services
            'ollama': {
                'name': 'Ollama',
                'description': 'Local LLM serving platform',
                'category': 'ai_ml',
                'docker_services': {
                    'ollama': {
                        'image': 'ollama/ollama:latest',
                        'ports': ['11434:11434'],
                        'volumes': ['ollama_data:/root/.ollama']
                    }
                },
                'required_ports': [11434],
                'volumes': ['ollama_data']
            },
            
            'langfuse': {
                'name': 'LangFuse',
                'description': 'LLM observability and monitoring platform',
                'category': 'monitoring',
                'dependencies': ['postgresql'],
                'docker_services': {
                    'langfuse': {
                        'image': 'langfuse/langfuse:latest',
                        'environment': {
                            'DATABASE_URL': 'postgresql://postgres:${POSTGRES_PASSWORD}@postgres:5432/langfuse'
                        },
                        'ports': ['3001:3000']
                    }
                },
                'required_ports': [3001]
            },
            
            # Workflow Tools
            'n8n': {
                'name': 'n8n',
                'description': 'Workflow automation platform',
                'category': 'infrastructure',
                'docker_services': {
                    'n8n': {
                        'image': 'n8nio/n8n:latest',
                        'ports': ['5678:5678'],
                        'volumes': ['n8n_data:/home/<USER>/.n8n'],
                        'environment': {
                            'N8N_BASIC_AUTH_ACTIVE': 'true',
                            'N8N_BASIC_AUTH_USER': '${N8N_USER}',
                            'N8N_BASIC_AUTH_PASSWORD': '${N8N_PASSWORD}'
                        }
                    }
                },
                'environment_variables': {
                    'N8N_USER': 'admin',
                    'N8N_PASSWORD': 'your_secure_password'
                },
                'required_ports': [5678],
                'volumes': ['n8n_data']
            },
            
            # Infrastructure
            'caddy': {
                'name': 'Caddy',
                'description': 'Modern web server with automatic HTTPS',
                'category': 'infrastructure',
                'docker_services': {
                    'caddy': {
                        'image': 'caddy:latest',
                        'ports': ['80:80', '443:443'],
                        'volumes': ['./Caddyfile:/etc/caddy/Caddyfile', 'caddy_data:/data']
                    }
                },
                'required_ports': [80, 443],
                'volumes': ['caddy_data'],
                'generated_files': ['Caddyfile']
            },
            
            # Runtime Requirements
            'nodejs': {
                'name': 'Node.js',
                'description': 'JavaScript runtime environment',
                'category': 'development',
                'generated_files': ['package.json']
            },
            
            'rust': {
                'name': 'Rust',
                'description': 'Systems programming language',
                'category': 'development',
                'generated_files': ['Cargo.toml']
            }
        }


class BuiltinTemplate(ITemplate):
    """Built-in template implementation."""
    
    def __init__(self, template_id: str, template_data: Dict[str, Any]):
        """Initialize built-in template."""
        self.template_id = template_id
        self.template_data = template_data
        self._metadata = self._create_metadata()
    
    def get_metadata(self) -> TemplateMetadata:
        """Return template metadata."""
        return self._metadata
    
    def validate_compatibility(self, other_templates: Set[str]) -> ValidationResult:
        """Validate compatibility with other selected templates."""
        result = ValidationResult(is_valid=True)
        
        for conflict in self._metadata.conflicts:
            if conflict in other_templates:
                result.add_error(f"Template '{self.template_id}' conflicts with '{conflict}'")
        
        return result
    
    def get_environment_variables(self) -> Dict[str, str]:
        """Return required environment variables for this template."""
        return self._metadata.environment_variables.copy()
    
    def get_docker_services(self) -> Dict[str, Dict[str, Any]]:
        """Return Docker Compose service definitions."""
        return self._metadata.docker_services.copy()
    
    def _create_metadata(self) -> TemplateMetadata:
        """Create metadata from template data."""
        data = self.template_data.copy()
        data['id'] = self.template_id
        
        # Set defaults
        data.setdefault('version', '1.0.0')
        data.setdefault('dependencies', [])
        data.setdefault('optional_dependencies', [])
        data.setdefault('conflicts', [])
        data.setdefault('docker_services', {})
        data.setdefault('environment_variables', {})
        data.setdefault('required_ports', [])
        data.setdefault('volumes', [])
        data.setdefault('required_files', [])
        data.setdefault('generated_files', [])
        data.setdefault('tags', [])
        
        # Convert category string to enum
        if 'category' in data and isinstance(data['category'], str):
            try:
                data['category'] = TemplateCategory(data['category'])
            except ValueError:
                data['category'] = TemplateCategory.INFRASTRUCTURE
        else:
            data['category'] = TemplateCategory.INFRASTRUCTURE
        
        return TemplateMetadata(**data)