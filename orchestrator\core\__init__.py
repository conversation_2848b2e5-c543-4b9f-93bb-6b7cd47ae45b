"""
Core orchestrator functionality following SOLID principles.

This module provides the foundation for the template orchestrator system:
- Abstract interfaces for extensibility
- Data models with validation
- Core business logic services
- Dependency injection framework
"""

from .interfaces import (
    ITemplate,
    ITemplateRegistry, 
    IValidator,
    IGenerator,
    IDependencyResolver
)

from .models import (
    TemplateMetadata,
    TemplateCategory,
    ValidationResult,
    DependencyResult,
    GenerationResult,
    ProjectConfig
)

from .services import (
    TemplateOrchestrator,
    ProjectGenerator,
    DependencyContainer
)

__all__ = [
    # Interfaces
    "ITemplate",
    "ITemplateRegistry",
    "IValidator", 
    "IGenerator",
    "IDependencyResolver",
    
    # Models
    "TemplateMetadata",
    "TemplateCategory",
    "ValidationResult",
    "DependencyResult", 
    "GenerationResult",
    "ProjectConfig",
    
    # Services
    "TemplateOrchestrator",
    "ProjectGenerator",
    "DependencyContainer"
]