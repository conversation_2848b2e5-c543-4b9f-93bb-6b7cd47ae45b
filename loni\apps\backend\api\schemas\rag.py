"""
RAG-related API schemas.
"""
import uuid
from typing import List, Optional, Dict, Any

from pydantic import BaseModel, Field


class DocumentCreate(BaseModel):
    """Schema for creating documents."""
    content: str = Field(..., min_length=1)
    title: str = Field(..., min_length=1, max_length=255)
    source: str = Field(..., min_length=1, max_length=500)
    metadata: Optional[Dict[str, Any]] = None


class DocumentRead(BaseModel):
    """Schema for reading documents."""
    id: str
    title: str
    source: str
    metadata: Dict[str, Any]


class DocumentDetail(DocumentRead):
    """Schema for detailed document view."""
    content: str


class SearchRequest(BaseModel):
    """Schema for search requests."""
    query: str = Field(..., min_length=1, max_length=1000)
    limit: Optional[int] = Field(5, ge=1, le=20)
    score_threshold: Optional[float] = Field(0.7, ge=0.0, le=1.0)
    filters: Optional[Dict[str, Any]] = None


class SearchResult(BaseModel):
    """Schema for search results."""
    id: str
    score: float
    title: str
    content: str
    source: str
    metadata: Dict[str, Any]


class SearchResponse(BaseModel):
    """Schema for search response."""
    query: str
    results: List[SearchResult]
    total_results: int
    execution_time_ms: float


class ConversationSearchRequest(BaseModel):
    """Schema for conversation search requests."""
    query: str = Field(..., min_length=1, max_length=1000)
    limit: Optional[int] = Field(10, ge=1, le=50)
    score_threshold: Optional[float] = Field(0.6, ge=0.0, le=1.0)


class ConversationSearchResult(BaseModel):
    """Schema for conversation search results."""
    conversation_id: uuid.UUID
    score: float
    message_count: int
    preview: str


class RAGStats(BaseModel):
    """Schema for RAG system statistics."""
    documents: Dict[str, Any]
    conversations: Dict[str, Any]


class FileUploadResponse(BaseModel):
    """Schema for file upload response."""
    document_id: str
    filename: str
    title: str
    status: str
    message: str