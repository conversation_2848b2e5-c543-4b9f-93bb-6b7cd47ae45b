"""
Monitoring and metrics configuration settings.

This module contains all monitoring-related configuration
following the Single Responsibility Principle.
"""

from pydantic_settings import BaseSettings


class MonitoringSettings(BaseSettings):
    """Monitoring and metrics settings."""
    
    enabled: bool = True
    prometheus_port: int = 8001
    
    # Health check settings
    health_check_interval: int = 30
    health_check_timeout: int = 5
    
    # Metrics collection
    collect_detailed_metrics: bool = True
    metric_retention_days: int = 30
    
    class Config:
        env_prefix = "MONITORING_"