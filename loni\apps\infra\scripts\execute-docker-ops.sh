#!/bin/bash
# Execute Docker Compose operations with error logging
# Simple script to restart Docker Compose stack and capture errors

set -euo pipefail

# Configuration
INFRA_DIR="E:/Projects/lonors/loni/apps/infra"
LOG_DIR="../../data/logs/docker-compose"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1" >&2
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

warn() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# Change to infra directory
cd "$INFRA_DIR" || exit 1
log "Changed to directory: $INFRA_DIR"

# Create log directory
mkdir -p "$LOG_DIR"
log "Created log directory: $LOG_DIR"

# Log files
LOG_FILE="$LOG_DIR/docker-compose-$TIMESTAMP.log"
ERROR_FILE="$LOG_DIR/docker-compose-errors-$TIMESTAMP.jsonl"

log_structured_error() {
    local service="$1"
    local error_type="$2"
    local message="$3"
    local severity="${4:-ERROR}"
    
    local timestamp=$(date -u +"%Y-%m-%dT%H:%M:%S.%3NZ")
    local error_entry=$(cat <<EOF
{"timestamp":"$timestamp","service":"$service","error_type":"$error_type","message":"$message","severity":"$severity"}
EOF
)
    
    echo "$error_entry" >> "$ERROR_FILE"
    error "$service: $message"
}

# Function to execute docker-compose commands with logging
execute_docker_command() {
    local command="$1"
    local description="$2"
    
    log "Executing: $description"
    log "Command: docker-compose $command"
    
    # Execute command and capture output
    if docker-compose $command 2>&1 | tee -a "$LOG_FILE"; then
        success "$description completed successfully"
        return 0
    else
        local exit_code=$?
        error "$description failed with exit code: $exit_code"
        log_structured_error "docker-compose" "command_failure" "$description failed" "CRITICAL"
        return $exit_code
    fi
}

# Main execution
log "Starting Docker Compose operations with error logging"
log "Log file: $LOG_FILE"
log "Error file: $ERROR_FILE"

echo "=== Docker Compose Operations Log ===" > "$LOG_FILE"
echo "Timestamp: $(date)" >> "$LOG_FILE"
echo "Directory: $INFRA_DIR" >> "$LOG_FILE"
echo "" >> "$LOG_FILE"

# Step 1: Stop all services
log "=== STEP 1: Stopping all services ==="
execute_docker_command "down --remove-orphans" "Stop all services and remove orphans"

# Step 2: Build and start services
log "=== STEP 2: Building and starting services ==="
execute_docker_command "up -d --build" "Build and start all services"

# Step 3: Wait for services to initialize
log "=== STEP 3: Waiting for services to initialize ==="
sleep 30

# Step 4: Check service health
log "=== STEP 4: Checking service health ==="
if docker-compose ps 2>&1 | tee -a "$LOG_FILE"; then
    success "Service status check completed"
else
    error "Failed to check service status"
    log_structured_error "docker-compose" "status_check_failure" "Failed to check service status" "ERROR"
fi

# Step 5: Check for unhealthy services
log "=== STEP 5: Analyzing service health ==="
unhealthy_services=$(docker-compose ps --filter "status=exited" --format "table {{.Service}}\t{{.Status}}" 2>/dev/null || echo "")

if [[ -n "$unhealthy_services" && "$unhealthy_services" != *"SERVICE"* ]]; then
    warn "Found unhealthy services:"
    echo "$unhealthy_services"
    
    # Log each unhealthy service
    while IFS=$'\t' read -r service status; do
        if [[ "$service" != "SERVICE" && -n "$service" ]]; then
            log_structured_error "$service" "unhealthy" "Service status: $status" "ERROR"
        fi
    done <<< "$unhealthy_services"
else
    success "All services appear to be healthy"
fi

success "Docker Compose operations completed"
log "Full logs available at: $LOG_FILE"
log "Structured errors available at: $ERROR_FILE"

# Show final summary
log "=== FINAL SUMMARY ==="
running_count=$(docker-compose ps --filter "status=running" --format "{{.Service}}" 2>/dev/null | wc -l || echo "0")
total_count=$(docker-compose ps --format "{{.Service}}" 2>/dev/null | wc -l || echo "0")

log "Services running: $running_count/$total_count"

if [[ "$running_count" -gt 0 ]]; then
    success "Docker Compose stack is operational"
else
    error "No services are running - check logs for errors"
fi
