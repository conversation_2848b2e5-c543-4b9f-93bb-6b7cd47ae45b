#!/bin/bash

# LONI Platform Services Startup Script
# Handles Docker environment validation and incremental service startup

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${BLUE}=== $1 ===${NC}"
}

# Check Docker availability
check_docker() {
    print_header "Checking Docker Environment"
    
    if ! command -v docker &> /dev/null; then
        print_error "Docker is not available in this environment"
        print_warning "Please enable Docker Desktop WSL2 integration:"
        echo "  1. Open Docker Desktop"
        echo "  2. Go to Settings > Resources > WSL Integration"
        echo "  3. Enable integration with your WSL2 distro"
        echo "  4. Restart your WSL2 terminal"
        exit 1
    fi

    if ! docker info &> /dev/null; then
        print_error "Docker daemon is not running"
        print_warning "Please start Docker Desktop"
        exit 1
    fi

    print_status "Docker is available and running"
}

# Validate environment file
check_environment() {
    print_header "Validating Environment Configuration"
    
    if [ ! -f .env ]; then
        print_error ".env file not found"
        print_warning "Please run the setup script first: bash scripts/setup.sh"
        exit 1
    fi
    
    print_status "Environment configuration found"
}

# Start core services first
start_core_services() {
    print_header "Starting Core Services"
    
    print_status "Starting databases..."
    docker-compose up -d postgres redis qdrant
    
    # Wait for databases to be healthy
    print_status "Waiting for databases to be ready..."
    for i in {1..30}; do
        if docker-compose ps postgres | grep -q "healthy"; then
            print_status "PostgreSQL is ready"
            break
        fi
        sleep 2
    done
    
    for i in {1..30}; do
        if docker-compose ps redis | grep -q "healthy"; then
            print_status "Redis is ready"
            break
        fi
        sleep 2
    done
    
    for i in {1..30}; do
        if docker-compose ps qdrant | grep -q "healthy"; then
            print_status "Qdrant is ready"
            break
        fi
        sleep 2
    done
}

# Start monitoring services
start_monitoring() {
    print_header "Starting Monitoring Services"
    
    print_status "Starting exporters..."
    docker-compose up -d node-exporter cadvisor postgres-exporter redis-exporter
    
    sleep 10
    
    print_status "Starting Prometheus..."
    docker-compose up -d prometheus
    
    # Wait for Prometheus to be ready
    for i in {1..20}; do
        if docker-compose ps prometheus | grep -q "healthy"; then
            print_status "Prometheus is ready"
            break
        fi
        sleep 3
    done
    
    print_status "Starting Grafana..."
    docker-compose up -d grafana
}

# Start application services
start_application_services() {
    print_header "Starting Application Services"
    
    # Try to start Ollama (GPU version first, fallback to CPU)
    print_status "Starting Ollama AI service..."
    if docker-compose --profile gpu up -d ollama 2>/dev/null; then
        print_status "Ollama started with GPU support"
    else
        print_warning "GPU not available, starting Ollama in CPU mode"
        docker-compose --profile cpu up -d ollama-cpu
    fi
    
    print_status "Starting n8n workflow automation..."
    docker-compose up -d n8n
    
    print_status "Starting Jaeger tracing..."
    docker-compose up -d jaeger
    
    # Optional: Start Neo4j if needed
    read -p "Start Neo4j graph database? (y/n): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        print_status "Starting Neo4j..."
        docker-compose up -d neo4j
    fi
}

# Start reverse proxy last
start_reverse_proxy() {
    print_header "Starting Reverse Proxy"
    
    # Wait a bit for services to be ready
    sleep 10
    
    print_status "Starting Nginx reverse proxy..."
    docker-compose up -d nginx
}

# Health check all services
check_services_health() {
    print_header "Service Health Check"
    
    services=("postgres" "redis" "qdrant" "prometheus" "grafana" "nginx")
    
    for service in "${services[@]}"; do
        if docker-compose ps "$service" | grep -q "healthy"; then
            print_status "$service is healthy"
        else
            print_warning "$service is not healthy"
        fi
    done
}

# Print access information
print_access_info() {
    print_header "LONI Platform Access Information"
    
    echo ""
    print_status "Service URLs:"
    echo "  • Monitoring (Grafana): http://localhost:3001"
    echo "  • Metrics (Prometheus): http://localhost:9090"
    echo "  • Tracing (Jaeger): http://localhost:16686"
    echo "  • Automation (n8n): http://localhost:5678"
    echo "  • Vector DB (Qdrant): http://localhost:6333"
    echo ""
    
    print_status "Reverse Proxy URLs (when nginx is running):"
    echo "  • Main Application: http://localhost"
    echo "  • Monitoring: http://localhost/monitoring (admin/loni_monitoring_admin)"
    echo "  • Metrics: http://localhost/prometheus (admin/loni_monitoring_admin)"
    echo "  • Automation: http://localhost/automation (admin/loni_monitoring_admin)"
    echo "  • Tracing: http://localhost/tracing (admin/loni_monitoring_admin)"
    echo ""
    
    print_status "Database Access:"
    echo "  • PostgreSQL: localhost:5432 (user: loni)"
    echo "  • Redis: localhost:6379 (password in .env)"
    echo "  • Qdrant: localhost:6333"
    echo ""
    
    print_warning "Next Steps:"
    echo "  1. Start your backend API: cd ../backend && uvicorn main:app --reload --port 8000"
    echo "  2. Start your frontend: cd ../frontend && bun dev"
    echo "  3. Access the platform at http://localhost:3000"
    echo ""
}

# Main execution
main() {
    print_header "LONI Platform Services Startup"
    
    # Change to script directory
    cd "$(dirname "$0")/.."
    
    # Run startup sequence
    check_docker
    check_environment
    start_core_services
    start_monitoring
    start_application_services
    start_reverse_proxy
    
    sleep 5
    check_services_health
    print_access_info
    
    print_status "🎉 LONI Platform infrastructure startup complete!"
}

# Handle script arguments
case "${1:-start}" in
    "start")
        main
        ;;
    "stop")
        print_status "Stopping all LONI services..."
        docker-compose down
        ;;
    "restart")
        print_status "Restarting LONI services..."
        docker-compose down
        sleep 5
        main
        ;;
    "status")
        docker-compose ps
        ;;
    "logs")
        docker-compose logs -f "${2:-}"
        ;;
    *)
        echo "Usage: $0 {start|stop|restart|status|logs [service]}"
        exit 1
        ;;
esac