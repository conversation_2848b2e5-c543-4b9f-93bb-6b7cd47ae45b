"""
Command-line interface for the template orchestrator.

Provides a comprehensive CLI with interactive template selection,
validation, and project generation capabilities.
"""

import click
import sys
from typing import Set, List, Optional, Dict, Any
from pathlib import Path
from rich.console import Console
from rich.table import Table
from rich.panel import Panel
from rich.progress import Progress, SpinnerColumn, TextColumn
from rich.prompt import Confirm, Prompt
from rich.tree import Tree
from rich.text import Text

from ..core.services import TemplateOrchestrator, DependencyContainer
from ..core.registry import TemplateRegistry, BuiltinTemplatePlugin
from ..core.dependency_resolver import OptimizedDependencyResolver
from ..core.validators import DependencyValidator, ConfigurationValidator
from ..generators.docker_compose import DockerComposeGenerator
from ..generators.environment import EnvironmentGenerator
from ..generators.project_structure import ProjectStructureGenerator
from ..core.models import ProjectConfig, TemplateCategory
from .interactive import InteractiveTemplateSelector


class OrchestratorCLI:
    """
    Main CLI interface for the template orchestrator.
    
    Provides commands for template management, project generation,
    and interactive template selection.
    """
    
    def __init__(self):
        """Initialize CLI with console and orchestrator."""
        self.console = Console()
        self.orchestrator = self._initialize_orchestrator()
    
    def _initialize_orchestrator(self) -> TemplateOrchestrator:
        """Initialize the orchestrator with all dependencies."""
        # Create template registry
        registry = TemplateRegistry()
        
        # Register built-in templates
        builtin_plugin = BuiltinTemplatePlugin()
        registry.register_plugin(builtin_plugin)
        
        # Create dependency resolver
        dependency_resolver = OptimizedDependencyResolver(registry)
        
        # Create validators
        dependency_validator = DependencyValidator(registry)
        config_validator = ConfigurationValidator(registry)
        
        # Create generators
        docker_generator = DockerComposeGenerator(registry)
        env_generator = EnvironmentGenerator(registry)
        structure_generator = ProjectStructureGenerator(registry)
        
        # Create dependency container
        container = DependencyContainer(
            template_registry=registry,
            dependency_resolver=dependency_resolver
        )
        
        # Register validators and generators
        container.register_validator("dependency", dependency_validator)
        container.register_validator("configuration", config_validator)
        container.register_generator("docker_compose", docker_generator)
        container.register_generator("environment", env_generator)
        container.register_generator("project_structure", structure_generator)
        
        return TemplateOrchestrator(container)
    
    def run(self):
        """Run the CLI application."""
        self._show_welcome()
        
        @click.group()
        @click.version_option(version="1.0.0", prog_name="orchestrator")
        def cli():
            """Project Template Orchestrator - Generate projects with intelligent dependency management."""
            pass
        
        @cli.command()
        def list_templates():
            """List all available templates."""
            self._list_templates()
        
        @cli.command()
        @click.option('--category', '-c', help='Filter by template category')
        @click.option('--search', '-s', help='Search templates by name or description')
        def search(category: Optional[str], search: Optional[str]):
            """Search for templates."""
            self._search_templates(category, search)
        
        @cli.command()
        @click.argument('project_name')
        @click.option('--templates', '-t', multiple=True, help='Template IDs to include')
        @click.option('--interactive', '-i', is_flag=True, help='Interactive template selection')
        @click.option('--output-dir', '-o', default='.', help='Output directory')
        def create(project_name: str, templates: tuple, interactive: bool, output_dir: str):
            """Create a new project."""
            self._create_project(project_name, templates, interactive, output_dir)
        
        @cli.command()
        @click.argument('templates', nargs=-1)
        def validate(templates: tuple):
            """Validate template selection."""
            self._validate_templates(templates)
        
        @cli.command()
        @click.argument('templates', nargs=-1)
        def preview(templates: tuple):
            """Preview what would be generated."""
            self._preview_generation(templates)
        
        @cli.command()
        @click.argument('template_id')
        def info(template_id: str):
            """Show detailed information about a template."""
            self._show_template_info(template_id)
        
        @cli.command()
        def interactive():
            """Launch interactive template selection."""
            self._launch_interactive()
        
        # Run the CLI
        cli()
    
    def _show_welcome(self):
        """Show welcome message."""
        welcome_text = Text.assemble(
            ("🚀 ", "bold blue"),
            ("Project Template Orchestrator", "bold green"),
            (" - Intelligent Project Generation", "bold")
        )
        
        panel = Panel(
            welcome_text,
            subtitle="Use 'orchestrator --help' for available commands",
            border_style="blue"
        )
        
        self.console.print(panel)
        self.console.print()
    
    def _list_templates(self):
        """List all available templates."""
        templates = self.orchestrator.get_available_templates()
        
        if not templates:
            self.console.print("[red]No templates available[/red]")
            return
        
        # Group templates by category
        categories = {}
        for template in templates:
            category = template.category.value
            if category not in categories:
                categories[category] = []
            categories[category].append(template)
        
        # Create table
        table = Table(title="Available Templates")
        table.add_column("ID", style="cyan", no_wrap=True)
        table.add_column("Name", style="green")
        table.add_column("Category", style="blue")
        table.add_column("Description")
        table.add_column("Dependencies", style="yellow")
        
        for category, templates_in_category in sorted(categories.items()):
            for template in sorted(templates_in_category, key=lambda t: t.id):
                deps = ", ".join(template.dependencies) if template.dependencies else "-"
                table.add_row(
                    template.id,
                    template.name,
                    category,
                    template.description[:50] + "..." if len(template.description) > 50 else template.description,
                    deps
                )
        
        self.console.print(table)
        self.console.print(f"\n[green]Total: {len(templates)} templates[/green]")
    
    def _search_templates(self, category: Optional[str], search_term: Optional[str]):
        """Search for templates."""
        templates = self.orchestrator.get_available_templates()
        
        # Filter by category
        if category:
            templates = [t for t in templates if t.category.value == category]
        
        # Filter by search term
        if search_term:
            search_lower = search_term.lower()
            templates = [
                t for t in templates 
                if (search_lower in t.name.lower() or 
                    search_lower in t.description.lower() or
                    any(search_lower in tag.lower() for tag in t.tags))
            ]
        
        if not templates:
            self.console.print("[red]No templates found matching criteria[/red]")
            return
        
        # Display results
        table = Table(title=f"Search Results ({len(templates)} found)")
        table.add_column("ID", style="cyan")
        table.add_column("Name", style="green")
        table.add_column("Description")
        
        for template in templates:
            table.add_row(template.id, template.name, template.description)
        
        self.console.print(table)
    
    def _create_project(self, project_name: str, templates: tuple, interactive: bool, output_dir: str):
        """Create a new project."""
        if interactive or not templates:
            # Launch interactive selection
            selector = InteractiveTemplateSelector(self.orchestrator, self.console)
            selected_templates = selector.select_templates()
            if not selected_templates:
                self.console.print("[yellow]No templates selected. Exiting.[/yellow]")
                return
        else:
            selected_templates = set(templates)
        
        # Validate selection
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            console=self.console
        ) as progress:
            task = progress.add_task("Validating template selection...", total=None)
            
            validation_result = self.orchestrator.select_templates(selected_templates)
            
            if not validation_result.is_valid:
                progress.stop()
                self._display_validation_errors(validation_result)
                return
            
            progress.update(task, description="Validation successful!")
        
        # Show preview
        preview = self.orchestrator.preview_generation(selected_templates)
        self._display_preview(preview)
        
        # Confirm generation
        if not Confirm.ask("\nProceed with project generation?", default=True):
            self.console.print("[yellow]Project generation cancelled.[/yellow]")
            return
        
        # Generate project
        self._generate_project_with_progress(project_name, selected_templates, output_dir)
    
    def _validate_templates(self, templates: tuple):
        """Validate template selection."""
        if not templates:
            self.console.print("[red]Please specify templates to validate[/red]")
            return
        
        selected_templates = set(templates)
        validation_result = self.orchestrator.select_templates(selected_templates)
        
        if validation_result.is_valid:
            self.console.print("[green]✓ Template selection is valid![/green]")
            
            # Show resolved dependencies
            current_selection = self.orchestrator.get_current_selection()
            if len(current_selection) > len(selected_templates):
                added_deps = current_selection - selected_templates
                self.console.print(f"[blue]Added dependencies: {', '.join(added_deps)}[/blue]")
        else:
            self._display_validation_errors(validation_result)
    
    def _preview_generation(self, templates: tuple):
        """Preview what would be generated."""
        if not templates:
            self.console.print("[red]Please specify templates to preview[/red]")
            return
        
        selected_templates = set(templates)
        preview = self.orchestrator.preview_generation(selected_templates)
        self._display_preview(preview)
    
    def _show_template_info(self, template_id: str):
        """Show detailed information about a template."""
        template = self.orchestrator.container.template_registry.get_template(template_id)
        if not template:
            self.console.print(f"[red]Template '{template_id}' not found[/red]")
            return
        
        metadata = template.get_metadata()
        
        # Create info panel
        info_lines = [
            f"[bold]Name:[/bold] {metadata.name}",
            f"[bold]Description:[/bold] {metadata.description}",
            f"[bold]Category:[/bold] {metadata.category.value}",
            f"[bold]Version:[/bold] {metadata.version}",
        ]
        
        if metadata.dependencies:
            info_lines.append(f"[bold]Dependencies:[/bold] {', '.join(metadata.dependencies)}")
        
        if metadata.conflicts:
            info_lines.append(f"[bold]Conflicts:[/bold] {', '.join(metadata.conflicts)}")
        
        if metadata.required_ports:
            info_lines.append(f"[bold]Required Ports:[/bold] {', '.join(map(str, metadata.required_ports))}")
        
        if metadata.docker_services:
            info_lines.append(f"[bold]Docker Services:[/bold] {', '.join(metadata.docker_services.keys())}")
        
        if metadata.tags:
            info_lines.append(f"[bold]Tags:[/bold] {', '.join(metadata.tags)}")
        
        panel = Panel(
            "\n".join(info_lines),
            title=f"Template: {template_id}",
            border_style="blue"
        )
        
        self.console.print(panel)
    
    def _launch_interactive(self):
        """Launch interactive template selection mode."""
        selector = InteractiveTemplateSelector(self.orchestrator, self.console)
        selected_templates = selector.select_templates()
        
        if not selected_templates:
            self.console.print("[yellow]No templates selected.[/yellow]")
            return
        
        # Get project name
        project_name = Prompt.ask("Enter project name")
        
        # Generate project
        self._generate_project_with_progress(project_name, selected_templates, ".")
    
    def _display_validation_errors(self, validation_result):
        """Display validation errors and warnings."""
        self.console.print("[red]✗ Validation failed:[/red]")
        
        for issue in validation_result.issues:
            if issue.severity.value == "error":
                self.console.print(f"  [red]Error:[/red] {issue.message}")
                if issue.suggestion:
                    self.console.print(f"    [dim]Suggestion: {issue.suggestion}[/dim]")
            elif issue.severity.value == "warning":
                self.console.print(f"  [yellow]Warning:[/yellow] {issue.message}")
                if issue.suggestion:
                    self.console.print(f"    [dim]Suggestion: {issue.suggestion}[/dim]")
    
    def _display_preview(self, preview: Dict[str, Any]):
        """Display generation preview."""
        if not preview.get("valid", False):
            self.console.print("[red]Invalid template selection[/red]")
            return
        
        # Create preview panel
        tree = Tree("📦 Project Preview")
        
        # Templates section
        templates_node = tree.add("🧩 Selected Templates")
        for template_id in preview.get("resolved_templates", []):
            templates_node.add(f"[green]{template_id}[/green]")
        
        # Docker services section
        if preview.get("docker_services"):
            services_node = tree.add("🐳 Docker Services")
            for service_name in preview["docker_services"].keys():
                services_node.add(f"[blue]{service_name}[/blue]")
        
        # Environment variables section
        if preview.get("environment_variables"):
            env_node = tree.add("🔧 Environment Variables")
            env_count = len(preview["environment_variables"])
            env_node.add(f"[yellow]{env_count} variables required[/yellow]")
        
        # Required ports section
        if preview.get("required_ports"):
            ports_node = tree.add("🌐 Required Ports")
            for port in sorted(preview["required_ports"]):
                ports_node.add(f"[cyan]{port}[/cyan]")
        
        self.console.print(tree)
    
    def _generate_project_with_progress(self, project_name: str, selected_templates: Set[str], output_dir: str):
        """Generate project with progress indication."""
        output_path = Path(output_dir) / project_name
        
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            console=self.console
        ) as progress:
            # Main generation task
            task = progress.add_task("Generating project...", total=None)
            
            # Generate project
            result = self.orchestrator.generate_project(project_name, selected_templates)
            
            if result.success:
                progress.update(task, description="✓ Project generated successfully!")
                progress.stop()
                
                # Display success message
                success_panel = Panel(
                    f"[green]✓ Project '{project_name}' created successfully![/green]\n\n"
                    f"[bold]Location:[/bold] {output_path}\n"
                    f"[bold]Generated files:[/bold] {len(result.generated_files)}\n\n"
                    f"[bold]Next steps:[/bold]\n" + 
                    "\n".join(f"  • {step}" for step in result.next_steps),
                    title="🎉 Success!",
                    border_style="green"
                )
                
                self.console.print(success_panel)
            else:
                progress.update(task, description="✗ Project generation failed!")
                progress.stop()
                
                # Display errors
                self.console.print("[red]✗ Project generation failed:[/red]")
                for error in result.errors:
                    self.console.print(f"  [red]Error:[/red] {error}")
                
                if result.warnings:
                    for warning in result.warnings:
                        self.console.print(f"  [yellow]Warning:[/yellow] {warning}")


def main():
    """Entry point for the CLI application."""
    try:
        cli = OrchestratorCLI()
        cli.run()
    except KeyboardInterrupt:
        print("\n👋 Goodbye!")
        sys.exit(0)
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()