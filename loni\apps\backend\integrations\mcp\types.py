"""
MCP protocol types and data structures.

This module defines the core types used in MCP communication
following the MCP specification.
"""

from typing import Any, Dict, List, Optional, Union
from uuid import UUID
from datetime import datetime

from pydantic import BaseModel, Field


class MCPRequest(BaseModel):
    """MCP request message."""
    
    id: str = Field(..., description="Unique request identifier")
    method: str = Field(..., description="Method name to call")
    params: Optional[Dict[str, Any]] = Field(default=None, description="Method parameters")
    jsonrpc: str = Field(default="2.0", description="JSON-RPC version")


class MCPResponse(BaseModel):
    """MCP response message."""
    
    id: str = Field(..., description="Request identifier")
    result: Optional[Any] = Field(default=None, description="Method result")
    error: Optional[Dict[str, Any]] = Field(default=None, description="Error information")
    jsonrpc: str = Field(default="2.0", description="JSON-RPC version")


class MCPTool(BaseModel):
    """MCP tool definition."""
    
    name: str = Field(..., description="Tool name")
    description: str = Field(..., description="Tool description")
    parameters: Dict[str, Any] = Field(default_factory=dict, description="Tool parameters schema")
    
    class Config:
        json_schema_extra = {
            "example": {
                "name": "search_documents",
                "description": "Search documents using semantic similarity",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "query": {"type": "string", "description": "Search query"},
                        "limit": {"type": "integer", "description": "Maximum results", "default": 5}
                    },
                    "required": ["query"]
                }
            }
        }


class MCPResource(BaseModel):
    """MCP resource definition."""
    
    uri: str = Field(..., description="Resource URI")
    name: str = Field(..., description="Resource name")
    description: Optional[str] = Field(default=None, description="Resource description")
    mime_type: Optional[str] = Field(default=None, description="MIME type")
    
    class Config:
        json_schema_extra = {
            "example": {
                "uri": "loni://user/profile/123",
                "name": "User Profile",
                "description": "User profile information",
                "mime_type": "application/json"
            }
        }


class MCPCapabilities(BaseModel):
    """MCP server capabilities."""
    
    tools: List[MCPTool] = Field(default_factory=list, description="Available tools")
    resources: List[MCPResource] = Field(default_factory=list, description="Available resources")
    prompts: List[str] = Field(default_factory=list, description="Available prompts")
    
    
class MCPServerInfo(BaseModel):
    """MCP server information."""
    
    name: str = Field(..., description="Server name")
    version: str = Field(..., description="Server version")
    capabilities: MCPCapabilities = Field(default_factory=MCPCapabilities, description="Server capabilities")


class MCPError(BaseModel):
    """MCP error information."""
    
    code: int = Field(..., description="Error code")
    message: str = Field(..., description="Error message")
    data: Optional[Any] = Field(default=None, description="Additional error data")


class MCPToolCall(BaseModel):
    """MCP tool call request."""
    
    tool_name: str = Field(..., description="Name of tool to call")
    arguments: Dict[str, Any] = Field(default_factory=dict, description="Tool arguments")
    call_id: Optional[str] = Field(default=None, description="Call identifier")


class MCPToolResult(BaseModel):
    """MCP tool call result."""
    
    call_id: Optional[str] = Field(default=None, description="Call identifier")
    result: Any = Field(..., description="Tool execution result")
    error: Optional[MCPError] = Field(default=None, description="Execution error")
    metadata: Optional[Dict[str, Any]] = Field(default=None, description="Result metadata")


class MCPResourceContent(BaseModel):
    """MCP resource content."""
    
    uri: str = Field(..., description="Resource URI")
    content: Union[str, bytes, Dict[str, Any]] = Field(..., description="Resource content")
    mime_type: str = Field(..., description="Content MIME type")
    encoding: Optional[str] = Field(default=None, description="Content encoding")
    metadata: Optional[Dict[str, Any]] = Field(default=None, description="Content metadata")


class MCPPrompt(BaseModel):
    """MCP prompt definition."""
    
    name: str = Field(..., description="Prompt name")
    description: Optional[str] = Field(default=None, description="Prompt description")
    template: str = Field(..., description="Prompt template")
    parameters: Optional[Dict[str, Any]] = Field(default=None, description="Template parameters")


class MCPSession(BaseModel):
    """MCP session information."""
    
    session_id: str = Field(..., description="Session identifier")
    client_info: Dict[str, Any] = Field(default_factory=dict, description="Client information")
    server_info: MCPServerInfo = Field(..., description="Server information")
    created_at: datetime = Field(default_factory=datetime.utcnow, description="Session creation time")
    last_activity: datetime = Field(default_factory=datetime.utcnow, description="Last activity time")
    is_active: bool = Field(default=True, description="Session active status")


# MCP Protocol Constants
class MCPMethods:
    """Standard MCP method names."""
    
    # Core methods
    INITIALIZE = "initialize"
    PING = "ping"
    
    # Tool methods
    LIST_TOOLS = "tools/list"
    CALL_TOOL = "tools/call"
    
    # Resource methods
    LIST_RESOURCES = "resources/list"
    READ_RESOURCE = "resources/read"
    
    # Prompt methods
    LIST_PROMPTS = "prompts/list"
    GET_PROMPT = "prompts/get"
    
    # Notification methods
    NOTIFICATION = "notification"
    LOG = "log"


class MCPErrorCodes:
    """Standard MCP error codes."""
    
    # JSON-RPC errors
    PARSE_ERROR = -32700
    INVALID_REQUEST = -32600
    METHOD_NOT_FOUND = -32601
    INVALID_PARAMS = -32602
    INTERNAL_ERROR = -32603
    
    # MCP-specific errors
    TOOL_NOT_FOUND = -32000
    TOOL_EXECUTION_ERROR = -32001
    RESOURCE_NOT_FOUND = -32002
    RESOURCE_ACCESS_ERROR = -32003
    PROMPT_NOT_FOUND = -32004
    SESSION_ERROR = -32005
