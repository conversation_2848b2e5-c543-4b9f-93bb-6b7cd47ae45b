/**
 * Authentication service and hooks
 */
import { apiClient } from './api'
import type { User, LoginRequest, RegisterRequest, AuthResponse } from '@/types'

export class AuthService {
  async login(credentials: LoginRequest): Promise<AuthResponse> {
    const formData = new FormData()
    formData.append('username', credentials.username)
    formData.append('password', credentials.password)

    const response = await fetch(`${apiClient['baseUrl']}/auth/jwt/login`, {
      method: 'POST',
      body: formData,
    })

    if (!response.ok) {
      const error = await response.json()
      throw new Error(error.detail || 'Login failed')
    }

    const authData: AuthResponse = await response.json()
    apiClient.setToken(authData.access_token)
    return authData
  }

  async register(userData: RegisterRequest): Promise<User> {
    const user = await apiClient.post<User>('/auth/register', userData)
    return user
  }

  async logout(): Promise<void> {
    try {
      await apiClient.post('/auth/jwt/logout')
    } catch (error) {
      // Ignore logout errors, we're removing token anyway
    } finally {
      apiClient.removeToken()
    }
  }

  async getCurrentUser(): Promise<User> {
    return apiClient.get<User>('/users/me')
  }

  async updateProfile(data: Partial<User>): Promise<User> {
    return apiClient.patch<User>('/users/me', data)
  }

  async requestPasswordReset(email: string): Promise<void> {
    await apiClient.post('/auth/forgot-password', { email })
  }

  async resetPassword(token: string, password: string): Promise<void> {
    await apiClient.post('/auth/reset-password', { token, password })
  }

  async requestEmailVerification(): Promise<void> {
    await apiClient.post('/auth/request-verify-token')
  }

  async verifyEmail(token: string): Promise<void> {
    await apiClient.post('/auth/verify', { token })
  }

  isAuthenticated(): boolean {
    if (typeof window === 'undefined') return false
    return !!localStorage.getItem('auth_token')
  }
}

export const authService = new AuthService()