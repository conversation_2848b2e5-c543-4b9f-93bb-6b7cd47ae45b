"""
Core business logic services following SOLID principles.

These services implement the main orchestrator functionality while
maintaining separation of concerns and dependency injection.
"""

import time
from typing import Dict, List, Set, Optional, Any
from pathlib import Path
from dataclasses import dataclass, field

from .interfaces import (
    ITemplateRegistry, 
    IDependencyResolver,
    IValidator,
    IGenerator,
    IOrchestrator
)
from .models import (
    TemplateMetadata,
    ValidationResult,
    DependencyResult, 
    GenerationResult,
    ProjectConfig,
    GenerationContext,
    ValidationSeverity
)


@dataclass
class DependencyContainer:
    """Dependency injection container for managing service dependencies."""
    
    template_registry: ITemplateRegistry
    dependency_resolver: IDependencyResolver
    validators: Dict[str, IValidator] = field(default_factory=dict)
    generators: Dict[str, IGenerator] = field(default_factory=dict)
    
    def register_validator(self, name: str, validator: IValidator) -> None:
        """Register a validator service."""
        self.validators[name] = validator
    
    def register_generator(self, name: str, generator: IGenerator) -> None:
        """Register a generator service."""
        self.generators[name] = generator
    
    def get_validator(self, name: str) -> Optional[IValidator]:
        """Get a validator by name."""
        return self.validators.get(name)
    
    def get_generator(self, name: str) -> Optional[IGenerator]:
        """Get a generator by name."""
        return self.generators.get(name)


class TemplateOrchestrator(IOrchestrator):
    """
    Main orchestrator service coordinating template selection and project generation.
    
    Follows the Facade pattern to provide a simplified interface to the complex
    subsystem of template management, dependency resolution, and project generation.
    """
    
    def __init__(self, container: DependencyContainer):
        """Initialize orchestrator with dependency container."""
        self.container = container
        self._current_selection: Set[str] = set()
        self._last_validation: Optional[ValidationResult] = None
        self._generation_cache: Dict[str, GenerationResult] = {}
    
    def select_templates(self, template_ids: Set[str]) -> ValidationResult:
        """
        Select templates with comprehensive validation.
        
        Args:
            template_ids: Set of template IDs to select
            
        Returns:
            ValidationResult with any issues found
        """
        self._current_selection = template_ids.copy()
        
        # Validate template existence
        validation_result = self._validate_template_existence(template_ids)
        if not validation_result.is_valid:
            self._last_validation = validation_result
            return validation_result
        
        # Resolve dependencies
        dependency_result = self.container.dependency_resolver.resolve_dependencies(template_ids)
        if not dependency_result.is_valid:
            validation_result = self._create_validation_from_dependency_result(dependency_result)
            self._last_validation = validation_result
            return validation_result
        
        # Update selection with resolved dependencies
        self._current_selection = set(dependency_result.resolved_templates)
        
        # Run additional validators
        for validator_name, validator in self.container.validators.items():
            validator_result = validator.validate(self._current_selection)
            if not validator_result.is_valid:
                validation_result.issues.extend(validator_result.issues)
                validation_result.warnings.extend(validator_result.warnings)
        
        # Final validation state
        validation_result.is_valid = not validation_result.has_errors
        self._last_validation = validation_result
        return validation_result
    
    def generate_project(self, project_name: str, selected_templates: Set[str]) -> GenerationResult:
        """
        Generate complete project with selected templates.
        
        Args:
            project_name: Name of the project to create
            selected_templates: Set of template IDs to include
            
        Returns:
            GenerationResult with generation details and any errors
        """
        start_time = time.time()
        
        try:
            # Validate selection first
            validation_result = self.select_templates(selected_templates)
            if not validation_result.is_valid:
                return GenerationResult(
                    success=False,
                    project_path=project_name,
                    errors=[f"Validation failed: {issue.message}" for issue in validation_result.issues],
                    generation_time_seconds=time.time() - start_time
                )
            
            # Create project configuration
            project_config = ProjectConfig(
                project_name=project_name,
                project_path=str(Path.cwd() / project_name),
                selected_templates=self._current_selection
            )
            
            # Resolve dependencies for generation order
            dependency_result = self.container.dependency_resolver.resolve_dependencies(self._current_selection)
            
            # Create generation context
            context = GenerationContext(
                project_config=project_config,
                dependency_result=dependency_result,
                template_registry=self.container.template_registry
            )
            
            # Generate project using all available generators
            generation_result = self._execute_generation(context)
            generation_result.generation_time_seconds = time.time() - start_time
            
            return generation_result
            
        except Exception as e:
            return GenerationResult(
                success=False,
                project_path=project_name,
                errors=[f"Generation failed: {str(e)}"],
                generation_time_seconds=time.time() - start_time
            )
    
    def get_available_templates(self) -> List[TemplateMetadata]:
        """Get all available templates with metadata."""
        templates = self.container.template_registry.get_all_templates()
        return [template.get_metadata() for template in templates]
    
    def preview_generation(self, selected_templates: Set[str]) -> Dict[str, Any]:
        """
        Preview what will be generated without creating files.
        
        Args:
            selected_templates: Set of template IDs to preview
            
        Returns:
            Dictionary with preview information
        """
        validation_result = self.select_templates(selected_templates)
        if not validation_result.is_valid:
            return {
                "valid": False,
                "errors": [issue.message for issue in validation_result.issues],
                "warnings": [issue.message for issue in validation_result.issues if issue.severity == ValidationSeverity.WARNING]
            }
        
        dependency_result = self.container.dependency_resolver.resolve_dependencies(self._current_selection)
        
        preview = {
            "valid": True,
            "selected_templates": list(self._current_selection),
            "resolved_templates": dependency_result.resolved_templates,
            "generated_files": [],
            "docker_services": {},
            "environment_variables": {},
            "required_ports": [],
            "next_steps": []
        }
        
        # Collect information from all selected templates
        for template_id in self._current_selection:
            template = self.container.template_registry.get_template(template_id)
            if template:
                metadata = template.get_metadata()
                preview["docker_services"].update(metadata.docker_services)
                preview["environment_variables"].update(metadata.environment_variables)
                preview["required_ports"].extend(metadata.required_ports)
                preview["generated_files"].extend(metadata.generated_files)
        
        # Remove duplicates from ports
        preview["required_ports"] = list(set(preview["required_ports"]))
        
        # Add generation steps
        preview["next_steps"] = [
            f"Create project directory: {preview.get('project_name', 'new-project')}",
            f"Generate {len(preview['docker_services'])} Docker services",
            f"Configure {len(preview['environment_variables'])} environment variables",
            "Run docker-compose up to start services"
        ]
        
        return preview
    
    def get_current_selection(self) -> Set[str]:
        """Get currently selected templates."""
        return self._current_selection.copy()
    
    def get_last_validation(self) -> Optional[ValidationResult]:
        """Get the last validation result."""
        return self._last_validation
    
    def _validate_template_existence(self, template_ids: Set[str]) -> ValidationResult:
        """Validate that all template IDs exist in the registry."""
        result = ValidationResult(is_valid=True)
        
        for template_id in template_ids:
            template = self.container.template_registry.get_template(template_id)
            if not template:
                result.add_error(
                    f"Template '{template_id}' not found",
                    template_id=template_id,
                    suggestion="Check available templates with 'list-templates' command"
                )
        
        return result
    
    def _create_validation_from_dependency_result(self, dependency_result: DependencyResult) -> ValidationResult:
        """Convert dependency result to validation result."""
        result = ValidationResult(is_valid=dependency_result.is_valid)
        
        # Add missing dependencies as errors
        for missing in dependency_result.missing_dependencies:
            result.add_error(
                f"Missing required dependency: {missing}",
                suggestion=f"Add template '{missing}' to your selection"
            )
        
        # Add conflicts as errors
        for template1, template2 in dependency_result.conflicts:
            result.add_error(
                f"Templates '{template1}' and '{template2}' are mutually exclusive",
                suggestion=f"Choose either '{template1}' or '{template2}', but not both"
            )
        
        # Add circular dependencies as errors
        for cycle in dependency_result.circular_dependencies:
            cycle_str = " -> ".join(cycle + [cycle[0]])
            result.add_error(
                f"Circular dependency detected: {cycle_str}",
                suggestion="Review template dependencies to break the cycle"
            )
        
        # Add optional suggestions as warnings
        for suggestion in dependency_result.optional_suggestions:
            result.add_warning(
                f"Consider adding optional template: {suggestion}",
                suggestion=f"Template '{suggestion}' would enhance your project"
            )
        
        return result
    
    def _execute_generation(self, context: GenerationContext) -> GenerationResult:
        """Execute project generation using available generators."""
        result = GenerationResult(
            success=True,
            project_path=context.project_config.project_path
        )
        
        # Create project directory
        project_path = Path(context.project_config.project_path)
        project_path.mkdir(parents=True, exist_ok=context.project_config.overwrite_existing)
        
        # Run all generators
        for generator_name, generator in self.container.generators.items():
            try:
                if generator.can_generate(context.project_config.selected_templates):
                    generator_result = generator.generate(project_path, context.project_config)
                    
                    if generator_result.success:
                        result.generated_files.extend(generator_result.generated_files)
                        result.next_steps.extend(generator_result.next_steps)
                    else:
                        result.errors.extend(generator_result.errors)
                        result.warnings.extend(generator_result.warnings)
                        
            except Exception as e:
                result.errors.append(f"Generator '{generator_name}' failed: {str(e)}")
                result.success = False
        
        # Final success state
        result.success = len(result.errors) == 0
        
        return result


class ProjectGenerator:
    """
    High-level service for coordinating project generation.
    
    This service orchestrates multiple generators to create complete projects.
    """
    
    def __init__(self, container: DependencyContainer):
        """Initialize project generator with dependency container."""
        self.container = container
    
    def generate_from_config(self, config: ProjectConfig) -> GenerationResult:
        """Generate project from configuration."""
        context = GenerationContext(
            project_config=config,
            dependency_result=self.container.dependency_resolver.resolve_dependencies(config.selected_templates),
            template_registry=self.container.template_registry
        )
        
        return self._generate_project(context)
    
    def _generate_project(self, context: GenerationContext) -> GenerationResult:
        """Internal project generation logic."""
        result = GenerationResult(
            success=True,
            project_path=context.project_config.project_path
        )
        
        project_path = Path(context.project_config.project_path)
        
        # Generate in dependency order
        for template_id in context.dependency_result.resolved_templates:
            template = self.container.template_registry.get_template(template_id)
            if template:
                try:
                    # Generate template-specific files
                    self._generate_template_files(template, project_path, context, result)
                except Exception as e:
                    result.errors.append(f"Failed to generate files for template '{template_id}': {str(e)}")
                    result.success = False
        
        return result
    
    def _generate_template_files(self, template, project_path: Path, context: GenerationContext, result: GenerationResult):
        """Generate files for a specific template."""
        metadata = template.get_metadata()
        
        # Add template's generated files to result
        for file_path in metadata.generated_files:
            result.add_generated_file(
                path=file_path,
                generated_by=metadata.id
            )
        
        # Template-specific generation logic would go here
        # This is a placeholder for the actual file generation