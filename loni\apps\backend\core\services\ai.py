"""
AI service (Legacy wrapper).

This module provides backward compatibility for the AIService
while delegating to the new specialized AI services.

DEPRECATED: Use AIOrchestrator and specialized services directly.
"""

import warnings
from typing import Dict, List, Any, Optional, AsyncGenerator
from uuid import UUID

from .ai_orchestrator import AIOrchestrator
from ..repositories.conversation import ConversationRepository, MessageRepository


class AIService:
    """
    Legacy AI service wrapper.
    
    This class maintains backward compatibility while delegating
    to the new specialized AI services.
    """
    
    def __init__(
        self,
        conversation_repository: ConversationRepository,
        message_repository: MessageRepository
    ):
        """
        Initialize the legacy AI service.
        
        Args:
            conversation_repository: Repository for conversation operations
            message_repository: Repository for message operations
        """
        warnings.warn(
            "AIService is deprecated. Use AIOrchestrator and specialized services instead.",
            DeprecationWarning,
            stacklevel=2
        )
        
        self.orchestrator = AIOrchestrator(conversation_repository, message_repository)
    
    async def get_available_models(self) -> List[Dict[str, Any]]:
        """Get list of available AI models."""
        return await self.orchestrator.get_available_models()
    
    async def chat_completion(
        self,
        user_id: UUID,
        conversation_id: UUID,
        message: str,
        model_name: str = "gpt-4",
        stream: bool = False,
        rag_enabled: bool = True
    ) -> str:
        """Generate AI response for a chat message."""
        if stream:
            # For streaming, collect the full response
            full_response = ""
            async for chunk in self.orchestrator.stream_chat_completion(
                user_id=user_id,
                conversation_id=conversation_id,
                message=message,
                model_id=model_name,
                rag_enabled=rag_enabled
            ):
                if chunk.get('type') == 'content':
                    full_response += chunk.get('content', '')
                elif chunk.get('type') == 'completion':
                    full_response = chunk.get('full_response', full_response)
            return full_response
        else:
            result = await self.orchestrator.chat_completion(
                user_id=user_id,
                conversation_id=conversation_id,
                message=message,
                model_id=model_name,
                rag_enabled=rag_enabled
            )
            return result['response']
    
    async def generate_title(self, conversation_id: UUID) -> str:
        """Generate a title for a conversation based on its messages."""
        return await self.orchestrator.generate_conversation_title(conversation_id)
    
    async def analyze_conversation(self, conversation_id: UUID) -> Dict[str, Any]:
        """Analyze conversation for insights and metrics."""
        return await self.orchestrator.analyze_conversation(conversation_id)