"""
Input validation and sanitization utilities.

This module provides comprehensive input validation and sanitization
to prevent injection attacks and ensure data integrity.
"""

import re
from typing import Any, Dict, List, Optional, Union
from urllib.parse import urlparse

from pydantic import BaseModel, Field, validator
from loguru import logger


class ValidationError(Exception):
    """Custom validation error."""
    pass


class InputSanitizer:
    """Input sanitization utilities."""
    
    # Dangerous patterns to detect
    SQL_INJECTION_PATTERNS = [
        r"(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION)\b)",
        r"(--|#|/\*|\*/)",
        r"(\b(OR|AND)\s+\d+\s*=\s*\d+)",
        r"(\bUNION\s+SELECT\b)",
        r"(\bINTO\s+OUTFILE\b)",
    ]
    
    XSS_PATTERNS = [
        r"<script[^>]*>.*?</script>",
        r"javascript:",
        r"on\w+\s*=",
        r"<iframe[^>]*>.*?</iframe>",
        r"<object[^>]*>.*?</object>",
        r"<embed[^>]*>.*?</embed>",
    ]
    
    COMMAND_INJECTION_PATTERNS = [
        r"[;&|`$(){}[\]\\]",
        r"\b(rm|del|format|shutdown|reboot|kill|ps|ls|cat|grep|find)\b",
        r"(&&|\|\|)",
    ]
    
    @classmethod
    def sanitize_string(cls, value: str, max_length: int = 1000) -> str:
        """
        Sanitize string input.
        
        Args:
            value: Input string
            max_length: Maximum allowed length
            
        Returns:
            Sanitized string
            
        Raises:
            ValidationError: If input is dangerous
        """
        if not isinstance(value, str):
            raise ValidationError("Input must be a string")
        
        # Check length
        if len(value) > max_length:
            raise ValidationError(f"Input too long (max {max_length} characters)")
        
        # Check for dangerous patterns
        cls._check_sql_injection(value)
        cls._check_xss(value)
        cls._check_command_injection(value)
        
        # Basic sanitization
        sanitized = value.strip()
        
        # Remove null bytes
        sanitized = sanitized.replace('\x00', '')
        
        # Normalize whitespace
        sanitized = re.sub(r'\s+', ' ', sanitized)
        
        return sanitized
    
    @classmethod
    def sanitize_html(cls, value: str) -> str:
        """
        Sanitize HTML content.
        
        Args:
            value: HTML string
            
        Returns:
            Sanitized HTML
        """
        # Remove dangerous tags and attributes
        dangerous_tags = ['script', 'iframe', 'object', 'embed', 'form', 'input']
        dangerous_attrs = ['onclick', 'onload', 'onerror', 'onmouseover']
        
        sanitized = value
        
        # Remove dangerous tags
        for tag in dangerous_tags:
            pattern = f'<{tag}[^>]*>.*?</{tag}>'
            sanitized = re.sub(pattern, '', sanitized, flags=re.IGNORECASE | re.DOTALL)
        
        # Remove dangerous attributes
        for attr in dangerous_attrs:
            pattern = f'{attr}\s*=\s*["\'][^"\']*["\']'
            sanitized = re.sub(pattern, '', sanitized, flags=re.IGNORECASE)
        
        return sanitized
    
    @classmethod
    def _check_sql_injection(cls, value: str) -> None:
        """Check for SQL injection patterns."""
        for pattern in cls.SQL_INJECTION_PATTERNS:
            if re.search(pattern, value, re.IGNORECASE):
                logger.warning(f"Potential SQL injection detected: {pattern}")
                raise ValidationError("Potentially dangerous SQL pattern detected")
    
    @classmethod
    def _check_xss(cls, value: str) -> None:
        """Check for XSS patterns."""
        for pattern in cls.XSS_PATTERNS:
            if re.search(pattern, value, re.IGNORECASE):
                logger.warning(f"Potential XSS detected: {pattern}")
                raise ValidationError("Potentially dangerous XSS pattern detected")
    
    @classmethod
    def _check_command_injection(cls, value: str) -> None:
        """Check for command injection patterns."""
        for pattern in cls.COMMAND_INJECTION_PATTERNS:
            if re.search(pattern, value, re.IGNORECASE):
                logger.warning(f"Potential command injection detected: {pattern}")
                raise ValidationError("Potentially dangerous command pattern detected")


class URLValidator:
    """URL validation utilities."""
    
    ALLOWED_SCHEMES = ['http', 'https']
    BLOCKED_DOMAINS = ['localhost', '127.0.0.1', '0.0.0.0']
    
    @classmethod
    def validate_url(cls, url: str, allow_local: bool = False) -> bool:
        """
        Validate URL safety.
        
        Args:
            url: URL to validate
            allow_local: Whether to allow local URLs
            
        Returns:
            True if URL is safe
            
        Raises:
            ValidationError: If URL is unsafe
        """
        try:
            parsed = urlparse(url)
        except Exception:
            raise ValidationError("Invalid URL format")
        
        # Check scheme
        if parsed.scheme not in cls.ALLOWED_SCHEMES:
            raise ValidationError(f"Unsupported URL scheme: {parsed.scheme}")
        
        # Check for blocked domains
        if not allow_local and parsed.hostname in cls.BLOCKED_DOMAINS:
            raise ValidationError(f"Blocked domain: {parsed.hostname}")
        
        # Check for private IP ranges
        if not allow_local and cls._is_private_ip(parsed.hostname):
            raise ValidationError("Private IP addresses not allowed")
        
        return True
    
    @classmethod
    def _is_private_ip(cls, hostname: str) -> bool:
        """Check if hostname is a private IP."""
        if not hostname:
            return False
        
        # Simple check for common private IP patterns
        private_patterns = [
            r'^10\.',
            r'^172\.(1[6-9]|2[0-9]|3[01])\.',
            r'^192\.168\.',
            r'^127\.',
        ]
        
        for pattern in private_patterns:
            if re.match(pattern, hostname):
                return True
        
        return False


class ContentValidator:
    """Content validation utilities."""
    
    @classmethod
    def validate_message_content(cls, content: str) -> str:
        """
        Validate chat message content.
        
        Args:
            content: Message content
            
        Returns:
            Validated content
        """
        # Basic sanitization
        content = InputSanitizer.sanitize_string(content, max_length=10000)
        
        # Check for empty content
        if not content.strip():
            raise ValidationError("Message content cannot be empty")
        
        # Check for excessive repetition
        if cls._has_excessive_repetition(content):
            raise ValidationError("Message contains excessive repetition")
        
        return content
    
    @classmethod
    def validate_document_content(cls, content: str, content_type: str) -> str:
        """
        Validate document content.
        
        Args:
            content: Document content
            content_type: Content MIME type
            
        Returns:
            Validated content
        """
        # Check content length
        max_length = 1000000  # 1MB text limit
        if len(content) > max_length:
            raise ValidationError(f"Document too large (max {max_length} characters)")
        
        # Content type specific validation
        if content_type and 'html' in content_type.lower():
            content = InputSanitizer.sanitize_html(content)
        else:
            content = InputSanitizer.sanitize_string(content, max_length=max_length)
        
        return content
    
    @classmethod
    def validate_model_name(cls, model_name: str) -> str:
        """
        Validate AI model name.
        
        Args:
            model_name: Model name
            
        Returns:
            Validated model name
        """
        # Allow only alphanumeric, hyphens, dots, and colons
        if not re.match(r'^[a-zA-Z0-9\-\.:]+$', model_name):
            raise ValidationError("Invalid model name format")
        
        # Check length
        if len(model_name) > 100:
            raise ValidationError("Model name too long")
        
        return model_name.lower()
    
    @classmethod
    def _has_excessive_repetition(cls, content: str) -> bool:
        """Check for excessive character or word repetition."""
        # Check for repeated characters
        if re.search(r'(.)\1{20,}', content):
            return True
        
        # Check for repeated words
        words = content.split()
        if len(words) > 10:
            word_counts = {}
            for word in words:
                word_counts[word] = word_counts.get(word, 0) + 1
                if word_counts[word] > len(words) * 0.5:  # More than 50% repetition
                    return True
        
        return False


class RateLimitValidator:
    """Rate limiting validation."""
    
    # In-memory rate limiting (in production, use Redis)
    _request_counts: Dict[str, Dict[str, int]] = {}
    
    @classmethod
    def check_rate_limit(
        cls, 
        identifier: str, 
        limit: int, 
        window_seconds: int = 3600
    ) -> bool:
        """
        Check if request is within rate limit.
        
        Args:
            identifier: Unique identifier (user ID, IP, etc.)
            limit: Maximum requests allowed
            window_seconds: Time window in seconds
            
        Returns:
            True if within limit
            
        Raises:
            ValidationError: If rate limit exceeded
        """
        import time
        
        current_time = int(time.time())
        window_start = current_time - window_seconds
        
        # Clean old entries
        if identifier in cls._request_counts:
            cls._request_counts[identifier] = {
                timestamp: count 
                for timestamp, count in cls._request_counts[identifier].items()
                if int(timestamp) > window_start
            }
        else:
            cls._request_counts[identifier] = {}
        
        # Count requests in current window
        total_requests = sum(cls._request_counts[identifier].values())
        
        if total_requests >= limit:
            raise ValidationError(f"Rate limit exceeded: {total_requests}/{limit} requests")
        
        # Record current request
        timestamp_key = str(current_time)
        cls._request_counts[identifier][timestamp_key] = (
            cls._request_counts[identifier].get(timestamp_key, 0) + 1
        )
        
        return True


# Pydantic validators for common use cases
class SafeString(str):
    """String type with automatic sanitization."""
    
    @classmethod
    def __get_validators__(cls):
        yield cls.validate
    
    @classmethod
    def validate(cls, v):
        if not isinstance(v, str):
            raise TypeError('string required')
        return InputSanitizer.sanitize_string(v)


class SafeURL(str):
    """URL type with validation."""
    
    @classmethod
    def __get_validators__(cls):
        yield cls.validate
    
    @classmethod
    def validate(cls, v):
        if not isinstance(v, str):
            raise TypeError('string required')
        URLValidator.validate_url(v)
        return v


class SafeModelName(str):
    """Model name type with validation."""
    
    @classmethod
    def __get_validators__(cls):
        yield cls.validate
    
    @classmethod
    def validate(cls, v):
        if not isinstance(v, str):
            raise TypeError('string required')
        return ContentValidator.validate_model_name(v)
