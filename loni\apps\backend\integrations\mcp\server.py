"""
MCP Server implementation.

This module provides the MCP server for handling AI model communication
following the Model Context Protocol specification.
"""

import asyncio
import json
from typing import Any, Callable, Dict, List, Optional
from uuid import uuid4
from datetime import datetime

from loguru import logger

from .types import (
    MCPRequest, MCPResponse, MCPTool, MCPResource, MCPCapabilities,
    MCPServerInfo, MCPError, MCPToolCall, MCPToolResult, MCPResourceContent,
    MCPMethods, MCPErrorCodes, MCPSession
)


class MCPServer:
    """
    MCP Server implementation.
    
    Provides standardized AI model communication following the
    Model Context Protocol specification.
    """
    
    def __init__(self, name: str, version: str = "1.0.0"):
        """
        Initialize MCP server.
        
        Args:
            name: Server name
            version: Server version
        """
        self.name = name
        self.version = version
        self.capabilities = MCPCapabilities()
        self.sessions: Dict[str, MCPSession] = {}
        
        # Tool and resource handlers
        self._tool_handlers: Dict[str, Callable] = {}
        self._resource_handlers: Dict[str, Callable] = {}
        self._prompt_handlers: Dict[str, Callable] = {}
        
        # Register core methods
        self._register_core_methods()
    
    def _register_core_methods(self) -> None:
        """Register core MCP methods."""
        self._method_handlers = {
            MCPMethods.INITIALIZE: self._handle_initialize,
            MCPMethods.PING: self._handle_ping,
            MCPMethods.LIST_TOOLS: self._handle_list_tools,
            MCPMethods.CALL_TOOL: self._handle_call_tool,
            MCPMethods.LIST_RESOURCES: self._handle_list_resources,
            MCPMethods.READ_RESOURCE: self._handle_read_resource,
            MCPMethods.LIST_PROMPTS: self._handle_list_prompts,
            MCPMethods.GET_PROMPT: self._handle_get_prompt,
        }
    
    def register_tool(self, tool: MCPTool, handler: Callable) -> None:
        """
        Register a tool with its handler.
        
        Args:
            tool: Tool definition
            handler: Tool execution handler
        """
        self.capabilities.tools.append(tool)
        self._tool_handlers[tool.name] = handler
        logger.info(f"Registered MCP tool: {tool.name}")
    
    def register_resource(self, resource: MCPResource, handler: Callable) -> None:
        """
        Register a resource with its handler.
        
        Args:
            resource: Resource definition
            handler: Resource access handler
        """
        self.capabilities.resources.append(resource)
        self._resource_handlers[resource.uri] = handler
        logger.info(f"Registered MCP resource: {resource.uri}")
    
    def tool(self, name: str, description: str, parameters: Optional[Dict[str, Any]] = None):
        """
        Decorator for registering tools.
        
        Args:
            name: Tool name
            description: Tool description
            parameters: Tool parameters schema
        """
        def decorator(func: Callable) -> Callable:
            tool = MCPTool(
                name=name,
                description=description,
                parameters=parameters or {}
            )
            self.register_tool(tool, func)
            return func
        return decorator
    
    def resource(self, uri: str, name: str, description: Optional[str] = None, mime_type: Optional[str] = None):
        """
        Decorator for registering resources.
        
        Args:
            uri: Resource URI
            name: Resource name
            description: Resource description
            mime_type: Resource MIME type
        """
        def decorator(func: Callable) -> Callable:
            resource = MCPResource(
                uri=uri,
                name=name,
                description=description,
                mime_type=mime_type
            )
            self.register_resource(resource, func)
            return func
        return decorator
    
    async def handle_request(self, request_data: str, session_id: Optional[str] = None) -> str:
        """
        Handle incoming MCP request.
        
        Args:
            request_data: JSON request data
            session_id: Optional session identifier
            
        Returns:
            JSON response data
        """
        try:
            # Parse request
            request_dict = json.loads(request_data)
            request = MCPRequest(**request_dict)
            
            # Update session activity
            if session_id and session_id in self.sessions:
                self.sessions[session_id].last_activity = datetime.utcnow()
            
            # Handle request
            response = await self._dispatch_request(request, session_id)
            
            return json.dumps(response.dict())
            
        except json.JSONDecodeError:
            error_response = MCPResponse(
                id="unknown",
                error={
                    "code": MCPErrorCodes.PARSE_ERROR,
                    "message": "Invalid JSON"
                }
            )
            return json.dumps(error_response.dict())
        except Exception as e:
            logger.error(f"MCP request handling failed: {e}")
            error_response = MCPResponse(
                id=getattr(request, 'id', 'unknown'),
                error={
                    "code": MCPErrorCodes.INTERNAL_ERROR,
                    "message": str(e)
                }
            )
            return json.dumps(error_response.dict())
    
    async def _dispatch_request(self, request: MCPRequest, session_id: Optional[str] = None) -> MCPResponse:
        """
        Dispatch request to appropriate handler.
        
        Args:
            request: MCP request
            session_id: Session identifier
            
        Returns:
            MCP response
        """
        handler = self._method_handlers.get(request.method)
        
        if not handler:
            return MCPResponse(
                id=request.id,
                error={
                    "code": MCPErrorCodes.METHOD_NOT_FOUND,
                    "message": f"Method not found: {request.method}"
                }
            )
        
        try:
            result = await handler(request.params or {}, session_id)
            return MCPResponse(id=request.id, result=result)
        except Exception as e:
            logger.error(f"Method {request.method} failed: {e}")
            return MCPResponse(
                id=request.id,
                error={
                    "code": MCPErrorCodes.INTERNAL_ERROR,
                    "message": str(e)
                }
            )
    
    async def _handle_initialize(self, params: Dict[str, Any], session_id: Optional[str] = None) -> Dict[str, Any]:
        """Handle initialize request."""
        # Create new session
        if not session_id:
            session_id = str(uuid4())
        
        session = MCPSession(
            session_id=session_id,
            client_info=params.get("client_info", {}),
            server_info=MCPServerInfo(
                name=self.name,
                version=self.version,
                capabilities=self.capabilities
            )
        )
        
        self.sessions[session_id] = session
        
        return {
            "session_id": session_id,
            "server_info": session.server_info.dict(),
            "capabilities": self.capabilities.dict()
        }
    
    async def _handle_ping(self, params: Dict[str, Any], session_id: Optional[str] = None) -> Dict[str, Any]:
        """Handle ping request."""
        return {"pong": True, "timestamp": datetime.utcnow().isoformat()}
    
    async def _handle_list_tools(self, params: Dict[str, Any], session_id: Optional[str] = None) -> Dict[str, Any]:
        """Handle list tools request."""
        return {"tools": [tool.dict() for tool in self.capabilities.tools]}
    
    async def _handle_call_tool(self, params: Dict[str, Any], session_id: Optional[str] = None) -> Dict[str, Any]:
        """Handle tool call request."""
        tool_name = params.get("name")
        arguments = params.get("arguments", {})
        
        if not tool_name:
            raise ValueError("Tool name is required")
        
        handler = self._tool_handlers.get(tool_name)
        if not handler:
            raise ValueError(f"Tool not found: {tool_name}")
        
        try:
            result = await handler(**arguments)
            return {"result": result}
        except Exception as e:
            logger.error(f"Tool {tool_name} execution failed: {e}")
            raise ValueError(f"Tool execution failed: {str(e)}")
    
    async def _handle_list_resources(self, params: Dict[str, Any], session_id: Optional[str] = None) -> Dict[str, Any]:
        """Handle list resources request."""
        return {"resources": [resource.dict() for resource in self.capabilities.resources]}
    
    async def _handle_read_resource(self, params: Dict[str, Any], session_id: Optional[str] = None) -> Dict[str, Any]:
        """Handle read resource request."""
        uri = params.get("uri")
        
        if not uri:
            raise ValueError("Resource URI is required")
        
        handler = self._resource_handlers.get(uri)
        if not handler:
            raise ValueError(f"Resource not found: {uri}")
        
        try:
            content = await handler(uri)
            return {"content": content}
        except Exception as e:
            logger.error(f"Resource {uri} access failed: {e}")
            raise ValueError(f"Resource access failed: {str(e)}")
    
    async def _handle_list_prompts(self, params: Dict[str, Any], session_id: Optional[str] = None) -> Dict[str, Any]:
        """Handle list prompts request."""
        return {"prompts": self.capabilities.prompts}
    
    async def _handle_get_prompt(self, params: Dict[str, Any], session_id: Optional[str] = None) -> Dict[str, Any]:
        """Handle get prompt request."""
        prompt_name = params.get("name")
        
        if not prompt_name:
            raise ValueError("Prompt name is required")
        
        # For now, return a placeholder
        return {"prompt": f"Prompt {prompt_name} content"}
    
    def get_server_info(self) -> MCPServerInfo:
        """Get server information."""
        return MCPServerInfo(
            name=self.name,
            version=self.version,
            capabilities=self.capabilities
        )
    
    def get_active_sessions(self) -> List[MCPSession]:
        """Get list of active sessions."""
        return [session for session in self.sessions.values() if session.is_active]
    
    def close_session(self, session_id: str) -> bool:
        """
        Close a session.
        
        Args:
            session_id: Session identifier
            
        Returns:
            True if session was closed, False if not found
        """
        if session_id in self.sessions:
            self.sessions[session_id].is_active = False
            return True
        return False


# Example usage and tool registration
async def example_usage():
    """Example of how to use the MCP server."""
    server = MCPServer("loni-mcp-server", "1.0.0")

    # Register a tool using decorator
    @server.tool(
        name="search_documents",
        description="Search documents using semantic similarity",
        parameters={
            "type": "object",
            "properties": {
                "query": {"type": "string", "description": "Search query"},
                "limit": {"type": "integer", "description": "Maximum results", "default": 5}
            },
            "required": ["query"]
        }
    )
    async def search_documents(query: str, limit: int = 5) -> List[Dict[str, Any]]:
        """Search documents implementation."""
        # This would integrate with the actual RAG service
        return [
            {"title": "Example Document", "content": "Sample content", "score": 0.95}
        ]

    # Register a resource using decorator
    @server.resource(
        uri="loni://user/profile",
        name="User Profile",
        description="Current user profile information",
        mime_type="application/json"
    )
    async def get_user_profile(uri: str) -> Dict[str, Any]:
        """Get user profile implementation."""
        # This would integrate with the user service
        return {"id": "123", "name": "John Doe", "email": "<EMAIL>"}

    return server
