/**
 * Chat input component with enhanced features
 */
'use client'

import { useState, useRef, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Textarea } from '@/components/ui/textarea'
import { Badge } from '@/components/ui/badge'
import { 
  Send, 
  Paperclip, 
  Mic, 
  Square,
  Settings,
  Zap,
  FileText
} from 'lucide-react'
import type { ModelInfo } from '@/types'

interface ChatInputProps {
  onSend: (message: string) => void
  onSendStream?: (message: string) => void
  isGenerating?: boolean
  placeholder?: string
  maxLength?: number
  currentModel?: string
  models?: ModelInfo[]
  onModelChange?: (modelId: string) => void
  ragEnabled?: boolean
  onRagToggle?: () => void
  temperature?: number
  onTemperatureChange?: (temp: number) => void
  className?: string
}

export function ChatInput({
  onSend,
  onSendStream,
  isGenerating = false,
  placeholder = "Type your message...",
  maxLength = 10000,
  currentModel = 'gpt-4',
  models = [],
  onModelChange,
  ragEnabled = true,
  onRagToggle,
  temperature = 0.7,
  onTemperatureChange,
  className
}: ChatInputProps) {
  const [message, setMessage] = useState('')
  const [showSettings, setShowSettings] = useState(false)
  const [isRecording, setIsRecording] = useState(false)
  const textareaRef = useRef<HTMLTextAreaElement>(null)

  const isEmptyMessage = !message.trim()
  const isOverLimit = message.length > maxLength

  // Auto-resize textarea
  useEffect(() => {
    if (textareaRef.current) {
      textareaRef.current.style.height = 'auto'
      textareaRef.current.style.height = `${textareaRef.current.scrollHeight}px`
    }
  }, [message])

  const handleSubmit = (useStream = false) => {
    if (isEmptyMessage || isOverLimit || isGenerating) return

    const trimmedMessage = message.trim()
    
    if (useStream && onSendStream) {
      onSendStream(trimmedMessage)
    } else {
      onSend(trimmedMessage)
    }
    
    setMessage('')
    
    // Reset textarea height
    if (textareaRef.current) {
      textareaRef.current.style.height = 'auto'
    }
  }

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      handleSubmit(e.metaKey || e.ctrlKey) // Use streaming on Cmd/Ctrl+Enter
    }
  }

  const handleStopGeneration = () => {
    // This would be handled by the parent component
    // by cancelling the streaming request
  }

  const currentModelInfo = models.find(m => m.id === currentModel)

  return (
    <div className={`border-t bg-background p-4 ${className}`}>
      {/* Settings Panel */}
      {showSettings && (
        <div className="mb-4 p-4 bg-gray-50 rounded-lg border">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {/* Model Selection */}
            <div>
              <label className="block text-sm font-medium mb-2">Model</label>
              <select
                value={currentModel}
                onChange={(e) => onModelChange?.(e.target.value)}
                className="w-full p-2 border rounded-md text-sm bg-white"
              >
                {models.map((model) => (
                  <option key={model.id} value={model.id}>
                    {model.name} ({model.provider})
                  </option>
                ))}
              </select>
            </div>

            {/* Temperature */}
            <div>
              <label className="block text-sm font-medium mb-2">
                Temperature: {temperature}
              </label>
              <input
                type="range"
                min="0"
                max="2"
                step="0.1"
                value={temperature}
                onChange={(e) => onTemperatureChange?.(parseFloat(e.target.value))}
                className="w-full"
              />
            </div>

            {/* RAG Toggle */}
            <div>
              <label className="block text-sm font-medium mb-2">Features</label>
              <Button
                variant={ragEnabled ? "ai-primary" : "outline"}
                size="sm"
                onClick={onRagToggle}
                className="w-full"
              >
                <FileText className="w-4 h-4 mr-2" />
                RAG {ragEnabled ? 'Enabled' : 'Disabled'}
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Input Area */}
      <div className="flex flex-col gap-3">
        {/* Current Model Info */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Badge variant="ai-primary" className="text-xs">
              {currentModelInfo?.name || currentModel}
            </Badge>
            {ragEnabled && (
              <Badge variant="ai-secondary" className="text-xs">
                RAG Enabled
              </Badge>
            )}
            <Badge variant="outline" className="text-xs">
              Temp: {temperature}
            </Badge>
          </div>

          <Button
            variant="ghost"
            size="sm"
            onClick={() => setShowSettings(!showSettings)}
            className="text-gray-500 hover:text-gray-700"
          >
            <Settings className="w-4 h-4" />
          </Button>
        </div>

        {/* Input Row */}
        <div className="flex gap-2 items-end">
          {/* Attachment Button */}
          <Button
            variant="ghost"
            size="icon"
            className="flex-shrink-0 mb-1"
            disabled={isGenerating}
          >
            <Paperclip className="w-4 h-4" />
          </Button>

          {/* Text Input */}
          <div className="flex-1 relative">
            <Textarea
              ref={textareaRef}
              value={message}
              onChange={(e) => setMessage(e.target.value)}
              onKeyDown={handleKeyDown}
              placeholder={placeholder}
              disabled={isGenerating}
              className={`
                min-h-[44px] max-h-[200px] resize-none pr-12
                ${isOverLimit ? 'border-red-500 focus:border-red-500' : ''}
              `}
              rows={1}
            />
            
            {/* Character Counter */}
            <div className="absolute bottom-2 right-2 text-xs text-gray-400">
              <span className={isOverLimit ? 'text-red-500' : ''}>
                {message.length}
              </span>
              /{maxLength}
            </div>
          </div>

          {/* Voice Input Button */}
          <Button
            variant={isRecording ? "destructive" : "ghost"}
            size="icon"
            className="flex-shrink-0 mb-1"
            disabled={isGenerating}
            onClick={() => setIsRecording(!isRecording)}
          >
            <Mic className="w-4 h-4" />
          </Button>

          {/* Send/Stop Button */}
          {isGenerating ? (
            <Button
              variant="destructive"
              size="icon"
              onClick={handleStopGeneration}
              className="flex-shrink-0 mb-1"
            >
              <Square className="w-4 h-4" />
            </Button>
          ) : (
            <div className="flex gap-1">
              {/* Regular Send */}
              <Button
                variant="loni"
                size="icon"
                onClick={() => handleSubmit(false)}
                disabled={isEmptyMessage || isOverLimit}
                className="flex-shrink-0 mb-1"
              >
                <Send className="w-4 h-4" />
              </Button>
              
              {/* Stream Send */}
              {onSendStream && (
                <Button
                  variant="ai-primary"
                  size="icon"
                  onClick={() => handleSubmit(true)}
                  disabled={isEmptyMessage || isOverLimit}
                  className="flex-shrink-0 mb-1"
                  title="Send with streaming (Cmd/Ctrl+Enter)"
                >
                  <Zap className="w-4 h-4" />
                </Button>
              )}
            </div>
          )}
        </div>

        {/* Help Text */}
        <div className="flex justify-between text-xs text-gray-500">
          <span>
            Press Enter to send, Shift+Enter for new line
            {onSendStream && ', Cmd/Ctrl+Enter for streaming'}
          </span>
          {isGenerating && (
            <span className="text-ai-primary animate-pulse">
              AI is thinking...
            </span>
          )}
        </div>
      </div>
    </div>
  )
}