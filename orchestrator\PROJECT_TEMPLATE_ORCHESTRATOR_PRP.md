# PRP: Project Template Orchestrator

## FEATURE

### Core Functionality
Create a sophisticated project template orchestrator that provides an interactive checkbox-based interface for selecting and combining pre-built templates, services, and technologies. The system should intelligently manage dependencies, prevent conflicts, and generate fully configured Docker-based project environments.

### Key Requirements
1. **Interactive Template Selection**: Checkbox interface with real-time dependency validation
2. **Smart Dependency Management**: Automatic enabling/disabling of related templates based on selections
3. **Conflict Prevention**: Mutually exclusive options (<PERSON><PERSON> ↔ Electron, npm ↔ pnpm ↔ bun)
4. **Project Generation**: Create new directory with selected Docker implementations and configurations
5. **Configuration Management**: Generate environment files, docker-compose.yml, and service configurations
6. **Template Library**: Reusable component library (ShadCN, custom components) that don't need rebuilding
7. **SOLID Principles**: Maintainable, extensible architecture following SOLID design principles

### Specific Template Categories
- **Backend Services**: PostgreSQL, Supabase, Qdrant, Neo4j, Redis, MinIO
- **Frontend Frameworks**: React 18, NextJS, Streamlit
- **UI Libraries**: ShadCN UI, Radix UI, TailwindCSS, Framer Motion
- **Build Tools**: Vite, Webpack, esbuild
- **Package Managers**: npm, pnpm, bun, yarn (mutually exclusive)
- **Desktop Frameworks**: Tauri, Electron (mutually exclusive)
- **AI/ML Services**: Ollama, OpenAI, Anthropic, LangFuse, Mem0
- **Monitoring**: LangFuse, Prometheus, Grafana
- **Reverse Proxies**: Caddy, Nginx
- **Workflow Tools**: n8n, Flowise

### Acceptance Criteria
- [ ] Checkbox interface with dependency validation
- [ ] Real-time conflict detection and prevention
- [ ] Project directory generation with selected templates
- [ ] Docker Compose file generation with chosen services
- [ ] Environment configuration generation
- [ ] Template library for reusable components
- [ ] Extensible architecture for adding new templates
- [ ] Comprehensive validation and error handling
- [ ] CLI and optional GUI interface

## EXAMPLES

### Similar Implementation Patterns from Codebase

#### 1. Docker Compose Orchestration (local-ai-packaged)
```yaml
# Reference: local-ai-packaged/docker-compose.yml
services:
  supabase:
    image: supabase/postgres:15.1.0.147
    environment:
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"

  qdrant:
    image: qdrant/qdrant:latest
    ports:
      - "6333:6333"
    volumes:
      - qdrant_data:/qdrant/storage
```

#### 2. Environment Configuration Management (ai-agent-mastery)
```python
# Reference: ai-agent-mastery/6_Agent_Deployment/deploy.py
def create_env_files(deployment_type: str, project_name: str):
    """Generate environment files based on deployment configuration"""
    env_template = {
        'POSTGRES_PASSWORD': generate_password(),
        'SUPABASE_URL': f'http://localhost:8000',
        'OPENAI_API_KEY': os.getenv('OPENAI_API_KEY', ''),
        'DEPLOYMENT_TYPE': deployment_type
    }
    
    with open(f'{project_name}/.env', 'w') as f:
        for key, value in env_template.items():
            f.write(f'{key}={value}\n')
```

#### 3. Dependency Injection Pattern (ai-agent-mastery)
```python
# Reference: ai-agent-mastery/4_Pydantic_AI_Agent/agent.py
@dataclass
class AgentDeps:
    """Dependency injection container for agent dependencies"""
    supabase_client: SupabaseClient
    embedding_client: EmbeddingClient
    memory_client: MemoryClient
    
    @classmethod
    def from_env(cls) -> 'AgentDeps':
        return cls(
            supabase_client=create_supabase_client(),
            embedding_client=create_embedding_client(),
            memory_client=create_memory_client()
        )
```

#### 4. Component Library Structure (ai-agent-mastery/frontend)
```typescript
// Reference: ai-agent-mastery/5_Agent_Application/frontend/src/components/ui/
export { Button } from './button'
export { Input } from './input'
export { Card, CardContent, CardHeader } from './card'
export { Dialog, DialogContent, DialogTrigger } from './dialog'
// Centralized component exports for reusability
```

#### 5. Configuration Validation (Context-Engineering-Intro)
```python
# Reference pattern for validation
def validate_configuration(selected_templates: List[str]) -> ValidationResult:
    """Validate template selections for conflicts and dependencies"""
    conflicts = check_mutual_exclusions(selected_templates)
    missing_deps = check_required_dependencies(selected_templates)
    
    return ValidationResult(
        is_valid=len(conflicts) == 0 and len(missing_deps) == 0,
        conflicts=conflicts,
        missing_dependencies=missing_deps
    )
```

### Frontend Checkbox Dependency Example
```typescript
// Intelligent checkbox dependency management
const handleTemplateSelection = (templateId: string, isSelected: boolean) => {
  const newSelection = { ...selectedTemplates, [templateId]: isSelected };
  
  // Auto-enable dependencies
  if (isSelected) {
    const dependencies = getTemplateDependencies(templateId);
    dependencies.forEach(dep => {
      newSelection[dep] = true;
    });
  }
  
  // Auto-disable conflicts
  const conflicts = getTemplateConflicts(templateId);
  conflicts.forEach(conflict => {
    newSelection[conflict] = false;
  });
  
  setSelectedTemplates(newSelection);
};
```

## DOCUMENTATION

### Architecture References

#### 1. SOLID Principles Implementation Guide
- **Single Responsibility Principle**: Each class/module has one reason to change
- **Open/Closed Principle**: Open for extension, closed for modification
- **Liskov Substitution Principle**: Derived classes must be substitutable for base classes
- **Interface Segregation Principle**: Many client-specific interfaces rather than one general-purpose interface
- **Dependency Inversion Principle**: Depend upon abstractions, not concretions

#### 2. Template System Architecture
```
TemplateOrchestrator/
├── core/
│   ├── interfaces/          # Abstract base classes and protocols
│   ├── models/             # Data models and DTOs
│   ├── services/           # Business logic services
│   └── validators/         # Validation logic
├── templates/
│   ├── backend/            # Backend service templates
│   ├── frontend/           # Frontend framework templates
│   ├── database/           # Database service templates
│   └── infrastructure/     # Docker, networking templates
├── generators/
│   ├── docker_compose/     # Docker Compose file generation
│   ├── environment/        # Environment configuration generation
│   └── project_structure/  # Directory structure generation
└── ui/
    ├── cli/               # Command-line interface
    └── web/               # Optional web interface
```

#### 3. Docker Compose Generation Patterns
Reference: `ai-agent-mastery/6_Agent_Deployment/docker-compose.yml`
- Service definitions with proper networking
- Volume management for data persistence
- Environment variable templating
- Health checks and dependencies
- Resource limits and constraints

#### 4. Environment Configuration Best Practices
Reference: `ai-agent-mastery/blueprints/*.env.example`
- Secure default values
- Clear variable documentation
- Service-specific grouping
- Validation and type checking

### External Documentation

#### 1. Docker Compose Specification
- **URL**: https://docs.docker.com/compose/compose-file/
- **Key Sections**: Services, Networks, Volumes, Extensions
- **Version**: 3.8+ for modern features

#### 2. FastAPI Project Generation
- **URL**: https://fastapi.tiangolo.com/project-generation/
- **Patterns**: Project structure, dependency injection, configuration management

#### 3. React Component Library Design
- **URL**: https://react.dev/learn/sharing-state-between-components
- **Patterns**: Component composition, prop drilling prevention, context usage

#### 4. TypeScript Design Patterns
- **URL**: https://refactoring.guru/design-patterns/typescript
- **Patterns**: Factory, Builder, Template Method, Strategy

## OTHER CONSIDERATIONS

### 1. Template Discovery and Registration

#### Automatic Template Detection
```python
@dataclass
class TemplateMetadata:
    id: str
    name: str
    description: str
    category: TemplateCategory
    dependencies: List[str]
    conflicts: List[str]
    docker_services: List[str]
    environment_variables: Dict[str, str]
    required_ports: List[int]
    
class TemplateRegistry:
    """Centralized registry for all available templates"""
    
    def register_template(self, template: TemplateMetadata) -> None:
        """Register a new template with dependency validation"""
        pass
    
    def discover_templates(self, directory: Path) -> List[TemplateMetadata]:
        """Automatically discover templates from directory structure"""
        pass
```

### 2. Dependency Resolution Algorithm

#### Smart Dependency Management
```python
class DependencyResolver:
    """Resolves template dependencies and conflicts using graph algorithms"""
    
    def resolve_dependencies(self, selected: Set[str]) -> DependencyResult:
        """Resolve all dependencies for selected templates"""
        dependency_graph = self._build_dependency_graph(selected)
        resolved = self._topological_sort(dependency_graph)
        return DependencyResult(
            resolved_order=resolved,
            conflicts=self._detect_conflicts(selected),
            missing_dependencies=self._find_missing_deps(selected)
        )
    
    def _detect_conflicts(self, templates: Set[str]) -> List[Conflict]:
        """Detect mutually exclusive template conflicts"""
        conflicts = []
        for template in templates:
            conflicting = self.registry.get_conflicts(template)
            for conflict in conflicting:
                if conflict in templates:
                    conflicts.append(Conflict(template, conflict))
        return conflicts
```

### 3. Project Generation Engine

#### Modular Generation System
```python
class ProjectGenerator:
    """Generates complete project structure with selected templates"""
    
    def __init__(self, template_registry: TemplateRegistry):
        self.registry = template_registry
        self.generators = {
            GeneratorType.DOCKER_COMPOSE: DockerComposeGenerator(),
            GeneratorType.ENVIRONMENT: EnvironmentGenerator(),
            GeneratorType.PROJECT_STRUCTURE: ProjectStructureGenerator(),
            GeneratorType.CONFIGURATION: ConfigurationGenerator()
        }
    
    def generate_project(self, project_name: str, templates: List[str]) -> ProjectResult:
        """Generate complete project with all selected templates"""
        project_path = Path(project_name)
        project_path.mkdir(exist_ok=True)
        
        # Resolve dependencies
        resolved = self.dependency_resolver.resolve_dependencies(set(templates))
        if not resolved.is_valid:
            raise ValidationError(resolved.conflicts)
        
        # Generate all components
        for generator_type, generator in self.generators.items():
            generator.generate(project_path, resolved.templates)
        
        return ProjectResult(
            project_path=project_path,
            generated_files=self._get_generated_files(project_path),
            next_steps=self._generate_next_steps(resolved.templates)
        )
```

### 4. Configuration Management

#### Environment-Aware Configuration
```python
class ConfigurationManager:
    """Manages environment-specific configurations"""
    
    def generate_env_files(self, templates: List[str], environment: Environment) -> Dict[str, str]:
        """Generate environment files based on selected templates and target environment"""
        base_config = self._get_base_configuration()
        
        for template in templates:
            template_config = self.registry.get_template_config(template)
            base_config.update(template_config.get_environment_vars(environment))
        
        return base_config
    
    def validate_configuration(self, config: Dict[str, str]) -> ValidationResult:
        """Validate generated configuration for completeness and correctness"""
        required_vars = self._get_required_variables()
        missing = [var for var in required_vars if var not in config]
        
        return ValidationResult(
            is_valid=len(missing) == 0,
            missing_variables=missing,
            warnings=self._check_default_values(config)
        )
```

### 5. Anti-Pattern Prevention

#### Common Pitfalls and Solutions

**Anti-Pattern**: Hardcoded Template Definitions
```python
# BAD: Hardcoded template logic
if template == "shadcn":
    if "nextjs" not in selected:
        raise ValueError("ShadCN requires NextJS")

# GOOD: Declarative dependency definition
@dataclass
class ShadCNTemplate(Template):
    dependencies = ["nextjs"]
    conflicts = []
```

**Anti-Pattern**: Tight Coupling Between UI and Business Logic
```python
# BAD: UI logic mixed with business logic
def handle_checkbox_change(template_id, checked):
    # UI manipulation mixed with validation
    checkbox.disabled = not can_select(template_id)
    update_database(template_id, checked)

# GOOD: Separated concerns
class TemplateSelectionService:
    def select_template(self, template_id: str) -> SelectionResult:
        return self.validator.validate_selection(template_id)

class UIController:
    def handle_selection(self, template_id: str):
        result = self.service.select_template(template_id)
        self.view.update_ui(result)
```

**Anti-Pattern**: Monolithic Generator Class
```python
# BAD: Single class doing everything
class ProjectGenerator:
    def generate_everything(self, templates):
        self.generate_docker_compose()
        self.generate_environment()
        self.generate_frontend()
        self.generate_backend()
        # ... 500 lines later

# GOOD: Composition with specialized generators
class ProjectGenerator:
    def __init__(self):
        self.generators = [
            DockerComposeGenerator(),
            EnvironmentGenerator(),
            FrontendGenerator(),
            BackendGenerator()
        ]
    
    def generate(self, templates):
        for generator in self.generators:
            generator.generate(templates)
```

### 6. Performance Considerations

#### Lazy Loading and Caching
```python
class TemplateCache:
    """Cache template metadata and generation artifacts"""
    
    def __init__(self):
        self._template_cache: Dict[str, TemplateMetadata] = {}
        self._generation_cache: Dict[str, GeneratedArtifact] = {}
    
    @lru_cache(maxsize=128)
    def get_template_dependencies(self, template_id: str) -> List[str]:
        """Cached dependency lookup"""
        return self.registry.get_dependencies(template_id)
```

### 7. Extensibility Framework

#### Plugin System for Custom Templates
```python
class TemplatePlugin(ABC):
    """Abstract base class for template plugins"""
    
    @abstractmethod
    def get_metadata(self) -> TemplateMetadata:
        """Return template metadata"""
        pass
    
    @abstractmethod
    def generate_files(self, project_path: Path, config: Dict[str, Any]) -> List[Path]:
        """Generate template-specific files"""
        pass
    
    @abstractmethod
    def validate_dependencies(self, selected_templates: Set[str]) -> ValidationResult:
        """Validate template-specific dependencies"""
        pass

class PluginManager:
    """Manages template plugins and discovery"""
    
    def load_plugins(self, plugin_directory: Path) -> List[TemplatePlugin]:
        """Dynamically load template plugins"""
        pass
    
    def register_plugin(self, plugin: TemplatePlugin) -> None:
        """Register a new template plugin"""
        pass
```

### 8. Error Handling and Recovery

#### Comprehensive Error Management
```python
class TemplateError(Exception):
    """Base exception for template-related errors"""
    pass

class DependencyConflictError(TemplateError):
    """Raised when mutually exclusive templates are selected"""
    
    def __init__(self, conflicts: List[Conflict]):
        self.conflicts = conflicts
        super().__init__(f"Template conflicts detected: {conflicts}")

class MissingDependencyError(TemplateError):
    """Raised when required dependencies are missing"""
    
    def __init__(self, missing: List[str]):
        self.missing_dependencies = missing
        super().__init__(f"Missing required dependencies: {missing}")

class ProjectGenerationError(TemplateError):
    """Raised when project generation fails"""
    
    def __init__(self, stage: str, original_error: Exception):
        self.stage = stage
        self.original_error = original_error
        super().__init__(f"Generation failed at stage '{stage}': {original_error}")
```

### 9. Security Considerations

#### Secure Template Processing
```python
class SecurityValidator:
    """Validates templates for security vulnerabilities"""
    
    DANGEROUS_PATTERNS = [
        r'\$\{.*\}',  # Environment variable injection
        r'eval\(',     # Code evaluation
        r'exec\(',     # Code execution
    ]
    
    def validate_template_security(self, template_content: str) -> SecurityResult:
        """Scan template content for security vulnerabilities"""
        vulnerabilities = []
        
        for pattern in self.DANGEROUS_PATTERNS:
            if re.search(pattern, template_content):
                vulnerabilities.append(f"Dangerous pattern detected: {pattern}")
        
        return SecurityResult(
            is_secure=len(vulnerabilities) == 0,
            vulnerabilities=vulnerabilities
        )
```

### 10. Testing Strategy

#### Comprehensive Test Coverage
```python
class TestTemplateOrchestrator(unittest.TestCase):
    """Test suite for template orchestrator"""
    
    def setUp(self):
        self.orchestrator = TemplateOrchestrator()
        self.test_templates = self._create_test_templates()
    
    def test_dependency_resolution(self):
        """Test automatic dependency resolution"""
        selected = {"shadcn"}
        resolved = self.orchestrator.resolve_dependencies(selected)
        self.assertIn("nextjs", resolved.resolved_templates)
        self.assertIn("react", resolved.resolved_templates)
    
    def test_conflict_detection(self):
        """Test mutually exclusive template detection"""
        selected = {"tauri", "electron"}
        with self.assertRaises(DependencyConflictError):
            self.orchestrator.resolve_dependencies(selected)
    
    def test_project_generation(self):
        """Test complete project generation workflow"""
        templates = ["nextjs", "shadcn", "postgres"]
        project_path = self.orchestrator.generate_project("test-project", templates)
        
        # Verify generated files
        self.assertTrue((project_path / "docker-compose.yml").exists())
        self.assertTrue((project_path / ".env").exists())
        self.assertTrue((project_path / "frontend").exists())
```

This comprehensive PRP provides the complete context needed to implement a production-ready Project Template Orchestrator following SOLID principles and Context Engineering methodology. The implementation should handle all edge cases, provide excellent error handling, and maintain extensibility for future template additions.