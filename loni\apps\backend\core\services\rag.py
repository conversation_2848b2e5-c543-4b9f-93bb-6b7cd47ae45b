"""
RAG (Retrieval-Augmented Generation) service for document management and search.
"""
import hashlib
import uuid
from typing import List, Dict, Any, Optional
from pathlib import Path

from qdrant_client import QdrantClient
from qdrant_client.models import Distance, VectorParams, PointStruct, Filter, FieldCondition, MatchValue
from sqlalchemy.ext.asyncio import AsyncSession

from ..container import Container
from .base import BaseService


class RAGService(BaseService):
    """Service for RAG operations including document ingestion and retrieval."""
    
    def __init__(self, session: AsyncSession, container: Container):
        super().__init__(session, container)
        self.qdrant_client: QdrantClient = container.qdrant_client
        self.embedding_model = container.embedding_model
        
        # Collection names
        self.documents_collection = "documents"
        self.conversations_collection = "conversation_embeddings"
        
        # Initialize collections
        self._initialize_collections()
    
    def _initialize_collections(self):
        """Initialize Qdrant collections if they don't exist."""
        try:
            # Documents collection for RAG
            if not self.qdrant_client.collection_exists(self.documents_collection):
                self.qdrant_client.create_collection(
                    collection_name=self.documents_collection,
                    vectors_config=VectorParams(
                        size=1536,  # OpenAI text-embedding-ada-002 dimensions
                        distance=Distance.COSINE
                    )
                )
            
            # Conversation embeddings for semantic search
            if not self.qdrant_client.collection_exists(self.conversations_collection):
                self.qdrant_client.create_collection(
                    collection_name=self.conversations_collection,
                    vectors_config=VectorParams(
                        size=1536,
                        distance=Distance.COSINE
                    )
                )
        except Exception as e:
            print(f"Failed to initialize Qdrant collections: {e}")
    
    async def ingest_document(
        self,
        content: str,
        title: str,
        source: str,
        user_id: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> str:
        """Ingest a document into the RAG system."""
        try:
            # Generate embeddings for the content
            embeddings = await self._generate_embeddings(content)
            
            # Create document ID
            doc_id = str(uuid.uuid4())
            
            # Prepare metadata
            doc_metadata = {
                'title': title,
                'source': source,
                'content': content,
                'user_id': user_id,
                'content_hash': hashlib.sha256(content.encode()).hexdigest(),
                **(metadata or {})
            }
            
            # Create point for Qdrant
            point = PointStruct(
                id=doc_id,
                vector=embeddings,
                payload=doc_metadata
            )
            
            # Insert into Qdrant
            self.qdrant_client.upsert(
                collection_name=self.documents_collection,
                points=[point]
            )
            
            return doc_id
            
        except Exception as e:
            raise ValueError(f"Failed to ingest document: {str(e)}")
    
    async def ingest_file(
        self,
        file_path: Path,
        user_id: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> str:
        """Ingest a file into the RAG system."""
        try:
            # Read file content
            content = file_path.read_text(encoding='utf-8')
            
            # Extract title from filename
            title = file_path.stem
            
            return await self.ingest_document(
                content=content,
                title=title,
                source=str(file_path),
                user_id=user_id,
                metadata=metadata
            )
            
        except Exception as e:
            raise ValueError(f"Failed to ingest file {file_path}: {str(e)}")
    
    async def search(
        self,
        query: str,
        limit: int = 5,
        score_threshold: float = 0.7,
        user_id: Optional[str] = None,
        filters: Optional[Dict[str, Any]] = None
    ) -> List[Dict[str, Any]]:
        """Search for relevant documents using semantic similarity."""
        try:
            # Generate query embeddings
            query_embeddings = await self._generate_embeddings(query)
            
            # Prepare filters
            qdrant_filter = None
            if user_id or filters:
                conditions = []
                
                if user_id:
                    conditions.append(
                        FieldCondition(
                            key="user_id",
                            match=MatchValue(value=user_id)
                        )
                    )
                
                if filters:
                    for key, value in filters.items():
                        conditions.append(
                            FieldCondition(
                                key=key,
                                match=MatchValue(value=value)
                            )
                        )
                
                if conditions:
                    qdrant_filter = Filter(must=conditions)
            
            # Search in Qdrant
            search_results = self.qdrant_client.search(
                collection_name=self.documents_collection,
                query_vector=query_embeddings,
                limit=limit,
                score_threshold=score_threshold,
                query_filter=qdrant_filter,
                with_payload=True
            )
            
            # Format results
            results = []
            for result in search_results:
                results.append({
                    'id': result.id,
                    'score': result.score,
                    'title': result.payload.get('title', ''),
                    'content': result.payload.get('content', ''),
                    'source': result.payload.get('source', ''),
                    'metadata': {k: v for k, v in result.payload.items() 
                              if k not in ['title', 'content', 'source']}
                })
            
            return results
            
        except Exception as e:
            raise ValueError(f"Search failed: {str(e)}")
    
    async def get_document(self, doc_id: str) -> Optional[Dict[str, Any]]:
        """Get a specific document by ID."""
        try:
            points = self.qdrant_client.retrieve(
                collection_name=self.documents_collection,
                ids=[doc_id],
                with_payload=True
            )
            
            if not points:
                return None
            
            point = points[0]
            return {
                'id': point.id,
                'title': point.payload.get('title', ''),
                'content': point.payload.get('content', ''),
                'source': point.payload.get('source', ''),
                'metadata': {k: v for k, v in point.payload.items() 
                          if k not in ['title', 'content', 'source']}
            }
            
        except Exception as e:
            raise ValueError(f"Failed to get document {doc_id}: {str(e)}")
    
    async def delete_document(self, doc_id: str) -> bool:
        """Delete a document from the RAG system."""
        try:
            self.qdrant_client.delete(
                collection_name=self.documents_collection,
                points_selector=[doc_id]
            )
            return True
            
        except Exception as e:
            print(f"Failed to delete document {doc_id}: {e}")
            return False
    
    async def list_documents(
        self,
        user_id: Optional[str] = None,
        limit: int = 100,
        offset: int = 0
    ) -> List[Dict[str, Any]]:
        """List documents with optional user filtering."""
        try:
            # Prepare filter
            qdrant_filter = None
            if user_id:
                qdrant_filter = Filter(
                    must=[
                        FieldCondition(
                            key="user_id",
                            match=MatchValue(value=user_id)
                        )
                    ]
                )
            
            # Scroll through collection
            scroll_result = self.qdrant_client.scroll(
                collection_name=self.documents_collection,
                scroll_filter=qdrant_filter,
                limit=limit,
                offset=offset,
                with_payload=True
            )
            
            results = []
            for point in scroll_result[0]:  # scroll_result is (points, next_page_offset)
                results.append({
                    'id': point.id,
                    'title': point.payload.get('title', ''),
                    'source': point.payload.get('source', ''),
                    'metadata': {k: v for k, v in point.payload.items() 
                              if k not in ['title', 'content', 'source']}
                })
            
            return results
            
        except Exception as e:
            raise ValueError(f"Failed to list documents: {str(e)}")
    
    async def embed_conversation(
        self,
        conversation_id: str,
        messages: List[Dict[str, str]],
        user_id: str
    ) -> str:
        """Embed conversation for semantic search."""
        try:
            # Combine messages into searchable text
            content_parts = []
            for msg in messages:
                if msg.get('role') == 'user':
                    content_parts.append(f"User: {msg.get('content', '')}")
                elif msg.get('role') == 'assistant':
                    content_parts.append(f"Assistant: {msg.get('content', '')}")
            
            content = "\n".join(content_parts)
            
            # Generate embeddings
            embeddings = await self._generate_embeddings(content)
            
            # Create point
            point = PointStruct(
                id=conversation_id,
                vector=embeddings,
                payload={
                    'conversation_id': conversation_id,
                    'content': content,
                    'user_id': user_id,
                    'message_count': len(messages)
                }
            )
            
            # Insert into Qdrant
            self.qdrant_client.upsert(
                collection_name=self.conversations_collection,
                points=[point]
            )
            
            return conversation_id
            
        except Exception as e:
            raise ValueError(f"Failed to embed conversation: {str(e)}")
    
    async def search_conversations(
        self,
        query: str,
        user_id: str,
        limit: int = 10,
        score_threshold: float = 0.6
    ) -> List[Dict[str, Any]]:
        """Search conversations using semantic similarity."""
        try:
            # Generate query embeddings
            query_embeddings = await self._generate_embeddings(query)
            
            # Search with user filter
            search_results = self.qdrant_client.search(
                collection_name=self.conversations_collection,
                query_vector=query_embeddings,
                limit=limit,
                score_threshold=score_threshold,
                query_filter=Filter(
                    must=[
                        FieldCondition(
                            key="user_id",
                            match=MatchValue(value=user_id)
                        )
                    ]
                ),
                with_payload=True
            )
            
            # Format results
            results = []
            for result in search_results:
                results.append({
                    'conversation_id': result.payload.get('conversation_id'),
                    'score': result.score,
                    'message_count': result.payload.get('message_count', 0),
                    'preview': result.payload.get('content', '')[:200] + '...'
                })
            
            return results
            
        except Exception as e:
            raise ValueError(f"Conversation search failed: {str(e)}")
    
    async def _generate_embeddings(self, text: str) -> List[float]:
        """Generate embeddings for text using the configured model."""
        try:
            # Use the embedding model from container
            response = await self.embedding_model.create_embedding(text)
            return response.data[0].embedding
            
        except Exception as e:
            raise ValueError(f"Failed to generate embeddings: {str(e)}")
    
    async def get_collection_stats(self) -> Dict[str, Any]:
        """Get statistics about the RAG collections."""
        try:
            docs_info = self.qdrant_client.get_collection(self.documents_collection)
            convs_info = self.qdrant_client.get_collection(self.conversations_collection)
            
            return {
                'documents': {
                    'count': docs_info.points_count,
                    'status': docs_info.status.value,
                    'vectors_count': docs_info.vectors_count
                },
                'conversations': {
                    'count': convs_info.points_count,
                    'status': convs_info.status.value,
                    'vectors_count': convs_info.vectors_count
                }
            }
            
        except Exception as e:
            return {'error': str(e)}