"""
Streaming response service.

This service handles AI streaming responses
following the Single Responsibility Principle.
"""

from typing import AsyncGenerator, Dict, List, Optional
from uuid import UUID

from pydantic_ai import Agent
from loguru import logger

from .model_service import ModelService
from .rag_service import RAGService


class StreamingService:
    """Service for handling AI streaming responses."""
    
    def __init__(self, model_service: ModelService, rag_service: Optional[RAGService] = None):
        """
        Initialize the streaming service.
        
        Args:
            model_service: Service for model management
            rag_service: Service for RAG functionality (optional)
        """
        self.model_service = model_service
        self.rag_service = rag_service
        self._agents: Dict[str, Agent] = {}
        self._initialize_agents()
    
    def _initialize_agents(self) -> None:
        """Initialize streaming-capable agents."""
        # Streaming chat agent
        self._agents['stream_chat'] = Agent(
            model=self._get_model_client('openai'),
            system_prompt="""You are a helpful AI assistant. Provide responses in a 
            streaming fashion, ensuring each chunk is meaningful and builds upon the previous ones."""
        )
        
        # Streaming RAG agent
        self._agents['stream_rag'] = Agent(
            model=self._get_model_client('openai'),
            system_prompt="""You are an AI assistant with access to a knowledge base. 
            Stream your response while utilizing the provided context effectively."""
        )
    
    def _get_model_client(self, provider: str):
        """Get the appropriate model client for a provider."""
        # This would return the actual model client
        return f"{provider}_client"
    
    async def stream_response(
        self,
        messages: List[Dict[str, str]],
        model_id: str,
        rag_enabled: bool = False,
        conversation_id: Optional[UUID] = None,
        temperature: float = 0.7
    ) -> AsyncGenerator[Dict[str, any], None]:
        """
        Stream AI response for chat messages.
        
        Args:
            messages: List of conversation messages
            model_id: ID of the model to use
            rag_enabled: Whether to use RAG context
            conversation_id: ID of the conversation (for RAG context)
            temperature: Response randomness (0.0 to 1.0)
            
        Yields:
            Dictionary containing stream chunks with metadata
            
        Raises:
            ValueError: If streaming fails
        """
        try:
            # Validate model supports streaming
            model_info = await self.model_service.get_model_by_id(model_id)
            if not model_info:
                raise ValueError(f"Model {model_id} not found")
            
            if not model_info.get('supports_streaming', False):
                raise ValueError(f"Model {model_id} does not support streaming")
            
            # Select appropriate agent
            agent_type = 'stream_rag' if rag_enabled else 'stream_chat'
            agent = self._agents.get(agent_type, self._agents['stream_chat'])
            
            # Get RAG context if enabled
            rag_context = None
            if rag_enabled and self.rag_service and conversation_id:
                current_message = messages[-1]['content'] if messages else ""
                rag_context = await self.rag_service.get_context(
                    query=current_message,
                    conversation_id=conversation_id
                )
            
            # Prepare prompt
            prompt = self._prepare_streaming_prompt(messages, rag_context)
            
            # Stream response
            full_response = ""
            chunk_count = 0
            
            async with agent.run_stream(prompt) as response:
                async for chunk in response:
                    chunk_count += 1
                    full_response += chunk
                    
                    # Yield structured chunk data
                    yield {
                        'type': 'content',
                        'content': chunk,
                        'chunk_id': chunk_count,
                        'model_id': model_id,
                        'finished': False
                    }
            
            # Send final completion message
            yield {
                'type': 'completion',
                'content': '',
                'chunk_id': chunk_count + 1,
                'model_id': model_id,
                'finished': True,
                'full_response': full_response,
                'total_chunks': chunk_count
            }
            
        except Exception as e:
            logger.error(f"Streaming failed: {e}")
            # Yield error message
            yield {
                'type': 'error',
                'content': '',
                'error': str(e),
                'finished': True
            }
    
    def _prepare_streaming_prompt(
        self, 
        messages: List[Dict[str, str]], 
        rag_context: Optional[str] = None
    ) -> str:
        """
        Prepare the prompt optimized for streaming.
        
        Args:
            messages: Conversation messages
            rag_context: RAG context if available
            
        Returns:
            Formatted prompt for streaming
        """
        if not messages:
            return ""
        
        current_message = messages[-1]['content']
        
        if rag_context:
            return f"""Context from knowledge base:
{rag_context}

Conversation history:
{self._format_conversation_history(messages[:-1])}

Current question: {current_message}

Please provide a comprehensive response based on the context and conversation history. 
Stream your response naturally, building your answer progressively."""
        else:
            return f"""Conversation history:
{self._format_conversation_history(messages[:-1])}

Current message: {current_message}

Please provide a thoughtful response, streaming it naturally."""
    
    def _format_conversation_history(self, messages: List[Dict[str, str]]) -> str:
        """
        Format conversation history for streaming prompts.
        
        Args:
            messages: List of messages
            
        Returns:
            Formatted conversation history
        """
        if not messages:
            return "No previous conversation."
        
        formatted = []
        for msg in messages[-5:]:  # Only use last 5 messages for context
            role = msg['role'].title()
            content = msg['content']
            formatted.append(f"{role}: {content}")
        
        return "\n".join(formatted)
    
    async def stream_with_function_calling(
        self,
        messages: List[Dict[str, str]],
        model_id: str,
        available_functions: List[Dict[str, any]],
        conversation_id: Optional[UUID] = None
    ) -> AsyncGenerator[Dict[str, any], None]:
        """
        Stream response with function calling capabilities.
        
        Args:
            messages: List of conversation messages
            model_id: ID of the model to use
            available_functions: List of available functions
            conversation_id: ID of the conversation
            
        Yields:
            Dictionary containing stream chunks with function calls
        """
        try:
            # Validate model supports function calling
            model_info = await self.model_service.get_model_by_id(model_id)
            if not model_info or 'function_calling' not in model_info.get('capabilities', []):
                raise ValueError(f"Model {model_id} does not support function calling")
            
            # Create function-calling enabled agent
            agent = Agent(
                model=self._get_model_client(model_info['provider']),
                system_prompt="""You are an AI assistant with access to tools and functions. 
                Use the available functions when appropriate to provide accurate and helpful responses."""
            )
            
            # Add functions to agent (this would be implemented based on Pydantic AI API)
            # agent.add_functions(available_functions)
            
            prompt = self._prepare_streaming_prompt(messages)
            
            # Stream with function calling
            async with agent.run_stream(prompt) as response:
                async for chunk in response:
                    # Handle different chunk types (content, function_call, etc.)
                    if hasattr(chunk, 'function_call'):
                        yield {
                            'type': 'function_call',
                            'function_name': chunk.function_call.name,
                            'function_args': chunk.function_call.arguments,
                            'finished': False
                        }
                    else:
                        yield {
                            'type': 'content',
                            'content': str(chunk),
                            'finished': False
                        }
            
            yield {
                'type': 'completion',
                'finished': True
            }
            
        except Exception as e:
            logger.error(f"Function calling stream failed: {e}")
            yield {
                'type': 'error',
                'error': str(e),
                'finished': True
            }
    
    def validate_streaming_model(self, model_id: str) -> bool:
        """
        Validate if a model supports streaming.
        
        Args:
            model_id: ID of the model to check
            
        Returns:
            True if model supports streaming
        """
        # This would be async in real implementation
        try:
            models = self.model_service._build_models_list()
            model = next((m for m in models if m['id'] == model_id), None)
            return model is not None and model.get('supports_streaming', False)
        except Exception:
            return False