"""
Document model for RAG functionality.

This module provides the Document model for managing uploaded documents
and their processing status for RAG operations.
"""

from datetime import datetime
from enum import Enum
from typing import Optional
from uuid import UUID

from sqlalchemy import Boolean, DateTime, Foreign<PERSON>ey, Integer, String, Text
from sqlalchemy.dialects.postgresql import J<PERSON><PERSON>, UUID as PGUUID
from sqlalchemy.orm import Mapped, mapped_column, relationship

from .base import SoftDeleteModel


class ProcessingStatus(str, Enum):
    """Document processing status enumeration."""
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class DocumentType(str, Enum):
    """Document type enumeration."""
    TEXT = "text"
    PDF = "pdf"
    DOCX = "docx"
    MARKDOWN = "markdown"
    HTML = "html"
    CODE = "code"
    CSV = "csv"
    JSON = "json"


class Document(SoftDeleteModel):
    """
    Document model for RAG system.
    
    Represents uploaded documents that can be processed for
    retrieval-augmented generation.
    """
    
    __tablename__ = "documents"
    
    # Basic information
    title: Mapped[str] = mapped_column(
        String(255),
        nullable=False,
        index=True
    )
    
    content: Mapped[str] = mapped_column(
        Text,
        nullable=False
    )
    
    content_type: Mapped[Optional[str]] = mapped_column(
        String(100),
        nullable=True,
        index=True
    )
    
    source_url: Mapped[Optional[str]] = mapped_column(
        String(500),
        nullable=True
    )
    
    # User relationship
    user_id: Mapped[UUID] = mapped_column(
        PGUUID(as_uuid=True),
        ForeignKey("user.id", ondelete="CASCADE"),
        nullable=False,
        index=True
    )
    
    # File information
    file_size: Mapped[Optional[int]] = mapped_column(
        Integer,
        nullable=True
    )
    
    file_hash: Mapped[Optional[str]] = mapped_column(
        String(64),
        nullable=True
    )
    
    # Processing information
    processing_status: Mapped[ProcessingStatus] = mapped_column(
        String(20),
        default=ProcessingStatus.PENDING,
        nullable=False,
        index=True
    )
    
    chunk_count: Mapped[int] = mapped_column(
        Integer,
        default=0,
        nullable=False
    )
    
    # Metadata
    metadata: Mapped[dict] = mapped_column(
        JSON,
        default=dict,
        nullable=False
    )
    
    # Relationships
    user: Mapped["User"] = relationship(
        "User",
        back_populates="documents"
    )
    
    def mark_processing(self) -> None:
        """Mark document as being processed."""
        self.processing_status = ProcessingStatus.PROCESSING
        self.updated_at = datetime.utcnow()
    
    def mark_completed(self, chunk_count: int = 0) -> None:
        """Mark document processing as completed."""
        self.processing_status = ProcessingStatus.COMPLETED
        self.chunk_count = chunk_count
        self.updated_at = datetime.utcnow()
    
    def mark_failed(self, error_message: str = "") -> None:
        """Mark document processing as failed."""
        self.processing_status = ProcessingStatus.FAILED
        if error_message:
            self.metadata["error_message"] = error_message
        self.updated_at = datetime.utcnow()
    
    def mark_cancelled(self) -> None:
        """Mark document processing as cancelled."""
        self.processing_status = ProcessingStatus.CANCELLED
        self.updated_at = datetime.utcnow()
    
    @property
    def is_processed(self) -> bool:
        """Check if document is successfully processed."""
        return self.processing_status == ProcessingStatus.COMPLETED
    
    @property
    def is_processing(self) -> bool:
        """Check if document is currently being processed."""
        return self.processing_status == ProcessingStatus.PROCESSING
    
    @property
    def has_failed(self) -> bool:
        """Check if document processing failed."""
        return self.processing_status == ProcessingStatus.FAILED
    
    @property
    def content_length(self) -> int:
        """Get content length."""
        return len(self.content)
    
    @property
    def file_size_mb(self) -> Optional[float]:
        """Get file size in MB."""
        if self.file_size:
            return round(self.file_size / (1024 * 1024), 2)
        return None
    
    @property
    def document_type(self) -> DocumentType:
        """Infer document type from content type."""
        if not self.content_type:
            return DocumentType.TEXT
        
        content_type_lower = self.content_type.lower()
        
        if "pdf" in content_type_lower:
            return DocumentType.PDF
        elif "word" in content_type_lower or "docx" in content_type_lower:
            return DocumentType.DOCX
        elif "markdown" in content_type_lower or "md" in content_type_lower:
            return DocumentType.MARKDOWN
        elif "html" in content_type_lower:
            return DocumentType.HTML
        elif "csv" in content_type_lower:
            return DocumentType.CSV
        elif "json" in content_type_lower:
            return DocumentType.JSON
        elif any(lang in content_type_lower for lang in ["python", "javascript", "java", "cpp", "c++"]):
            return DocumentType.CODE
        else:
            return DocumentType.TEXT
    
    def get_processing_error(self) -> Optional[str]:
        """Get processing error message if any."""
        return self.metadata.get("error_message")
    
    def add_tag(self, tag: str) -> None:
        """Add a tag to the document."""
        tags = self.metadata.get("tags", [])
        if tag not in tags:
            tags.append(tag)
            self.metadata["tags"] = tags
            self.updated_at = datetime.utcnow()
    
    def remove_tag(self, tag: str) -> None:
        """Remove a tag from the document."""
        tags = self.metadata.get("tags", [])
        if tag in tags:
            tags.remove(tag)
            self.metadata["tags"] = tags
            self.updated_at = datetime.utcnow()
    
    def get_tags(self) -> list:
        """Get document tags."""
        return self.metadata.get("tags", [])
    
    def to_dict(self) -> dict:
        """Convert document to dictionary."""
        return {
            "id": str(self.id),
            "title": self.title,
            "content_type": self.content_type,
            "source_url": self.source_url,
            "user_id": str(self.user_id),
            "file_size": self.file_size,
            "file_size_mb": self.file_size_mb,
            "file_hash": self.file_hash,
            "processing_status": self.processing_status,
            "chunk_count": self.chunk_count,
            "content_length": self.content_length,
            "document_type": self.document_type,
            "metadata": self.metadata,
            "tags": self.get_tags(),
            "is_processed": self.is_processed,
            "is_processing": self.is_processing,
            "has_failed": self.has_failed,
            "processing_error": self.get_processing_error(),
            "is_deleted": self.is_deleted,
            "created_at": self.created_at.isoformat(),
            "updated_at": self.updated_at.isoformat(),
        }
