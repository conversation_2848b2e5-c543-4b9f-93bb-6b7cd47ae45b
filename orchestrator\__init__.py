"""
Project Template Orchestrator

A sophisticated system for generating Docker-based project environments
with intelligent dependency management and conflict prevention.
"""

__version__ = "1.0.0"
__author__ = "Lonors Project"
__description__ = "Project Template Orchestrator with SOLID architecture"

from .core.models import TemplateMetadata, ValidationResult, DependencyResult
from .core.services import TemplateOrchestrator, ProjectGenerator
from .core.validators import DependencyValidator, ConfigurationValidator

__all__ = [
    "TemplateMetadata",
    "ValidationResult", 
    "DependencyResult",
    "TemplateOrchestrator",
    "ProjectGenerator",
    "DependencyValidator",
    "ConfigurationValidator"
]