"""
Docker Compose file generator.

Generates docker-compose.yml files by merging service definitions
from selected templates while handling conflicts and dependencies.
"""

import yaml
from typing import Dict, Set, Any, List, Optional
from pathlib import Path
from collections import defaultdict

from ..core.interfaces import IDocker<PERSON>omposeGenerator, ITemplateRegistry
from ..core.models import ProjectConfig, GenerationResult, ValidationResult


class DockerComposeGenerator(IDockerComposeGenerator):
    """
    Generator for Docker Compose files with intelligent service merging.
    
    Handles service conflicts, network configuration, volume management,
    and environment variable substitution.
    """
    
    def __init__(self, template_registry: ITemplateRegistry):
        """Initialize with template registry."""
        self.template_registry = template_registry
        
        # Default Docker Compose version and configuration
        self.compose_version = "3.8"
        self.default_networks = {
            "default": {
                "name": "orchestrator_network",
                "driver": "bridge"
            }
        }
    
    def generate(self, project_path: Path, config: ProjectConfig) -> GenerationResult:
        """Generate Docker Compose file for the project."""
        result = GenerationResult(
            success=True,
            project_path=str(project_path)
        )
        
        try:
            # Collect all Docker services from selected templates
            all_services = self._collect_services(config.selected_templates)
            
            # Merge service definitions
            merged_services = self.merge_service_definitions(all_services)
            
            # Generate compose content
            compose_content = self.generate_compose_file(merged_services)
            
            # Write docker-compose.yml
            compose_file = project_path / "docker-compose.yml"
            with open(compose_file, 'w') as f:
                f.write(compose_content)
            
            result.add_generated_file(
                path="docker-compose.yml",
                generated_by="DockerComposeGenerator"
            )
            
            # Generate override files if needed
            if self._should_generate_overrides(config):
                override_content = self._generate_override_file(merged_services, config)
                override_file = project_path / "docker-compose.override.yml"
                with open(override_file, 'w') as f:
                    f.write(override_content)
                
                result.add_generated_file(
                    path="docker-compose.override.yml",
                    generated_by="DockerComposeGenerator"
                )
            
            # Add next steps
            result.next_steps.extend([
                "Review generated docker-compose.yml",
                "Configure environment variables in .env file",
                "Run 'docker-compose up -d' to start services"
            ])
            
        except Exception as e:
            result.success = False
            result.errors.append(f"Docker Compose generation failed: {str(e)}")
        
        return result
    
    def can_generate(self, templates: Set[str]) -> bool:
        """Check if any templates have Docker services."""
        for template_id in templates:
            template = self.template_registry.get_template(template_id)
            if template:
                metadata = template.get_metadata()
                if metadata.docker_services:
                    return True
        return False
    
    def generate_compose_file(self, services: Dict[str, Dict[str, Any]]) -> str:
        """Generate Docker Compose YAML content."""
        # Collect all volumes from services
        volumes = self._extract_volumes(services)
        
        # Build compose structure
        compose_data = {
            "version": self.compose_version,
            "services": services,
            "volumes": volumes,
            "networks": self.default_networks
        }
        
        # Generate YAML with proper formatting
        yaml_content = yaml.dump(
            compose_data,
            default_flow_style=False,
            sort_keys=False,
            indent=2,
            width=1000
        )
        
        # Add header comment
        header = f"""# Generated by Project Template Orchestrator
# This Docker Compose file contains services for your selected templates
# 
# To start all services:
#   docker-compose up -d
# 
# To stop all services:
#   docker-compose down
# 
# To view logs:
#   docker-compose logs -f

"""
        
        return header + yaml_content
    
    def merge_service_definitions(self, services: List[Dict[str, Any]]) -> Dict[str, Dict[str, Any]]:
        """Merge multiple service definitions handling conflicts."""
        merged = {}
        port_usage = defaultdict(list)  # Track port conflicts
        
        for service_dict in services:
            for service_name, service_config in service_dict.items():
                if service_name in merged:
                    # Merge with existing service
                    merged[service_name] = self._merge_single_service(
                        merged[service_name], 
                        service_config,
                        service_name
                    )
                else:
                    # Add new service
                    merged[service_name] = service_config.copy()
                
                # Track port usage for conflict detection
                if 'ports' in service_config:
                    for port_mapping in service_config['ports']:
                        external_port = port_mapping.split(':')[0]
                        port_usage[external_port].append(service_name)
        
        # Handle port conflicts
        self._resolve_port_conflicts(merged, port_usage)
        
        return merged
    
    def _collect_services(self, template_ids: Set[str]) -> List[Dict[str, Any]]:
        """Collect Docker services from all selected templates."""
        services = []
        
        for template_id in template_ids:
            template = self.template_registry.get_template(template_id)
            if template:
                metadata = template.get_metadata()
                if metadata.docker_services:
                    services.append(metadata.docker_services)
        
        return services
    
    def _merge_single_service(self, existing: Dict[str, Any], new: Dict[str, Any], service_name: str) -> Dict[str, Any]:
        """Merge two service configurations."""
        merged = existing.copy()
        
        for key, value in new.items():
            if key not in merged:
                merged[key] = value
            elif key == 'environment':
                # Merge environment variables
                if isinstance(merged[key], dict) and isinstance(value, dict):
                    merged[key].update(value)
                elif isinstance(merged[key], list) and isinstance(value, list):
                    merged[key].extend(value)
            elif key == 'volumes':
                # Merge volume mounts
                if isinstance(merged[key], list) and isinstance(value, list):
                    merged[key].extend(value)
                    # Remove duplicates while preserving order
                    merged[key] = list(dict.fromkeys(merged[key]))
            elif key == 'ports':
                # Handle port conflicts
                if isinstance(merged[key], list) and isinstance(value, list):
                    merged[key].extend(value)
            elif key == 'depends_on':
                # Merge service dependencies
                if isinstance(merged[key], list) and isinstance(value, list):
                    merged[key].extend(value)
                    merged[key] = list(set(merged[key]))  # Remove duplicates
            else:
                # For other keys, new value takes precedence
                merged[key] = value
        
        return merged
    
    def _resolve_port_conflicts(self, services: Dict[str, Dict[str, Any]], port_usage: Dict[str, List[str]]):
        """Resolve port conflicts by incrementing conflicting ports."""
        for external_port, service_names in port_usage.items():
            if len(service_names) > 1:
                # Multiple services want the same external port
                for i, service_name in enumerate(service_names[1:], 1):  # Skip first service
                    service = services[service_name]
                    if 'ports' in service:
                        new_ports = []
                        for port_mapping in service['ports']:
                            if port_mapping.startswith(f"{external_port}:"):
                                # Find available port
                                new_external_port = self._find_available_port(
                                    int(external_port) + i * 1000,
                                    port_usage
                                )
                                internal_port = port_mapping.split(':')[1]
                                new_mapping = f"{new_external_port}:{internal_port}"
                                new_ports.append(new_mapping)
                                
                                # Update port usage tracking
                                port_usage[str(new_external_port)] = [service_name]
                            else:
                                new_ports.append(port_mapping)
                        
                        service['ports'] = new_ports
    
    def _find_available_port(self, start_port: int, port_usage: Dict[str, List[str]]) -> int:
        """Find an available port starting from start_port."""
        port = start_port
        while str(port) in port_usage:
            port += 1
        return port
    
    def _extract_volumes(self, services: Dict[str, Dict[str, Any]]) -> Dict[str, Optional[Dict[str, Any]]]:
        """Extract volume definitions from services."""
        volumes = {}
        
        for service_name, service_config in services.items():
            if 'volumes' in service_config:
                for volume_def in service_config['volumes']:
                    if ':' in volume_def:
                        # Split volume definition
                        parts = volume_def.split(':')
                        if len(parts) >= 2:
                            volume_name = parts[0]
                            # Only add named volumes (not host paths)
                            if not volume_name.startswith('/') and not volume_name.startswith('./'):
                                volumes[volume_name] = None  # External volume
        
        return volumes
    
    def _should_generate_overrides(self, config: ProjectConfig) -> bool:
        """Determine if override file should be generated."""
        # Generate override for development-specific configurations
        return config.custom_configuration.get('environment', 'development') == 'development'
    
    def _generate_override_file(self, services: Dict[str, Dict[str, Any]], config: ProjectConfig) -> str:
        """Generate docker-compose.override.yml for development."""
        override_services = {}
        
        # Add development-specific configurations
        for service_name, service_config in services.items():
            override_config = {}
            
            # Add volume mounts for development
            if 'volumes' in service_config:
                override_config['volumes'] = service_config['volumes'].copy()
                # Add source code mounting for development
                if any('app' in vol for vol in service_config.get('volumes', [])):
                    override_config['volumes'].append('./src:/app/src')
            
            # Add development environment variables
            if service_name in ['frontend', 'backend', 'api']:
                override_config['environment'] = {
                    'NODE_ENV': 'development',
                    'DEBUG': 'true',
                    'HOT_RELOAD': 'true'
                }
            
            if override_config:
                override_services[service_name] = override_config
        
        override_data = {
            "version": self.compose_version,
            "services": override_services
        }
        
        header = """# Development override for Docker Compose
# This file is automatically loaded in development environments
# 
# Contains development-specific configurations like:
# - Source code volume mounts for hot reloading
# - Debug environment variables
# - Development-specific service configurations

"""
        
        yaml_content = yaml.dump(
            override_data,
            default_flow_style=False,
            sort_keys=False,
            indent=2
        )
        
        return header + yaml_content
    
    def validate_services(self, services: Dict[str, Dict[str, Any]]) -> ValidationResult:
        """Validate Docker service definitions."""
        result = ValidationResult(is_valid=True)
        
        for service_name, service_config in services.items():
            # Validate required fields
            if 'image' not in service_config:
                result.add_error(f"Service '{service_name}' missing required 'image' field")
            
            # Validate port formats
            if 'ports' in service_config:
                for port in service_config['ports']:
                    if not self._is_valid_port_mapping(port):
                        result.add_error(f"Invalid port mapping '{port}' in service '{service_name}'")
            
            # Validate volume formats
            if 'volumes' in service_config:
                for volume in service_config['volumes']:
                    if not self._is_valid_volume_mapping(volume):
                        result.add_warning(f"Potentially invalid volume mapping '{volume}' in service '{service_name}'")
        
        return result
    
    def _is_valid_port_mapping(self, port_mapping: str) -> bool:
        """Validate port mapping format."""
        if ':' not in port_mapping:
            return False
        
        parts = port_mapping.split(':')
        if len(parts) != 2:
            return False
        
        try:
            external_port = int(parts[0])
            internal_port = int(parts[1])
            return 1 <= external_port <= 65535 and 1 <= internal_port <= 65535
        except ValueError:
            return False
    
    def _is_valid_volume_mapping(self, volume_mapping: str) -> bool:
        """Validate volume mapping format."""
        if ':' not in volume_mapping:
            return True  # Named volume
        
        parts = volume_mapping.split(':')
        return len(parts) >= 2