"""
Conversation service for chat-related business logic.
"""
from typing import Optional, List, Dict, Any
from uuid import UUID

from sqlalchemy.ext.asyncio import AsyncSession

from ..container import Container
from ..models.conversation import Conversation, Message
from ..repositories.conversation import ConversationRepository, MessageRepository
from .base import BaseService


class ConversationService(BaseService):
    """Service for conversation-related business operations."""
    
    def __init__(self, session: AsyncSession, container: Container):
        super().__init__(session, container)
        self.conversation_repo = ConversationRepository(session)
        self.message_repo = MessageRepository(session)
    
    async def create_conversation(
        self,
        user_id: UUID,
        title: str = "New Conversation",
        model_name: str = "gpt-4",
        rag_enabled: bool = True,
        temperature: float = 0.7
    ) -> Conversation:
        """Create a new conversation."""
        return await self.conversation_repo.create(
            user_id=user_id,
            title=title,
            model_name=model_name,
            rag_enabled=rag_enabled,
            temperature=temperature
        )
    
    async def get_conversation(
        self, 
        conversation_id: UUID, 
        user_id: Optional[UUID] = None
    ) -> Optional[Conversation]:
        """Get conversation by ID with optional user validation."""
        conversation = await self.conversation_repo.get_by_id(conversation_id)
        
        if conversation and user_id and conversation.user_id != user_id:
            return None  # User doesn't own this conversation
        
        return conversation
    
    async def get_conversation_with_messages(
        self,
        conversation_id: UUID,
        user_id: Optional[UUID] = None
    ) -> Optional[Conversation]:
        """Get conversation with all messages loaded."""
        conversation = await self.conversation_repo.get_with_messages(conversation_id)
        
        if conversation and user_id and conversation.user_id != user_id:
            return None  # User doesn't own this conversation
        
        return conversation
    
    async def list_user_conversations(
        self,
        user_id: UUID,
        limit: int = 50,
        offset: int = 0
    ) -> List[Conversation]:
        """List conversations for a user."""
        return await self.conversation_repo.get_user_conversations(
            user_id=user_id,
            limit=limit,
            offset=offset
        )
    
    async def update_conversation_title(
        self,
        conversation_id: UUID,
        title: str,
        user_id: Optional[UUID] = None
    ) -> Optional[Conversation]:
        """Update conversation title."""
        # Validate ownership if user_id provided
        if user_id:
            conversation = await self.get_conversation(conversation_id, user_id)
            if not conversation:
                return None
        
        return await self.conversation_repo.update_title(conversation_id, title)
    
    async def update_conversation_config(
        self,
        conversation_id: UUID,
        model_name: Optional[str] = None,
        rag_enabled: Optional[bool] = None,
        temperature: Optional[float] = None,
        user_id: Optional[UUID] = None
    ) -> Optional[Conversation]:
        """Update conversation AI model configuration."""
        # Validate ownership if user_id provided
        if user_id:
            conversation = await self.get_conversation(conversation_id, user_id)
            if not conversation:
                return None
        
        # Get current conversation to preserve existing values
        conversation = await self.conversation_repo.get_by_id(conversation_id)
        if not conversation:
            return None
        
        return await self.conversation_repo.update_model_config(
            conversation_id=conversation_id,
            model_name=model_name or conversation.model_name,
            rag_enabled=rag_enabled if rag_enabled is not None else conversation.rag_enabled,
            temperature=temperature if temperature is not None else conversation.temperature
        )
    
    async def delete_conversation(
        self,
        conversation_id: UUID,
        user_id: Optional[UUID] = None,
        soft_delete: bool = True
    ) -> bool:
        """Delete conversation."""
        # Validate ownership if user_id provided
        if user_id:
            conversation = await self.get_conversation(conversation_id, user_id)
            if not conversation:
                return False
        
        if soft_delete:
            return await self.conversation_repo.soft_delete(conversation_id)
        else:
            return await self.conversation_repo.delete(conversation_id)
    
    async def add_message(
        self,
        conversation_id: UUID,
        role: str,
        content: str,
        model_name: Optional[str] = None,
        tokens_used: int = 0
    ) -> Message:
        """Add a single message to conversation."""
        return await self.message_repo.create(
            conversation_id=conversation_id,
            role=role,
            content=content,
            model_name=model_name,
            tokens_used=tokens_used
        )
    
    async def add_message_pair(
        self,
        conversation_id: UUID,
        user_content: str,
        assistant_content: str,
        model_name: str,
        tokens_used: int = 0
    ) -> tuple[Message, Message]:
        """Add both user and assistant messages atomically."""
        return await self.message_repo.create_message_pair(
            conversation_id=conversation_id,
            user_content=user_content,
            assistant_content=assistant_content,
            model_name=model_name,
            tokens_used=tokens_used
        )
    
    async def get_conversation_messages(
        self,
        conversation_id: UUID,
        limit: int = 100,
        offset: int = 0,
        user_id: Optional[UUID] = None
    ) -> List[Message]:
        """Get messages for a conversation."""
        # Validate ownership if user_id provided
        if user_id:
            conversation = await self.get_conversation(conversation_id, user_id)
            if not conversation:
                return []
        
        return await self.message_repo.get_conversation_messages(
            conversation_id=conversation_id,
            limit=limit,
            offset=offset
        )
    
    async def get_conversation_context(
        self,
        conversation_id: UUID,
        message_count: int = 10,
        user_id: Optional[UUID] = None
    ) -> List[Message]:
        """Get recent messages for conversation context."""
        # Validate ownership if user_id provided
        if user_id:
            conversation = await self.get_conversation(conversation_id, user_id)
            if not conversation:
                return []
        
        return await self.message_repo.get_latest_messages(
            conversation_id=conversation_id,
            count=message_count
        )
    
    async def get_conversation_stats(self, conversation_id: UUID) -> Dict[str, Any]:
        """Get conversation statistics."""
        conversation = await self.conversation_repo.get_by_id(conversation_id)
        if not conversation:
            raise ValueError(f"Conversation {conversation_id} not found")
        
        messages = await self.message_repo.get_conversation_messages(conversation_id)
        
        total_tokens = sum(msg.tokens_used or 0 for msg in messages)
        user_messages = [msg for msg in messages if msg.role == "user"]
        assistant_messages = [msg for msg in messages if msg.role == "assistant"]
        
        return {
            'conversation_id': conversation_id,
            'title': conversation.title,
            'model_name': conversation.model_name,
            'total_messages': len(messages),
            'user_messages': len(user_messages),
            'assistant_messages': len(assistant_messages),
            'total_tokens': total_tokens,
            'created_at': conversation.created_at,
            'updated_at': conversation.updated_at,
            'rag_enabled': conversation.rag_enabled,
            'temperature': conversation.temperature
        }