{"name": "Agent API Endpoint", "nodes": [{"parameters": {"httpMethod": "POST", "path": "api-agent", "responseMode": "responseNode", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [-1140, 0], "id": "d703b7fa-d076-47e5-924c-ef529886d2a2", "name": "Webhook", "webhookId": "a68629d6-b7de-461e-8cac-68112ae5bb4b"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "201ba6c4-9be3-4ae1-b7ca-9321c0542549", "leftValue": "={{ $('Webhook').item.json.body.session_id }}", "rightValue": "", "operator": {"type": "string", "operation": "empty", "singleValue": true}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [260, 0], "id": "819e6d0c-aaaa-46d8-9cf8-57d414dc3288", "name": "If"}, {"parameters": {"tableId": "conversations", "fieldsUi": {"fieldValues": [{"fieldId": "user_id", "fieldValue": "={{ $('Webhook').item.json.body.user_id }}"}, {"fieldId": "session_id", "fieldValue": "={{ $json.session_id }}"}]}}, "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [700, -140], "id": "9ea658e8-5e8b-46e3-a60f-a61c5fb6afdb", "name": "Supabase1", "credentials": {"supabaseApi": {"id": "YRyunbk1wZUl88Td", "name": "Supabase Self Hosted"}}}, {"parameters": {"promptType": "define", "text": "={{ $('Webhook').item.json.body.query }}", "options": {}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.9, "position": [980, 260], "id": "c46359a6-5375-4974-9548-c9f261934377", "name": "AI Agent"}, {"parameters": {"model": {"__rl": true, "value": "gpt-4.1-mini", "mode": "list", "cachedResultName": "gpt-4.1-mini"}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1.2, "position": [860, 520], "id": "6c325eea-e15c-46da-8257-76e53b0272fc", "name": "OpenAI Chat Model", "credentials": {"openAiApi": {"id": "KnSSZQEFv82oFpIv", "name": "OpenAi account"}}}, {"parameters": {"sessionIdType": "customKey", "sessionKey": "={{ $('Webhook').item.json.body.session_id || $json.session_id }}", "tableName": "messages"}, "type": "@n8n/n8n-nodes-langchain.memoryPostgresChat", "typeVersion": 1.3, "position": [1040, 520], "id": "bd347cdc-870a-48a6-836f-33106afff885", "name": "Postgres Chat Memory", "credentials": {"postgres": {"id": "ezhtjmm7WxmXJLU8", "name": "Self Hosted Supabase"}}}, {"parameters": {"promptType": "define", "text": "=Based on the user message below, create a 4-6 word sentence for the conversation description since this is the first message in the description.\n\n{{ $('Webhook').item.json.body.query }}"}, "type": "@n8n/n8n-nodes-langchain.chainLlm", "typeVersion": 1.6, "position": [960, -140], "id": "f8262c26-f331-49d5-b5a9-************", "name": "Basic LLM Chain"}, {"parameters": {"model": {"__rl": true, "value": "gpt-4.1-nano", "mode": "list", "cachedResultName": "gpt-4.1-nano"}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1.2, "position": [1020, 40], "id": "b0905202-f1eb-457c-bcef-e03cc937f7fa", "name": "OpenAI Chat Model1", "credentials": {"openAiApi": {"id": "KnSSZQEFv82oFpIv", "name": "OpenAi account"}}}, {"parameters": {"numberInputs": 3}, "type": "n8n-nodes-base.merge", "typeVersion": 3.1, "position": [1540, 20], "id": "17395ee4-f568-43c2-b096-************", "name": "<PERSON><PERSON>"}, {"parameters": {"fieldsToAggregate": {"fieldToAggregate": [{"fieldToAggregate": "output"}, {"fieldToAggregate": "title", "renameField": true, "outputFieldName": "conversation_title"}, {"fieldToAggregate": "session_id"}]}, "options": {}}, "type": "n8n-nodes-base.aggregate", "typeVersion": 1, "position": [1860, 20], "id": "c10899cb-bd8f-420b-ae84-c75a92df2ff9", "name": "Aggregate"}, {"parameters": {"respondWith": "allIncomingItems", "options": {}}, "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1.1, "position": [2340, 20], "id": "98c96ea9-64e0-402f-b664-07f74c8aa2aa", "name": "Respond to Webhook"}, {"parameters": {"assignments": {"assignments": [{"id": "57be6196-201b-43fa-8c37-06fb9f53f7c8", "name": "session_id", "value": "={{ $('Webhook').item.json.body.user_id }}~{{ [...Array(10)].map(() => Math.random().toString(36).charAt(2)).join('') }}", "type": "string"}]}, "includeOtherFields": true, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [480, -140], "id": "2cc5535d-533c-4c9a-a224-0c678f1ff3dd", "name": "<PERSON>"}, {"parameters": {"assignments": {"assignments": [{"id": "c05ad106-f118-4d0d-a361-724945332d6f", "name": "output", "value": "={{ $json.output[0] }}", "type": "string"}, {"id": "cb080a99-02ae-43c2-a5b5-25ac3617c523", "name": "conversation_title", "value": "={{ $json.conversation_title[0] }}", "type": "string"}, {"id": "8bb827bb-1b97-4e3b-be3b-da37ede6b052", "name": "session_id", "value": "={{ $json.session_id[0] }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [2100, 20], "id": "abe409cb-582f-43a3-8608-0c7110fe390c", "name": "Edit Fields1"}, {"parameters": {"operation": "update", "tableId": "conversations", "filters": {"conditions": [{"keyName": "session_id", "condition": "eq", "keyValue": "={{ $('Edit Fields').item.json.session_id }}"}]}, "fieldsUi": {"fieldValues": [{"fieldId": "title", "fieldValue": "={{ $json.text }}"}]}}, "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [1320, -140], "id": "1cda6f1e-641c-450a-b4a6-ba39f0f372ff", "name": "Supabase", "credentials": {"supabaseApi": {"id": "YRyunbk1wZUl88Td", "name": "Supabase Self Hosted"}}}, {"parameters": {"url": "https://your-supabase-url/auth/v1/user", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Authorization", "value": "={{ $json.headers.authorization }}"}, {"name": "apikey", "value": "your-supabase-anon-key"}]}, "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [-920, 0], "id": "ee88bb17-847c-4045-a297-3bf02ac3db09", "name": "HTTP Request"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "be275ce9-093e-4a06-81ec-606a1d7358a3", "leftValue": "={{ $('Webhook').item.json.body.user_id }}", "rightValue": "={{ $json.id }}", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [-700, 0], "id": "9a12e783-2583-4b23-aed9-90007ce79308", "name": "If1"}, {"parameters": {"errorMessage": "Unauthorized call to the agent API!"}, "type": "n8n-nodes-base.stopAndError", "typeVersion": 1, "position": [-420, 220], "id": "f0caee6a-198c-4110-a7ee-6e872e87d7d5", "name": "Stop and Error"}, {"parameters": {"operation": "getAll", "tableId": "requests", "limit": 5, "filters": {"conditions": [{"keyName": "timestamp", "condition": "gt", "keyValue": "={{ $now.toUTC().minus(1, 'minutes').format('yyyy-MM-dd HH:mm:ss') }}"}, {"keyName": "user_id", "condition": "eq", "keyValue": "={{ $('Webhook').item.json.body.user_id }}"}]}}, "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [-420, 0], "id": "658601ae-1c3a-48a0-aafe-e2bb7d67e1a8", "name": "Supabase2", "alwaysOutputData": true, "credentials": {"supabaseApi": {"id": "YRyunbk1wZUl88Td", "name": "Supabase Self Hosted"}}}, {"parameters": {"fieldsToSummarize": {"values": [{"field": "id"}]}, "options": {}}, "type": "n8n-nodes-base.summarize", "typeVersion": 1.1, "position": [-200, 0], "id": "ce18b74b-ce29-4fb9-b9c6-dca2ac9319a8", "name": "Summarize"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "ae9ed956-5249-4c7d-a273-5ddafe604388", "leftValue": "={{ $json.count_id }}", "rightValue": 5, "operator": {"type": "number", "operation": "lt"}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [20, 0], "id": "5f4a20c5-f8ed-43f6-b623-3132b085558d", "name": "If2"}, {"parameters": {"respondWith": "json", "responseBody": "{\n  \"output\": \"Rate limit exceeded!\"\n}", "options": {}}, "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1.1, "position": [260, 220], "id": "e0c72957-616b-4998-8cbf-4c9e8390eb6b", "name": "Respond to Webhook1"}, {"parameters": {"tableId": "requests", "fieldsUi": {"fieldValues": [{"fieldId": "id", "fieldValue": "={{ $('Webhook').item.json.body.request_id }}"}, {"fieldId": "user_id", "fieldValue": "={{ $('Webhook').item.json.body.user_id }}"}, {"fieldId": "user_query", "fieldValue": "={{ $('Webhook').item.json.body.query }}"}]}}, "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [680, 40], "id": "8e9d4121-5eee-498d-bcb1-216101dbd2d1", "name": "Supabase3", "credentials": {"supabaseApi": {"id": "YRyunbk1wZUl88Td", "name": "Supabase Self Hosted"}}}], "pinData": {}, "connections": {"Webhook": {"main": [[{"node": "HTTP Request", "type": "main", "index": 0}]]}, "If": {"main": [[{"node": "<PERSON>", "type": "main", "index": 0}, {"node": "Supabase3", "type": "main", "index": 0}], [{"node": "AI Agent", "type": "main", "index": 0}, {"node": "Supabase3", "type": "main", "index": 0}]]}, "Supabase1": {"main": [[{"node": "Basic LLM Chain", "type": "main", "index": 0}, {"node": "AI Agent", "type": "main", "index": 0}, {"node": "<PERSON><PERSON>", "type": "main", "index": 1}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "AI Agent", "type": "ai_languageModel", "index": 0}]]}, "Postgres Chat Memory": {"ai_memory": [[{"node": "AI Agent", "type": "ai_memory", "index": 0}]]}, "AI Agent": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 2}]]}, "OpenAI Chat Model1": {"ai_languageModel": [[{"node": "Basic LLM Chain", "type": "ai_languageModel", "index": 0}]]}, "Basic LLM Chain": {"main": [[{"node": "Supabase", "type": "main", "index": 0}]]}, "Merge": {"main": [[{"node": "Aggregate", "type": "main", "index": 0}]]}, "Aggregate": {"main": [[{"node": "Edit Fields1", "type": "main", "index": 0}]]}, "Edit Fields": {"main": [[{"node": "Supabase1", "type": "main", "index": 0}]]}, "Edit Fields1": {"main": [[{"node": "Respond to Webhook", "type": "main", "index": 0}]]}, "Supabase": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 0}]]}, "HTTP Request": {"main": [[{"node": "If1", "type": "main", "index": 0}]]}, "If1": {"main": [[{"node": "Supabase2", "type": "main", "index": 0}], [{"node": "Stop and Error", "type": "main", "index": 0}]]}, "Supabase2": {"main": [[{"node": "Summarize", "type": "main", "index": 0}]]}, "Summarize": {"main": [[{"node": "If2", "type": "main", "index": 0}]]}, "If2": {"main": [[{"node": "If", "type": "main", "index": 0}], [{"node": "Respond to Webhook1", "type": "main", "index": 0}]]}}, "active": true, "settings": {"executionOrder": "v1"}, "versionId": "c841b5c6-aed2-428d-9f8f-cf90193c62d1", "meta": {"templateCredsSetupCompleted": true, "instanceId": "283ffa8d05f0aea5fd537be52c7be659b3a880d9175d738aa61bb7798a0dea8e"}, "id": "g77skBTKfGkbZfAg", "tags": []}