"""
Chat and conversation routes.
"""
import time
import uuid
from typing import List

from fastapi import APIRouter, Depends, HTTPException, status
from fastapi.responses import StreamingResponse
from sqlalchemy.ext.asyncio import AsyncSession

from ..auth import current_active_user
from ..schemas.conversation import (
    ConversationCreate, ConversationRead, ConversationUpdate,
    ConversationWithMessages, ChatRequest, ChatResponse,
    ConversationStats, MessageRead, ModelInfo
)
from ...core.database import get_async_session
from ...core.container import get_container
from ...core.models.user import User
from ...core.services.conversation import ConversationService
from ...core.services.ai import AIService


def create_chat_router() -> APIRouter:
    """Create chat and conversation router."""
    router = APIRouter(prefix="/chat", tags=["chat"])
    
    @router.get("/models", response_model=List[ModelInfo])
    async def get_available_models(
        current_user: User = Depends(current_active_user),
        session: AsyncSession = Depends(get_async_session),
        container = Depends(get_container)
    ):
        """Get list of available AI models."""
        async with AIService(session, container) as ai_service:
            models = await ai_service.get_available_models()
            return [ModelInfo(**model) for model in models]
    
    @router.post("/completions", response_model=ChatResponse)
    async def chat_completion(
        request: ChatRequest,
        current_user: User = Depends(current_active_user),
        session: AsyncSession = Depends(get_async_session),
        container = Depends(get_container)
    ):
        """Generate AI chat completion."""
        try:
            async with ConversationService(session, container) as conv_service:
                async with AIService(session, container) as ai_service:
                    
                    # Create new conversation if not provided
                    conversation_id = request.conversation_id
                    if not conversation_id:
                        conversation = await conv_service.create_conversation(
                            user_id=current_user.id,
                            model_name=request.model_name,
                            rag_enabled=request.rag_enabled
                        )
                        conversation_id = conversation.id
                    else:
                        # Validate user owns the conversation
                        conversation = await conv_service.get_conversation(
                            conversation_id, current_user.id
                        )
                        if not conversation:
                            raise HTTPException(
                                status_code=status.HTTP_404_NOT_FOUND,
                                detail="Conversation not found"
                            )
                    
                    # Generate AI response
                    if request.stream:
                        # For streaming, we'll handle it differently
                        raise HTTPException(
                            status_code=status.HTTP_400_BAD_REQUEST,
                            detail="Use /chat/stream endpoint for streaming responses"
                        )
                    
                    response_content = await ai_service.chat_completion(
                        user_id=current_user.id,
                        conversation_id=conversation_id,
                        message=request.message,
                        model_name=request.model_name,
                        stream=False,
                        rag_enabled=request.rag_enabled
                    )
                    
                    # Get the created messages
                    messages = await conv_service.get_conversation_messages(
                        conversation_id=conversation_id,
                        limit=2,
                        user_id=current_user.id
                    )
                    
                    # Return the last two messages (user and assistant)
                    user_message = messages[-2] if len(messages) >= 2 else None
                    assistant_message = messages[-1] if messages else None
                    
                    if not user_message or not assistant_message:
                        raise HTTPException(
                            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                            detail="Failed to retrieve conversation messages"
                        )
                    
                    return ChatResponse(
                        conversation_id=conversation_id,
                        user_message=MessageRead.model_validate(user_message),
                        assistant_message=MessageRead.model_validate(assistant_message)
                    )
                    
        except ValueError as e:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=str(e)
            )
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Chat completion failed: {str(e)}"
            )
    
    @router.post("/stream")
    async def chat_stream(
        request: ChatRequest,
        current_user: User = Depends(current_active_user),
        session: AsyncSession = Depends(get_async_session),
        container = Depends(get_container)
    ):
        """Stream AI chat completion."""
        try:
            async def generate_stream():
                async with ConversationService(session, container) as conv_service:
                    async with AIService(session, container) as ai_service:
                        
                        # Create or validate conversation
                        conversation_id = request.conversation_id
                        if not conversation_id:
                            conversation = await conv_service.create_conversation(
                                user_id=current_user.id,
                                model_name=request.model_name,
                                rag_enabled=request.rag_enabled
                            )
                            conversation_id = conversation.id
                        else:
                            conversation = await conv_service.get_conversation(
                                conversation_id, current_user.id
                            )
                            if not conversation:
                                yield f"data: {{'error': 'Conversation not found'}}\n\n"
                                return
                        
                        # Stream the response
                        full_response = ""
                        async for chunk in ai_service._stream_response(
                            agent=ai_service._agents['chat'],
                            messages=[{'role': 'user', 'content': request.message}],
                            rag_context=await ai_service._get_rag_context(
                                request.message, conversation_id
                            ) if request.rag_enabled else None
                        ):
                            full_response += chunk
                            yield f"data: {{'content': '{chunk}'}}\n\n"
                        
                        # Save messages after streaming is complete
                        await conv_service.add_message_pair(
                            conversation_id=conversation_id,
                            user_content=request.message,
                            assistant_content=full_response,
                            model_name=request.model_name,
                            tokens_used=len(full_response.split()) + len(request.message.split())
                        )
                        
                        yield f"data: {{'done': true, 'conversation_id': '{conversation_id}'}}\n\n"
            
            return StreamingResponse(
                generate_stream(),
                media_type="text/plain",
                headers={"Cache-Control": "no-cache"}
            )
            
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Streaming failed: {str(e)}"
            )
    
    @router.post("/conversations", response_model=ConversationRead)
    async def create_conversation(
        request: ConversationCreate,
        current_user: User = Depends(current_active_user),
        session: AsyncSession = Depends(get_async_session),
        container = Depends(get_container)
    ):
        """Create a new conversation."""
        async with ConversationService(session, container) as conv_service:
            conversation = await conv_service.create_conversation(
                user_id=current_user.id,
                title=request.title,
                model_name=request.model_name,
                rag_enabled=request.rag_enabled,
                temperature=request.temperature
            )
            return ConversationRead.model_validate(conversation)
    
    @router.get("/conversations", response_model=List[ConversationRead])
    async def list_conversations(
        limit: int = 50,
        offset: int = 0,
        current_user: User = Depends(current_active_user),
        session: AsyncSession = Depends(get_async_session),
        container = Depends(get_container)
    ):
        """List user's conversations."""
        async with ConversationService(session, container) as conv_service:
            conversations = await conv_service.list_user_conversations(
                user_id=current_user.id,
                limit=limit,
                offset=offset
            )
            return [ConversationRead.model_validate(conv) for conv in conversations]
    
    @router.get("/conversations/{conversation_id}", response_model=ConversationWithMessages)
    async def get_conversation(
        conversation_id: uuid.UUID,
        current_user: User = Depends(current_active_user),
        session: AsyncSession = Depends(get_async_session),
        container = Depends(get_container)
    ):
        """Get conversation with messages."""
        async with ConversationService(session, container) as conv_service:
            conversation = await conv_service.get_conversation_with_messages(
                conversation_id=conversation_id,
                user_id=current_user.id
            )
            
            if not conversation:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Conversation not found"
                )
            
            return ConversationWithMessages.model_validate(conversation)
    
    @router.put("/conversations/{conversation_id}", response_model=ConversationRead)
    async def update_conversation(
        conversation_id: uuid.UUID,
        request: ConversationUpdate,
        current_user: User = Depends(current_active_user),
        session: AsyncSession = Depends(get_async_session),
        container = Depends(get_container)
    ):
        """Update conversation."""
        async with ConversationService(session, container) as conv_service:
            # Update title if provided
            if request.title is not None:
                conversation = await conv_service.update_conversation_title(
                    conversation_id=conversation_id,
                    title=request.title,
                    user_id=current_user.id
                )
                if not conversation:
                    raise HTTPException(
                        status_code=status.HTTP_404_NOT_FOUND,
                        detail="Conversation not found"
                    )
            
            # Update config if any model settings provided
            if any([request.model_name, request.rag_enabled is not None, request.temperature is not None]):
                conversation = await conv_service.update_conversation_config(
                    conversation_id=conversation_id,
                    model_name=request.model_name,
                    rag_enabled=request.rag_enabled,
                    temperature=request.temperature,
                    user_id=current_user.id
                )
                if not conversation:
                    raise HTTPException(
                        status_code=status.HTTP_404_NOT_FOUND,
                        detail="Conversation not found"
                    )
            
            # Get updated conversation
            conversation = await conv_service.get_conversation(
                conversation_id, current_user.id
            )
            return ConversationRead.model_validate(conversation)
    
    @router.delete("/conversations/{conversation_id}")
    async def delete_conversation(
        conversation_id: uuid.UUID,
        current_user: User = Depends(current_active_user),
        session: AsyncSession = Depends(get_async_session),
        container = Depends(get_container)
    ):
        """Delete conversation."""
        async with ConversationService(session, container) as conv_service:
            success = await conv_service.delete_conversation(
                conversation_id=conversation_id,
                user_id=current_user.id
            )
            
            if not success:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Conversation not found"
                )
            
            return {"message": "Conversation deleted successfully"}
    
    @router.get("/conversations/{conversation_id}/stats", response_model=ConversationStats)
    async def get_conversation_stats(
        conversation_id: uuid.UUID,
        current_user: User = Depends(current_active_user),
        session: AsyncSession = Depends(get_async_session),
        container = Depends(get_container)
    ):
        """Get conversation statistics."""
        async with ConversationService(session, container) as conv_service:
            # Validate user owns the conversation
            conversation = await conv_service.get_conversation(
                conversation_id, current_user.id
            )
            if not conversation:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Conversation not found"
                )
            
            stats = await conv_service.get_conversation_stats(conversation_id)
            return ConversationStats(**stats)
    
    @router.post("/conversations/{conversation_id}/title/generate")
    async def generate_conversation_title(
        conversation_id: uuid.UUID,
        current_user: User = Depends(current_active_user),
        session: AsyncSession = Depends(get_async_session),
        container = Depends(get_container)
    ):
        """Generate AI title for conversation."""
        async with ConversationService(session, container) as conv_service:
            async with AIService(session, container) as ai_service:
                
                # Validate user owns the conversation
                conversation = await conv_service.get_conversation(
                    conversation_id, current_user.id
                )
                if not conversation:
                    raise HTTPException(
                        status_code=status.HTTP_404_NOT_FOUND,
                        detail="Conversation not found"
                    )
                
                # Generate title
                title = await ai_service.generate_title(conversation_id)
                
                # Update conversation
                updated_conversation = await conv_service.update_conversation_title(
                    conversation_id=conversation_id,
                    title=title,
                    user_id=current_user.id
                )
                
                return {"title": title}
    
    return router