"""
Ollama HTTP client implementation.

This module provides a client for communicating with Ollama API
for local AI model management and inference.
"""

import asyncio
import json
from typing import Any, AsyncGenerator, Dict, List, Optional
from urllib.parse import urljoin

import httpx
from loguru import logger

from .models import (
    OllamaModel, ModelInfo, ModelDownloadProgress, ChatRequest, ChatResponse,
    EmbeddingRequest, EmbeddingResponse, GenerateRequest, GenerateResponse,
    ModelPullRequest, ModelDeleteRequest, OllamaError
)


class OllamaClient:
    """
    Ollama HTTP client.
    
    Provides methods for interacting with Ollama API for model management
    and AI inference operations.
    """
    
    def __init__(self, base_url: str = "http://localhost:11434", timeout: float = 60.0):
        """
        Initialize Ollama client.
        
        Args:
            base_url: Ollama server base URL
            timeout: Request timeout in seconds
        """
        self.base_url = base_url.rstrip('/')
        self.timeout = timeout
        
        # HTTP client with extended timeout for model operations
        self._client = httpx.AsyncClient(
            timeout=httpx.Timeout(timeout, read=300.0),  # Extended read timeout for downloads
            limits=httpx.Limits(max_connections=10, max_keepalive_connections=5)
        )
    
    async def __aenter__(self):
        """Async context manager entry."""
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        await self.close()
    
    async def health_check(self) -> bool:
        """
        Check if Ollama server is healthy.
        
        Returns:
            True if server is healthy, False otherwise
        """
        try:
            response = await self._client.get(f"{self.base_url}/api/tags")
            return response.status_code == 200
        except Exception as e:
            logger.warning(f"Ollama health check failed: {e}")
            return False
    
    async def list_models(self) -> List[OllamaModel]:
        """
        List all downloaded models.
        
        Returns:
            List of downloaded models
        """
        try:
            response = await self._client.get(f"{self.base_url}/api/tags")
            response.raise_for_status()
            
            data = response.json()
            models = []
            
            for model_data in data.get("models", []):
                model = OllamaModel(
                    name=model_data["name"].split(":")[0],
                    tag=model_data["name"].split(":")[-1] if ":" in model_data["name"] else "latest",
                    size=model_data.get("size"),
                    digest=model_data.get("digest"),
                    modified_at=model_data.get("modified_at")
                )
                models.append(model)
            
            return models
            
        except Exception as e:
            logger.error(f"Failed to list models: {e}")
            raise
    
    async def pull_model(self, name: str, stream: bool = True) -> AsyncGenerator[ModelDownloadProgress, None]:
        """
        Pull/download a model.
        
        Args:
            name: Model name to pull
            stream: Stream download progress
            
        Yields:
            Download progress updates
        """
        try:
            request = ModelPullRequest(name=name, stream=stream)
            
            async with self._client.stream(
                "POST",
                f"{self.base_url}/api/pull",
                json=request.dict(),
                timeout=None  # No timeout for downloads
            ) as response:
                response.raise_for_status()
                
                async for line in response.aiter_lines():
                    if line.strip():
                        try:
                            data = json.loads(line)
                            
                            progress = ModelDownloadProgress(
                                model_name=name,
                                status=data.get("status", "downloading"),
                                completed=data.get("completed", 0),
                                total=data.get("total", 0),
                                percent=self._calculate_percent(
                                    data.get("completed", 0),
                                    data.get("total", 0)
                                ),
                                digest=data.get("digest")
                            )
                            
                            yield progress
                            
                            if progress.is_complete:
                                break
                                
                        except json.JSONDecodeError:
                            continue
                            
        except Exception as e:
            logger.error(f"Failed to pull model {name}: {e}")
            raise
    
    async def delete_model(self, name: str) -> bool:
        """
        Delete a model.
        
        Args:
            name: Model name to delete
            
        Returns:
            True if deleted successfully
        """
        try:
            request = ModelDeleteRequest(name=name)
            
            response = await self._client.delete(
                f"{self.base_url}/api/delete",
                json=request.dict()
            )
            
            return response.status_code == 200
            
        except Exception as e:
            logger.error(f"Failed to delete model {name}: {e}")
            return False
    
    async def chat(self, request: ChatRequest) -> ChatResponse:
        """
        Generate chat completion.
        
        Args:
            request: Chat request
            
        Returns:
            Chat response
        """
        try:
            response = await self._client.post(
                f"{self.base_url}/api/chat",
                json=request.dict(exclude_none=True)
            )
            response.raise_for_status()
            
            data = response.json()
            return ChatResponse(**data)
            
        except Exception as e:
            logger.error(f"Chat completion failed: {e}")
            raise
    
    async def chat_stream(self, request: ChatRequest) -> AsyncGenerator[ChatResponse, None]:
        """
        Generate streaming chat completion.
        
        Args:
            request: Chat request with stream=True
            
        Yields:
            Chat response chunks
        """
        try:
            request.stream = True
            
            async with self._client.stream(
                "POST",
                f"{self.base_url}/api/chat",
                json=request.dict(exclude_none=True)
            ) as response:
                response.raise_for_status()
                
                async for line in response.aiter_lines():
                    if line.strip():
                        try:
                            data = json.loads(line)
                            yield ChatResponse(**data)
                            
                            if data.get("done", False):
                                break
                                
                        except json.JSONDecodeError:
                            continue
                            
        except Exception as e:
            logger.error(f"Streaming chat failed: {e}")
            raise
    
    async def generate(self, request: GenerateRequest) -> GenerateResponse:
        """
        Generate text completion.
        
        Args:
            request: Generate request
            
        Returns:
            Generate response
        """
        try:
            response = await self._client.post(
                f"{self.base_url}/api/generate",
                json=request.dict(exclude_none=True)
            )
            response.raise_for_status()
            
            data = response.json()
            return GenerateResponse(**data)
            
        except Exception as e:
            logger.error(f"Text generation failed: {e}")
            raise
    
    async def generate_stream(self, request: GenerateRequest) -> AsyncGenerator[GenerateResponse, None]:
        """
        Generate streaming text completion.
        
        Args:
            request: Generate request with stream=True
            
        Yields:
            Generate response chunks
        """
        try:
            request.stream = True
            
            async with self._client.stream(
                "POST",
                f"{self.base_url}/api/generate",
                json=request.dict(exclude_none=True)
            ) as response:
                response.raise_for_status()
                
                async for line in response.aiter_lines():
                    if line.strip():
                        try:
                            data = json.loads(line)
                            yield GenerateResponse(**data)
                            
                            if data.get("done", False):
                                break
                                
                        except json.JSONDecodeError:
                            continue
                            
        except Exception as e:
            logger.error(f"Streaming generation failed: {e}")
            raise
    
    async def embeddings(self, request: EmbeddingRequest) -> EmbeddingResponse:
        """
        Generate text embeddings.
        
        Args:
            request: Embedding request
            
        Returns:
            Embedding response
        """
        try:
            response = await self._client.post(
                f"{self.base_url}/api/embeddings",
                json=request.dict()
            )
            response.raise_for_status()
            
            data = response.json()
            return EmbeddingResponse(**data)
            
        except Exception as e:
            logger.error(f"Embedding generation failed: {e}")
            raise
    
    async def show_model(self, name: str) -> Dict[str, Any]:
        """
        Show model information.
        
        Args:
            name: Model name
            
        Returns:
            Model information
        """
        try:
            response = await self._client.post(
                f"{self.base_url}/api/show",
                json={"name": name}
            )
            response.raise_for_status()
            
            return response.json()
            
        except Exception as e:
            logger.error(f"Failed to show model {name}: {e}")
            raise
    
    def _calculate_percent(self, completed: int, total: int) -> float:
        """Calculate completion percentage."""
        if total <= 0:
            return 0.0
        return min(100.0, (completed / total) * 100.0)
    
    async def close(self) -> None:
        """Close the HTTP client."""
        try:
            await self._client.aclose()
            logger.info("Ollama client closed")
        except Exception as e:
            logger.warning(f"Error closing Ollama client: {e}")


# Example usage
async def example_usage():
    """Example of how to use the Ollama client."""
    async with OllamaClient() as client:
        # Check health
        is_healthy = await client.health_check()
        print(f"Ollama is healthy: {is_healthy}")
        
        if not is_healthy:
            return
        
        # List models
        models = await client.list_models()
        print(f"Downloaded models: {[m.full_name for m in models]}")
        
        # Pull a model if needed
        if not any(m.name == "llama3.2" for m in models):
            print("Downloading llama3.2...")
            async for progress in client.pull_model("llama3.2:latest"):
                print(f"Download progress: {progress.percent:.1f}%")
                if progress.is_complete:
                    break
        
        # Chat with model
        chat_request = ChatRequest(
            model="llama3.2:latest",
            messages=[{"role": "user", "content": "Hello, how are you?"}]
        )
        
        response = await client.chat(chat_request)
        print(f"Chat response: {response.message['content']}")
        
        # Generate embeddings
        embed_request = EmbeddingRequest(
            model="nomic-embed-text:latest",
            prompt="Hello world"
        )
        
        try:
            embeddings = await client.embeddings(embed_request)
            print(f"Embedding dimension: {len(embeddings.embedding)}")
        except Exception as e:
            print(f"Embeddings not available: {e}")
