"""
AI services configuration settings.

This module contains all AI provider configuration
following the Single Responsibility Principle.
"""

from typing import Optional
from pydantic_settings import BaseSettings


class OllamaSettings(BaseSettings):
    """Ollama local LLM settings."""
    
    host: str = "localhost"
    port: int = 11434
    timeout: int = 300
    
    # Model management
    models_path: str = "/data/models"
    default_model: str = "llama3.2:latest"
    
    class Config:
        env_prefix = "OLLAMA_"
    
    @property
    def url(self) -> str:
        """Construct Ollama URL."""
        return f"http://{self.host}:{self.port}"


class OpenAISettings(BaseSettings):
    """OpenAI API settings."""
    
    api_key: Optional[str] = None
    organization: Optional[str] = None
    base_url: str = "https://api.openai.com/v1"
    
    # Model settings
    default_chat_model: str = "gpt-4"
    default_embedding_model: str = "text-embedding-3-small"
    
    # Rate limiting
    max_requests_per_minute: int = 60
    max_tokens_per_minute: int = 150000
    
    class Config:
        env_prefix = "OPENAI_"


class AnthropicSettings(BaseSettings):
    """Anthropic API settings."""
    
    api_key: Optional[str] = None
    base_url: str = "https://api.anthropic.com"
    
    # Model settings
    default_model: str = "claude-3-sonnet-20240229"
    
    # Rate limiting
    max_requests_per_minute: int = 60
    max_tokens_per_minute: int = 80000
    
    class Config:
        env_prefix = "ANTHROPIC_"


class MCPSettings(BaseSettings):
    """Model Context Protocol settings."""
    
    enabled: bool = True
    server_name: str = "loni-mcp-server"
    server_version: str = "1.0.0"
    
    # Tool configuration
    max_tool_calls: int = 10
    tool_timeout: int = 30
    
    # Resource limits
    max_resource_size: int = 10 * 1024 * 1024  # 10MB
    
    class Config:
        env_prefix = "MCP_"