#!/bin/bash
# LONI Platform Logging Infrastructure Setup Script
# Creates log directories and sets up log rotation

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging function
log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1" >&2
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

warn() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# Get script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../../.." && pwd)"
LOG_ROOT="$PROJECT_ROOT/data/logs"

log "Setting up LONI Platform logging infrastructure..."
log "Project root: $PROJECT_ROOT"
log "Log root: $LOG_ROOT"

# Create log directory structure
create_log_directories() {
    log "Creating log directory structure..."
    
    local directories=(
        "$LOG_ROOT"
        "$LOG_ROOT/applications"
        "$LOG_ROOT/containers"
        "$LOG_ROOT/development"
        "$LOG_ROOT/development/typescript"
        "$LOG_ROOT/development/eslint"
        "$LOG_ROOT/development/prettier"
        "$LOG_ROOT/development/jest"
        "$LOG_ROOT/development/docker"
        "$LOG_ROOT/system"
        "$LOG_ROOT/structured"
        "$LOG_ROOT/summary"
        "$LOG_ROOT/archive"
    )
    
    for dir in "${directories[@]}"; do
        if [[ ! -d "$dir" ]]; then
            mkdir -p "$dir"
            log "Created directory: $dir"
        else
            log "Directory already exists: $dir"
        fi
    done
    
    # Set proper permissions
    chmod -R 755 "$LOG_ROOT"
    success "Log directory structure created successfully"
}

# Create log rotation configuration
create_logrotate_config() {
    log "Creating log rotation configuration..."
    
    local logrotate_config="$LOG_ROOT/logrotate.conf"
    
    cat > "$logrotate_config" << 'EOF'
# LONI Platform Log Rotation Configuration

# Application logs
/var/log/loni/applications/*.log {
    daily
    rotate 7
    compress
    delaycompress
    missingok
    notifempty
    create 644 root root
    postrotate
        # Signal applications to reopen log files if needed
        /bin/kill -HUP $(cat /var/run/loni-*.pid 2>/dev/null) 2>/dev/null || true
    endscript
}

# Container logs
/var/log/loni/containers/*.log {
    daily
    rotate 14
    compress
    delaycompress
    missingok
    notifempty
    create 644 root root
    maxsize 100M
}

# Development tool logs
/var/log/loni/development/**/*.log {
    daily
    rotate 3
    compress
    delaycompress
    missingok
    notifempty
    create 644 root root
    maxsize 50M
}

# Structured logs for AI parsing
/var/log/loni/structured/*.jsonl {
    daily
    rotate 30
    compress
    delaycompress
    missingok
    notifempty
    create 644 root root
    maxsize 500M
}

# Error summary logs
/var/log/loni/summary/*.log {
    hourly
    rotate 168  # 7 days of hourly logs
    compress
    delaycompress
    missingok
    notifempty
    create 644 root root
    maxsize 10M
}
EOF

    success "Log rotation configuration created: $logrotate_config"
}

# Create development tools logging configuration
create_dev_tools_config() {
    log "Creating development tools logging configuration..."
    
    local dev_config_dir="$PROJECT_ROOT/apps/infra/config/development"
    mkdir -p "$dev_config_dir"
    
    # TypeScript logging configuration
    cat > "$dev_config_dir/typescript-logging.json" << 'EOF'
{
  "compilerOptions": {
    "pretty": false,
    "listFiles": false,
    "listEmittedFiles": false,
    "traceResolution": false,
    "diagnostics": false,
    "extendedDiagnostics": false
  },
  "logging": {
    "level": "error",
    "format": "json",
    "output": "/var/log/loni/development/typescript/tsc-errors.log"
  }
}
EOF

    # ESLint logging configuration
    cat > "$dev_config_dir/eslint-logging.json" << 'EOF'
{
  "format": "json",
  "outputFile": "/var/log/loni/development/eslint/eslint-errors.log",
  "quiet": true,
  "maxWarnings": 0
}
EOF

    # Jest logging configuration
    cat > "$dev_config_dir/jest-logging.json" << 'EOF'
{
  "verbose": false,
  "silent": true,
  "reporters": [
    ["default", {"silent": true}],
    ["json", {"outputFile": "/var/log/loni/development/jest/test-results.log"}]
  ],
  "testResultsProcessor": "./scripts/jest-error-processor.js"
}
EOF

    success "Development tools logging configuration created"
}

# Create AI observability summary
create_ai_summary() {
    log "Creating AI observability summary..."
    
    local summary_file="$LOG_ROOT/AI_OBSERVABILITY_SUMMARY.json"
    
    cat > "$summary_file" << EOF
{
  "platform": "LONI",
  "logging_infrastructure": {
    "version": "1.0.0",
    "last_updated": "$(date -u +"%Y-%m-%dT%H:%M:%SZ")",
    "status": "active"
  },
  "log_locations": {
    "applications": "$LOG_ROOT/applications/",
    "containers": "$LOG_ROOT/containers/",
    "development": "$LOG_ROOT/development/",
    "structured_ai": "$LOG_ROOT/structured/",
    "error_summary": "$LOG_ROOT/summary/"
  },
  "ai_parsing_format": {
    "structured_logs": "JSON Lines format in structured/ directory",
    "error_filtering": "Only ERROR, FATAL, CRITICAL levels captured",
    "timestamp_format": "ISO8601",
    "fields": ["timestamp", "level", "service", "container_name", "message", "filename", "line_number"]
  },
  "log_rotation": {
    "applications": "7 days",
    "containers": "14 days", 
    "development": "3 days",
    "structured": "30 days",
    "summary": "7 days (hourly)"
  },
  "monitoring": {
    "fluent_bit_endpoint": "http://localhost:2020",
    "log_aggregation": "enabled",
    "error_only_filtering": "enabled"
  }
}
EOF

    success "AI observability summary created: $summary_file"
}

# Main execution
main() {
    log "Starting LONI logging infrastructure setup..."
    
    create_log_directories
    create_logrotate_config
    create_dev_tools_config
    create_ai_summary
    
    success "LONI logging infrastructure setup completed successfully!"
    log "Log directories created in: $LOG_ROOT"
    log "Configuration files created in: $PROJECT_ROOT/apps/infra/config/"
    log "AI observability summary: $LOG_ROOT/AI_OBSERVABILITY_SUMMARY.json"
    
    warn "Remember to:"
    warn "1. Run 'docker-compose up fluent-bit' to start log aggregation"
    warn "2. Configure your applications to write logs to the designated directories"
    warn "3. Set up cron job for log rotation: 'crontab -e' and add:"
    warn "   0 2 * * * /usr/sbin/logrotate $LOG_ROOT/logrotate.conf"
}

# Run main function
main "$@"
