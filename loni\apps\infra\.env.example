# LONI Infrastructure Environment Variables
# Copy this file to .env and update the values

# General Settings
TIMEZONE=UTC
COMPOSE_PROJECT_NAME=loni

# Database Settings
POSTGRES_USER=loni
POSTGRES_PASSWORD=loni_secure_password_change_me
POSTGRES_DB=loni
POSTGRES_PORT=5432

# Qdrant Vector Database
QDRANT_PORT=6333
QDRANT_GRPC_PORT=6334

# Ollama AI Models
OLLAMA_PORT=11434

# Redis Cache
REDIS_PASSWORD=loni_redis_password_change_me
REDIS_PORT=6379

# Neo4j Graph Database
NEO4J_PASSWORD=loni_neo4j_password_change_me
NEO4J_HTTP_PORT=7474
NEO4J_BOLT_PORT=7687

# n8n Workflow Automation
N8N_USER=admin
N8N_PASSWORD=loni_n8n_password_change_me
N8N_HOST=localhost
N8N_PORT=5678
N8N_DB=n8n

# Monitoring
PROMETHEUS_PORT=9090
GRAFANA_USER=admin
GRAFANA_PASSWORD=loni_grafana_password_change_me
GRAFANA_HOST=localhost
GRAFANA_PORT=3001

# Tracing
JAEGER_UI_PORT=16686
JAEGER_GRPC_PORT=14250
JAEGER_HTTP_PORT=14268
JAEGER_OTLP_GRPC_PORT=4317
JAEGER_OTLP_HTTP_PORT=4318

# Reverse Proxy
HTTP_PORT=80
HTTPS_PORT=443

# Application URLs (for nginx configuration)
FRONTEND_URL=http://localhost:3000
BACKEND_URL=http://localhost:8000

# Security Settings
JWT_SECRET=your_jwt_secret_key_change_me_to_something_very_secure
ENCRYPTION_KEY=your_encryption_key_32_chars_long

# External Services (Optional)
OPENAI_API_KEY=your_openai_api_key_here
ANTHROPIC_API_KEY=your_anthropic_api_key_here
GOOGLE_API_KEY=your_google_api_key_here
BRAVE_API_KEY=your_brave_search_api_key_here

# Email Settings (Optional)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your_app_password
SMTP_FROM=<EMAIL>

# Cloud Storage (Optional)
AWS_ACCESS_KEY_ID=your_aws_access_key
AWS_SECRET_ACCESS_KEY=your_aws_secret_key
AWS_REGION=us-east-1
S3_BUCKET=your-loni-bucket

# Backup Settings
BACKUP_SCHEDULE=0 2 * * *  # Daily at 2 AM
BACKUP_RETENTION_DAYS=30
BACKUP_S3_BUCKET=your-backup-bucket