-- PostgreSQL initialization script for LONI platform
-- Creates databases and users for n8n and other services

-- Create n8n database and grant access to main loni user
CREATE DATABASE n8n;
GRANT ALL PRIVILEGES ON DATABASE n8n TO loni;

-- Grant privileges on the database
\c n8n;
GRANT ALL ON SCHEMA public TO loni;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO loni;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO loni;

-- Enable necessary extensions for LONI platform
\c loni;
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";
CREATE EXTENSION IF NOT EXISTS "vector";

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_loni_users_email ON users(email);
CREATE INDEX IF NOT EXISTS idx_loni_conversations_user_id ON conversations(user_id);
CREATE INDEX IF NOT EXISTS idx_loni_messages_conversation_id ON messages(conversation_id);

-- Create function for updating timestamps
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Grant connection limits
ALTER USER loni CONNECTION LIMIT 50;