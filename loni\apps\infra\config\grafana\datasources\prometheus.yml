# Grafana datasource configuration for Prometheus

apiVersion: 1

datasources:
  # Prometheus datasource
  - name: Prometheus
    type: prometheus
    access: proxy
    url: http://prometheus:9090
    isDefault: true
    editable: true
    basicAuth: false
    jsonData:
      httpMethod: POST
      timeInterval: 15s
      queryTimeout: 60s
      defaultRegion: us-east-1
      keepCookies: []
    version: 1

  # Jaeger datasource for tracing
  - name: Jaeger
    type: jaeger
    access: proxy
    url: http://jaeger:16686
    editable: true
    basicAuth: false
    jsonData:
      tracesToLogsV2:
        datasourceUid: 'loki'
        spanStartTimeShift: '1h'
        spanEndTimeShift: '-1h'
        tags: ['job', 'instance', 'pod', 'namespace']
        filterByTraceID: false
        filterBySpanID: false
    version: 1

  # PostgreSQL datasource for database metrics
  - name: PostgreSQL
    type: postgres
    access: proxy
    url: postgres:5432
    database: loni
    user: loni
    editable: true
    basicAuth: false
    secureJsonData:
      password: loni_secure_password
    jsonData:
      sslmode: disable
      maxOpenConns: 100
      maxIdleConns: 100
      maxIdleConnsAuto: true
      connMaxLifetime: 14400
      timescaledb: false
    version: 1

  # TestData datasource for development
  - name: TestData
    type: testdata
    access: proxy
    editable: true
    basicAuth: false
    isDefault: false
    version: 1