#!/bin/bash

# LONI Platform Infrastructure Setup Script
# This script sets up the complete LONI infrastructure

set -e

echo "🚀 Setting up LONI Platform Infrastructure..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${BLUE}=== $1 ===${NC}"
}

# Check if Docker is installed
check_docker() {
    if ! command -v docker &> /dev/null; then
        print_error "Docker is not installed. Please install Docker first."
        exit 1
    fi

    if ! command -v docker-compose &> /dev/null; then
        print_error "Docker Compose is not installed. Please install Docker Compose first."
        exit 1
    fi

    print_status "Docker and Docker Compose are installed"
}

# Create necessary directories
create_directories() {
    print_header "Creating Directory Structure"
    
    directories=(
        "data/postgres"
        "data/qdrant"
        "data/ollama"
        "data/redis"
        "data/neo4j"
        "data/n8n"
        "data/prometheus"
        "data/grafana"
        "logs/nginx"
        "logs/backend"
        "logs/frontend"
        "config/ollama"
        "config/redis"
        "backups"
    )

    for dir in "${directories[@]}"; do
        mkdir -p "$dir"
        print_status "Created directory: $dir"
    done
}

# Set up environment file
setup_environment() {
    print_header "Setting up Environment Configuration"
    
    if [ ! -f .env ]; then
        if [ -f .env.example ]; then
            cp .env.example .env
            print_status "Created .env file from .env.example"
            print_warning "Please edit .env file with your specific configuration"
        else
            print_error ".env.example file not found"
            exit 1
        fi
    else
        print_status ".env file already exists"
    fi
}

# Generate secrets
generate_secrets() {
    print_header "Generating Security Secrets"
    
    # Generate random passwords if not set
    if ! grep -q "POSTGRES_PASSWORD=.*[a-zA-Z0-9]" .env; then
        POSTGRES_PASS=$(openssl rand -base64 32)
        sed -i "s/POSTGRES_PASSWORD=.*/POSTGRES_PASSWORD=${POSTGRES_PASS}/" .env
        print_status "Generated PostgreSQL password"
    fi

    if ! grep -q "JWT_SECRET=.*[a-zA-Z0-9]" .env; then
        JWT_SECRET=$(openssl rand -base64 64)
        sed -i "s/JWT_SECRET=.*/JWT_SECRET=${JWT_SECRET}/" .env
        print_status "Generated JWT secret"
    fi

    if ! grep -q "ENCRYPTION_KEY=.*[a-zA-Z0-9]" .env; then
        ENCRYPTION_KEY=$(openssl rand -base64 32)
        sed -i "s/ENCRYPTION_KEY=.*/ENCRYPTION_KEY=${ENCRYPTION_KEY}/" .env
        print_status "Generated encryption key"
    fi
}

# Set up Nginx authentication
setup_nginx_auth() {
    print_header "Setting up Nginx Authentication"
    
    if ! command -v htpasswd &> /dev/null; then
        print_warning "htpasswd not found. Installing apache2-utils..."
        if command -v apt-get &> /dev/null; then
            sudo apt-get update && sudo apt-get install -y apache2-utils
        elif command -v yum &> /dev/null; then
            sudo yum install -y httpd-tools
        elif command -v brew &> /dev/null; then
            brew install httpd
        else
            print_error "Cannot install htpasswd. Please install apache2-utils or httpd-tools manually."
            exit 1
        fi
    fi

    mkdir -p config/nginx
    
    if [ ! -f config/nginx/.htpasswd ]; then
        print_status "Creating basic auth file for monitoring endpoints..."
        echo -n "Enter username for monitoring access: "
        read -r USERNAME
        htpasswd -c config/nginx/.htpasswd "$USERNAME"
        print_status "Created basic auth file"
    else
        print_status "Basic auth file already exists"
    fi
}

# Pull Docker images
pull_images() {
    print_header "Pulling Docker Images"
    
    print_status "Pulling base images..."
    docker-compose pull

    print_status "All images pulled successfully"
}

# Initialize databases
init_databases() {
    print_header "Initializing Databases"
    
    # Start database services
    print_status "Starting database services..."
    docker-compose up -d postgres redis qdrant
    
    # Wait for services to be ready
    print_status "Waiting for databases to be ready..."
    sleep 30
    
    # Run database migrations (you'll need to add actual migration scripts)
    print_status "Running database migrations..."
    # docker-compose exec -T postgres psql -U loni -d loni -f /docker-entrypoint-initdb.d/001-init.sql
    
    print_status "Databases initialized"
}

# Set up Ollama models
setup_ollama() {
    print_header "Setting up Ollama AI Models"
    
    print_status "Starting Ollama service..."
    docker-compose up -d ollama
    
    print_status "Waiting for Ollama to be ready..."
    sleep 20
    
    # Pull default models
    print_status "Pulling default AI models..."
    
    models=("llama2" "codellama" "mistral")
    
    for model in "${models[@]}"; do
        print_status "Pulling $model model..."
        docker-compose exec ollama ollama pull "$model" || print_warning "Failed to pull $model"
    done
    
    print_status "Ollama setup complete"
}

# Start all services
start_services() {
    print_header "Starting All Services"
    
    print_status "Starting infrastructure services..."
    docker-compose up -d
    
    print_status "Waiting for services to be ready..."
    sleep 45
    
    # Health checks
    print_status "Performing health checks..."
    
    services=("postgres:5432" "redis:6379" "qdrant:6333" "prometheus:9090" "grafana:3000")
    
    for service in "${services[@]}"; do
        container=$(echo "$service" | cut -d':' -f1)
        port=$(echo "$service" | cut -d':' -f2)
        
        if docker-compose exec "$container" nc -z localhost "$port" 2>/dev/null; then
            print_status "$container is healthy"
        else
            print_warning "$container health check failed"
        fi
    done
}

# Setup monitoring
setup_monitoring() {
    print_header "Setting up Monitoring"
    
    # Create Grafana dashboards directory
    mkdir -p config/grafana/dashboards/provisioned
    
    # Copy dashboard files
    if [ -f config/grafana/dashboards/loni-overview.json ]; then
        cp config/grafana/dashboards/loni-overview.json config/grafana/dashboards/provisioned/
        print_status "Copied LONI overview dashboard"
    fi
    
    print_status "Monitoring setup complete"
}

# Print access information
print_access_info() {
    print_header "LONI Platform Access Information"
    
    echo ""
    print_status "Service Access URLs:"
    echo "  • Main Application: http://localhost"
    echo "  • API Documentation: http://localhost/api/docs"
    echo "  • Monitoring (Grafana): http://localhost/monitoring"
    echo "  • Metrics (Prometheus): http://localhost/prometheus"
    echo "  • Automation (n8n): http://localhost/automation"
    echo "  • Tracing (Jaeger): http://localhost/tracing"
    echo ""
    
    print_status "Database Access:"
    echo "  • PostgreSQL: localhost:5432"
    echo "  • Qdrant: localhost:6333"
    echo "  • Redis: localhost:6379"
    echo "  • Neo4j: localhost:7474 (Browser), localhost:7687 (Bolt)"
    echo ""
    
    print_status "Default Credentials:"
    echo "  • Check your .env file for generated passwords"
    echo "  • Monitoring auth: See config/nginx/.htpasswd"
    echo ""
    
    print_warning "Security Notes:"
    echo "  • Change default passwords in production"
    echo "  • Configure SSL/TLS certificates"
    echo "  • Review firewall settings"
    echo "  • Set up proper backup procedures"
    echo ""
}

# Main execution
main() {
    print_header "LONI Platform Infrastructure Setup"
    
    # Change to script directory
    cd "$(dirname "$0")"
    
    # Run setup steps
    check_docker
    create_directories
    setup_environment
    generate_secrets
    setup_nginx_auth
    pull_images
    init_databases
    setup_ollama
    setup_monitoring
    start_services
    
    print_access_info
    
    print_status "🎉 LONI Platform setup complete!"
    print_status "You can now access the platform at http://localhost"
}

# Run main function
main "$@"