"""
Environment configuration generator.

Generates .env and .env.example files with proper variable management,
security considerations, and documentation.
"""

import os
import secrets
import string
from typing import Dict, Set, Any, List, Optional
from pathlib import Path

from ..core.interfaces import IEnvironmentGenerator, ITemplateRegistry
from ..core.models import ProjectConfig, GenerationResult


class EnvironmentGenerator(IEnvironmentGenerator):
    """
    Generator for environment configuration files.
    
    Creates secure .env files with generated passwords and proper
    documentation in .env.example files.
    """
    
    def __init__(self, template_registry: ITemplateRegistry):
        """Initialize with template registry."""
        self.template_registry = template_registry
        
        # Security settings for password generation
        self.password_length = 16
        self.password_chars = string.ascii_letters + string.digits + "!@#$%^&*"
    
    def generate(self, project_path: Path, config: ProjectConfig) -> GenerationResult:
        """Generate environment files for the project."""
        result = GenerationResult(
            success=True,
            project_path=str(project_path)
        )
        
        try:
            # Collect environment variables from templates
            env_vars = self._collect_environment_variables(config.selected_templates)
            
            # Add custom environment variables
            env_vars.update(config.environment_variables)
            
            # Generate secure values for sensitive variables
            secure_env_vars = self._generate_secure_values(env_vars)
            
            # Generate .env file
            env_content = self.generate_env_file(secure_env_vars)
            env_file = project_path / ".env"
            with open(env_file, 'w') as f:
                f.write(env_content)
            
            result.add_generated_file(
                path=".env",
                generated_by="EnvironmentGenerator"
            )
            
            # Generate .env.example file
            example_content = self.generate_env_example(env_vars)
            example_file = project_path / ".env.example"
            with open(example_file, 'w') as f:
                f.write(example_content)
            
            result.add_generated_file(
                path=".env.example",
                generated_by="EnvironmentGenerator"
            )
            
            # Generate environment-specific files if needed
            if self._should_generate_env_variants(config):
                self._generate_environment_variants(project_path, env_vars, result)
            
            # Add next steps
            result.next_steps.extend([
                "Review generated .env file and update values as needed",
                "Never commit .env file to version control",
                "Share .env.example file with your team",
                "Update sensitive credentials before deployment"
            ])
            
        except Exception as e:
            result.success = False
            result.errors.append(f"Environment generation failed: {str(e)}")
        
        return result
    
    def can_generate(self, templates: Set[str]) -> bool:
        """Check if any templates have environment variables."""
        for template_id in templates:
            template = self.template_registry.get_template(template_id)
            if template:
                metadata = template.get_metadata()
                if metadata.environment_variables:
                    return True
        return True  # Always generate at least basic env files
    
    def generate_env_file(self, variables: Dict[str, str]) -> str:
        """Generate .env file content with actual values."""
        lines = [
            "# Environment Configuration",
            "# Generated by Project Template Orchestrator",
            "#",
            "# IMPORTANT: This file contains sensitive information.",
            "# Do not commit this file to version control!",
            "#",
            "# To use this file:",
            "#   1. Review all values below",
            "#   2. Update placeholder values with real credentials",
            "#   3. Ensure this file is in your .gitignore",
            "",
        ]
        
        # Group variables by category
        categorized_vars = self._categorize_variables(variables)
        
        for category, vars_in_category in categorized_vars.items():
            if not vars_in_category:
                continue
                
            lines.append(f"# {category.upper()} CONFIGURATION")
            lines.append("")
            
            for var_name, var_value in vars_in_category.items():
                # Add comment for sensitive variables
                if self._is_sensitive_variable(var_name):
                    lines.append(f"# SENSITIVE: Update this value before use")
                
                lines.append(f"{var_name}={var_value}")
            
            lines.append("")
        
        return "\n".join(lines)
    
    def generate_env_example(self, variables: Dict[str, str]) -> str:
        """Generate .env.example file with documentation."""
        lines = [
            "# Environment Configuration Example",
            "# Generated by Project Template Orchestrator",
            "#",
            "# This file shows all required environment variables for this project.",
            "# Copy this file to .env and update the values.",
            "#",
            "# Copy command:",
            "#   cp .env.example .env",
            "",
        ]
        
        # Group variables by category
        categorized_vars = self._categorize_variables(variables)
        
        for category, vars_in_category in categorized_vars.items():
            if not vars_in_category:
                continue
                
            lines.append(f"# {category.upper()} CONFIGURATION")
            lines.append("")
            
            for var_name, var_value in vars_in_category.items():
                # Add description comment
                description = self._get_variable_description(var_name)
                if description:
                    lines.append(f"# {description}")
                
                # Use placeholder for sensitive variables
                if self._is_sensitive_variable(var_name):
                    example_value = self._get_example_value(var_name)
                else:
                    example_value = var_value
                
                lines.append(f"{var_name}={example_value}")
                lines.append("")
        
        return "\n".join(lines)
    
    def _collect_environment_variables(self, template_ids: Set[str]) -> Dict[str, str]:
        """Collect environment variables from all selected templates."""
        env_vars = {}
        
        # Add basic project variables
        env_vars.update({
            "PROJECT_NAME": "my-project",
            "NODE_ENV": "development",
            "ENVIRONMENT": "development"
        })
        
        # Collect from templates
        for template_id in template_ids:
            template = self.template_registry.get_template(template_id)
            if template:
                metadata = template.get_metadata()
                env_vars.update(metadata.environment_variables)
        
        return env_vars
    
    def _generate_secure_values(self, env_vars: Dict[str, str]) -> Dict[str, str]:
        """Generate secure values for sensitive environment variables."""
        secure_vars = env_vars.copy()
        
        for var_name, var_value in env_vars.items():
            if self._needs_secure_generation(var_name, var_value):
                secure_vars[var_name] = self._generate_secure_value(var_name)
        
        return secure_vars
    
    def _needs_secure_generation(self, var_name: str, var_value: str) -> bool:
        """Check if variable needs secure value generation."""
        sensitive_keywords = ['password', 'secret', 'key', 'token']
        placeholder_values = ['your_password', 'your_secret', 'changeme', 'replace_me']
        
        # Check if it's a sensitive variable with placeholder value
        is_sensitive = any(keyword in var_name.lower() for keyword in sensitive_keywords)
        has_placeholder = var_value.lower() in placeholder_values or not var_value
        
        return is_sensitive and has_placeholder
    
    def _generate_secure_value(self, var_name: str) -> str:
        """Generate a secure value for a variable."""
        if 'jwt' in var_name.lower() or 'secret' in var_name.lower():
            # Generate longer secret for JWT and general secrets
            return secrets.token_urlsafe(32)
        elif 'password' in var_name.lower():
            # Generate complex password
            return ''.join(secrets.choice(self.password_chars) for _ in range(self.password_length))
        elif 'api_key' in var_name.lower() or 'key' in var_name.lower():
            # Generate API key format
            return f"sk-{''.join(secrets.choice(string.ascii_letters + string.digits) for _ in range(32))}"
        else:
            # Default secure random string
            return secrets.token_urlsafe(16)
    
    def _categorize_variables(self, variables: Dict[str, str]) -> Dict[str, Dict[str, str]]:
        """Categorize environment variables by type."""
        categories = {
            "general": {},
            "database": {},
            "authentication": {},
            "api_keys": {},
            "services": {},
            "development": {}
        }
        
        for var_name, var_value in variables.items():
            var_lower = var_name.lower()
            
            if any(keyword in var_lower for keyword in ['postgres', 'database', 'db_']):
                categories["database"][var_name] = var_value
            elif any(keyword in var_lower for keyword in ['jwt', 'auth', 'session']):
                categories["authentication"][var_name] = var_value
            elif any(keyword in var_lower for keyword in ['api_key', 'token', 'secret']):
                categories["api_keys"][var_name] = var_value
            elif any(keyword in var_lower for keyword in ['url', 'host', 'port', 'endpoint']):
                categories["services"][var_name] = var_value
            elif any(keyword in var_lower for keyword in ['debug', 'dev', 'environment', 'node_env']):
                categories["development"][var_name] = var_value
            else:
                categories["general"][var_name] = var_value
        
        # Remove empty categories
        return {k: v for k, v in categories.items() if v}
    
    def _is_sensitive_variable(self, var_name: str) -> bool:
        """Check if variable contains sensitive information."""
        sensitive_keywords = ['password', 'secret', 'key', 'token', 'private']
        return any(keyword in var_name.lower() for keyword in sensitive_keywords)
    
    def _get_variable_description(self, var_name: str) -> str:
        """Get description for environment variable."""
        descriptions = {
            "POSTGRES_PASSWORD": "Password for PostgreSQL database",
            "POSTGRES_DB": "Name of the PostgreSQL database",
            "POSTGRES_USER": "PostgreSQL database username",
            "JWT_SECRET": "Secret key for JWT token signing",
            "API_KEY": "API key for external service authentication",
            "NODE_ENV": "Node.js environment (development/production)",
            "SUPABASE_URL": "Supabase project URL",
            "SUPABASE_ANON_KEY": "Supabase anonymous key",
            "OPENAI_API_KEY": "OpenAI API key for LLM access",
            "PROJECT_NAME": "Name of the project",
            "ENVIRONMENT": "Deployment environment"
        }
        
        return descriptions.get(var_name, f"Configuration value for {var_name}")
    
    def _get_example_value(self, var_name: str) -> str:
        """Get example value for sensitive variables."""
        var_lower = var_name.lower()
        
        if 'password' in var_lower:
            return "your_secure_password_here"
        elif 'jwt' in var_lower or 'secret' in var_lower:
            return "your_jwt_secret_key_here"
        elif 'api_key' in var_lower:
            return "your_api_key_here"
        elif 'token' in var_lower:
            return "your_token_here"
        else:
            return "your_secure_value_here"
    
    def _should_generate_env_variants(self, config: ProjectConfig) -> bool:
        """Check if environment variants should be generated."""
        return config.custom_configuration.get('generate_env_variants', False)
    
    def _generate_environment_variants(self, project_path: Path, base_vars: Dict[str, str], result: GenerationResult):
        """Generate environment-specific configuration files."""
        environments = ['development', 'staging', 'production']
        
        for env in environments:
            env_vars = base_vars.copy()
            
            # Environment-specific modifications
            if env == 'development':
                env_vars.update({
                    "NODE_ENV": "development",
                    "DEBUG": "true",
                    "LOG_LEVEL": "debug"
                })
            elif env == 'staging':
                env_vars.update({
                    "NODE_ENV": "staging",
                    "DEBUG": "false",
                    "LOG_LEVEL": "info"
                })
            elif env == 'production':
                env_vars.update({
                    "NODE_ENV": "production",
                    "DEBUG": "false",
                    "LOG_LEVEL": "warn"
                })
            
            # Generate environment-specific file
            env_content = self._generate_environment_specific_content(env_vars, env)
            env_file = project_path / f".env.{env}"
            with open(env_file, 'w') as f:
                f.write(env_content)
            
            result.add_generated_file(
                path=f".env.{env}",
                generated_by="EnvironmentGenerator"
            )
    
    def _generate_environment_specific_content(self, variables: Dict[str, str], environment: str) -> str:
        """Generate content for environment-specific files."""
        lines = [
            f"# {environment.upper()} Environment Configuration",
            "# Generated by Project Template Orchestrator",
            f"#",
            f"# Environment-specific variables for {environment}",
            f"# Load this file in {environment} deployments",
            "",
        ]
        
        for var_name, var_value in variables.items():
            if self._is_sensitive_variable(var_name):
                lines.append(f"# SENSITIVE: Configure in deployment environment")
                lines.append(f"{var_name}=# SET_IN_DEPLOYMENT")
            else:
                lines.append(f"{var_name}={var_value}")
        
        return "\n".join(lines)
    
    def generate_gitignore_entries(self) -> List[str]:
        """Generate .gitignore entries for environment files."""
        return [
            "# Environment files",
            ".env",
            ".env.local",
            ".env.*.local",
            "",
            "# Keep example files",
            "!.env.example",
            "!.env.*.example"
        ]