"""
Conversation and message models for chat functionality.

This module provides models for managing conversations and messages
following the Single Responsibility Principle.
"""

from datetime import datetime
from enum import Enum
from typing import List, Optional
from uuid import UUID

from sqlalchemy import <PERSON>olean, DateTime, ForeignKey, String, Text, Integer
from sqlalchemy.dialects.postgresql import <PERSON><PERSON><PERSON>
from sqlalchemy.orm import Mapped, mapped_column, relationship

from .base import BaseModel, SoftDeleteModel


class MessageRole(str, Enum):
    """Message role enumeration."""
    USER = "user"
    ASSISTANT = "assistant"
    SYSTEM = "system"
    TOOL = "tool"


class ConversationStatus(str, Enum):
    """Conversation status enumeration."""
    ACTIVE = "active"
    ARCHIVED = "archived"
    DELETED = "deleted"


class Conversation(SoftDeleteModel):
    """
    Conversation model for chat sessions.
    
    Represents a conversation thread between a user and AI assistants.
    """
    
    # Basic information
    title: Mapped[str] = mapped_column(
        String(255),
        nullable=False,
        index=True
    )
    
    description: Mapped[Optional[str]] = mapped_column(
        Text,
        nullable=True
    )
    
    # User relationship
    user_id: Mapped[UUID] = mapped_column(
        ForeignKey("user.id", ondelete="CASCADE"),
        nullable=False,
        index=True
    )
    
    # Conversation settings
    status: Mapped[ConversationStatus] = mapped_column(
        default=ConversationStatus.ACTIVE,
        nullable=False,
        index=True
    )
    
    # AI model configuration
    model_name: Mapped[str] = mapped_column(
        String(100),
        default="gpt-4",
        nullable=False
    )
    
    model_provider: Mapped[str] = mapped_column(
        String(50),
        default="openai",
        nullable=False
    )
    
    # Configuration
    system_prompt: Mapped[Optional[str]] = mapped_column(
        Text,
        nullable=True
    )
    
    temperature: Mapped[float] = mapped_column(
        default=0.7,
        nullable=False
    )
    
    max_tokens: Mapped[Optional[int]] = mapped_column(
        nullable=True
    )
    
    # Metadata and settings
    metadata: Mapped[dict] = mapped_column(
        JSON,
        default=dict,
        nullable=False
    )
    
    # Conversation state
    message_count: Mapped[int] = mapped_column(
        default=0,
        nullable=False
    )
    
    last_message_at: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True),
        nullable=True
    )
    
    # Features
    rag_enabled: Mapped[bool] = mapped_column(
        Boolean,
        default=True,
        nullable=False
    )
    
    web_search_enabled: Mapped[bool] = mapped_column(
        Boolean,
        default=False,
        nullable=False
    )
    
    image_generation_enabled: Mapped[bool] = mapped_column(
        Boolean,
        default=False,
        nullable=False
    )
    
    # Relationships
    user: Mapped["User"] = relationship("User", back_populates="conversations")
    
    messages: Mapped[List["Message"]] = relationship(
        "Message",
        back_populates="conversation",
        cascade="all, delete-orphan",
        order_by="Message.created_at"
    )
    
    def update_message_count(self) -> None:
        """Update message count and last message timestamp."""
        self.message_count = len(self.messages)
        if self.messages:
            self.last_message_at = max(msg.created_at for msg in self.messages)
    
    def add_message(self, message: "Message") -> None:
        """Add a message to the conversation."""
        self.messages.append(message)
        self.update_message_count()
    
    def get_context_messages(self, limit: int = 20) -> List["Message"]:
        """Get recent messages for context."""
        return self.messages[-limit:] if self.messages else []
    
    def archive(self) -> None:
        """Archive the conversation."""
        self.status = ConversationStatus.ARCHIVED
    
    def activate(self) -> None:
        """Activate the conversation."""
        self.status = ConversationStatus.ACTIVE
    
    @property
    def is_active(self) -> bool:
        """Check if conversation is active."""
        return self.status == ConversationStatus.ACTIVE and not self.is_deleted
    
    def to_dict(self) -> dict:
        """Convert conversation to dictionary."""
        return {
            "id": str(self.id),
            "title": self.title,
            "description": self.description,
            "user_id": str(self.user_id),
            "status": self.status.value,
            "model_name": self.model_name,
            "model_provider": self.model_provider,
            "system_prompt": self.system_prompt,
            "temperature": self.temperature,
            "max_tokens": self.max_tokens,
            "metadata": self.metadata,
            "message_count": self.message_count,
            "last_message_at": self.last_message_at.isoformat() if self.last_message_at else None,
            "rag_enabled": self.rag_enabled,
            "web_search_enabled": self.web_search_enabled,
            "image_generation_enabled": self.image_generation_enabled,
            "created_at": self.created_at.isoformat(),
            "updated_at": self.updated_at.isoformat(),
        }


class Message(BaseModel):
    """
    Message model for individual chat messages.
    
    Represents a single message within a conversation.
    """
    
    # Conversation relationship
    conversation_id: Mapped[UUID] = mapped_column(
        ForeignKey("conversation.id", ondelete="CASCADE"),
        nullable=False,
        index=True
    )
    
    # Message content
    role: Mapped[MessageRole] = mapped_column(
        nullable=False,
        index=True
    )
    
    content: Mapped[str] = mapped_column(
        Text,
        nullable=False
    )
    
    # Message metadata
    metadata: Mapped[dict] = mapped_column(
        JSON,
        default=dict,
        nullable=False
    )
    
    # Processing information
    model_used: Mapped[Optional[str]] = mapped_column(
        String(100),
        nullable=True
    )
    
    processing_time_ms: Mapped[Optional[int]] = mapped_column(
        nullable=True
    )
    
    token_count: Mapped[Optional[int]] = mapped_column(
        nullable=True
    )
    
    # Tool usage
    tool_calls: Mapped[Optional[dict]] = mapped_column(
        JSON,
        nullable=True
    )
    
    tool_results: Mapped[Optional[dict]] = mapped_column(
        JSON,
        nullable=True
    )
    
    # Message state
    is_edited: Mapped[bool] = mapped_column(
        Boolean,
        default=False,
        nullable=False
    )
    
    edit_count: Mapped[int] = mapped_column(
        default=0,
        nullable=False
    )
    
    # Feedback
    user_rating: Mapped[Optional[int]] = mapped_column(
        nullable=True
    )
    
    user_feedback: Mapped[Optional[str]] = mapped_column(
        Text,
        nullable=True
    )
    
    # Relationships
    conversation: Mapped["Conversation"] = relationship(
        "Conversation",
        back_populates="messages"
    )
    
    def edit_content(self, new_content: str) -> None:
        """Edit message content."""
        self.content = new_content
        self.is_edited = True
        self.edit_count += 1
    
    def add_tool_call(self, tool_name: str, arguments: dict, result: dict) -> None:
        """Add tool call information."""
        if self.tool_calls is None:
            self.tool_calls = []
        if self.tool_results is None:
            self.tool_results = []
            
        self.tool_calls.append({
            "name": tool_name,
            "arguments": arguments
        })
        
        self.tool_results.append({
            "tool_name": tool_name,
            "result": result
        })
    
    def set_user_feedback(self, rating: int, feedback: Optional[str] = None) -> None:
        """Set user feedback for the message."""
        self.user_rating = rating
        self.user_feedback = feedback
    
    @property
    def word_count(self) -> int:
        """Get approximate word count."""
        return len(self.content.split())
    
    def to_dict(self) -> dict:
        """Convert message to dictionary."""
        return {
            "id": str(self.id),
            "conversation_id": str(self.conversation_id),
            "role": self.role.value,
            "content": self.content,
            "metadata": self.metadata,
            "model_used": self.model_used,
            "processing_time_ms": self.processing_time_ms,
            "token_count": self.token_count,
            "tool_calls": self.tool_calls,
            "tool_results": self.tool_results,
            "is_edited": self.is_edited,
            "edit_count": self.edit_count,
            "user_rating": self.user_rating,
            "user_feedback": self.user_feedback,
            "word_count": self.word_count,
            "created_at": self.created_at.isoformat(),
            "updated_at": self.updated_at.isoformat(),
        }